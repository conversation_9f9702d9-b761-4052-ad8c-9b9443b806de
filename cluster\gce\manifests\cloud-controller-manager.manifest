{
"apiVersion": "v1",
"kind": "Pod",
"metadata": {
  "name":"cloud-controller-manager",
  "namespace": "kube-system",
  "labels": {
    "tier": "control-plane",
    "component": "cloud-controller-manager"
  }
},
"spec":{
"securityContext": {
  {{runAsUser}}
  {{runAsGroup}}
  {{supplementalGroups}}
  "seccompProfile": {
      "type": "RuntimeDefault"
  }
},
"priorityClassName": "system-node-critical",
"hostNetwork": true,
"containers":[
    {
    "name": "cloud-controller-manager",
    "image": "gcr.io/k8s-staging-cloud-provider-gcp/cloud-controller-manager:v30.0.0",
    "resources": {
      "requests": {
        "cpu": "{{cpurequest}}"
      }
    },
    "args": [
      "--log-file=/var/log/cloud-controller-manager.log",
      "--also-stdout=false",
      "--redirect-stderr=true",
      "/cloud-controller-manager",
      {{params}}
    ],
    {{container_env}}
    "livenessProbe": {
      "httpGet": {
        "host": "127.0.0.1",
        "port": 10258,
        "scheme": "HTTPS",
        "path": "/healthz"
      },
      "initialDelaySeconds": 15,
      "timeoutSeconds": 15
    },
    "volumeMounts": [
        {{cloud_config_mount}}
        {{additional_cloud_config_mount}}
        {{pv_recycler_mount}}
        { "name": "srvkube",
        "mountPath": "/etc/srv/kubernetes",
        "readOnly": true},
        {{flexvolume_hostpath_mount}}
        { "name": "logfile",
        "mountPath": "/var/log/cloud-controller-manager.log",
        "readOnly": false},
        { "name": "etcssl",
        "mountPath": "/etc/ssl",
        "readOnly": true},
        { "name": "usrsharecacerts",
        "mountPath": "/usr/share/ca-certificates",
        "readOnly": true},
        { "name": "varssl",
        "mountPath": "/var/ssl",
        "readOnly": true},
        { "name": "etcopenssl",
        "mountPath": "/etc/openssl",
        "readOnly": true},
        { "name": "etcpki",
        "mountPath": "/etc/pki",
        "readOnly": true}
      ]
    },
    {
    "name": "cloud-pvl-admission",
    "image": "gcr.io/k8s-staging-cloud-pv-labeler/cloud-pv-admission-labeler:v0.3.0",
    "resources": {
      "requests": {
        "cpu": "10m"
      }
    },
    "command": [
      "/cloud-pv-admission-labeler",
      "--addr=localhost:9001",
      "--tls-cert-path=/etc/srv/kubernetes/pki/cloud-pvl-admission/server.crt",
      "--tls-key-path=/etc/srv/kubernetes/pki/cloud-pvl-admission/server.key",
      "--cloud-provider=gce",
      "--cloud-config=/etc/gce.conf"
    ],
    "volumeMounts": [
        {{cloud_config_mount}}
        {{additional_cloud_config_mount}}
        {{pv_recycler_mount}}
        { "name": "srvkube",
        "mountPath": "/etc/srv/kubernetes",
        "readOnly": true},
        {{flexvolume_hostpath_mount}}
        { "name": "logfile",
        "mountPath": "/var/log/cloud-pvl-admission.log",
        "readOnly": false},
        { "name": "etcssl",
        "mountPath": "/etc/ssl",
        "readOnly": true},
        { "name": "usrsharecacerts",
        "mountPath": "/usr/share/ca-certificates",
        "readOnly": true},
        { "name": "varssl",
        "mountPath": "/var/ssl",
        "readOnly": true},
        { "name": "etcopenssl",
        "mountPath": "/etc/openssl",
        "readOnly": true},
        { "name": "etcpki",
        "mountPath": "/etc/pki",
        "readOnly": true}
      ]
    }
],
"volumes":[
  {{cloud_config_volume}}
  {{additional_cloud_config_volume}}
  {{pv_recycler_volume}}
  { "name": "srvkube",
    "hostPath": {
        "path": "/etc/srv/kubernetes"}
  },
  {{flexvolume_hostpath}}
  { "name": "logfile",
    "hostPath": {
        "path": "/var/log/cloud-controller-manager.log",
        "type": "FileOrCreate"}
  },
  { "name": "etcssl",
    "hostPath": {
        "path": "/etc/ssl"}
  },
  { "name": "usrsharecacerts",
    "hostPath": {
        "path": "/usr/share/ca-certificates"}
  },
  { "name": "varssl",
    "hostPath": {
        "path": "/var/ssl"}
  },
  { "name": "etcopenssl",
    "hostPath": {
        "path": "/etc/openssl"}
  },
  { "name": "etcpki",
    "hostPath": {
        "path": "/etc/pki"}
  }
]
}}
