# hack/logcheck.conf contains regular expressions that are matched against <pkg>/<file>,
# for example k8s.io/cmd/kube-scheduler/app/config/config.go.
#
# By default, structured logging call parameters are checked, but usage of
# those calls is not required. That is changed on a per-file basis.
#
# Remember to clean the golangci-lint cache when changing the configuration and
# running the verify-golangci-lint.sh script multiple times, otherwise
# golangci-lint will report stale results:
#    _output/local/bin/golangci-lint cache clean

# At this point we don't enforce the usage structured logging calls except in
# those packages that were migrated. This disables the check for other files.
-structured .*

# Now enable it again for migrated packages.
structured k8s.io/kubernetes/pkg/kubelet/.*
structured k8s.io/kubernetes/pkg/proxy/.*
structured k8s.io/kms/.*
structured k8s.io/apiserver/pkg/storage/value/.*
structured k8s.io/apiserver/pkg/server/options/encryptionconfig/.*

# The following packages have been migrated to contextual logging.
# Packages matched here do not have to be listed above because
# "contextual" implies "structured".
contextual k8s.io/api/.*
contextual k8s.io/apimachinery/pkg/util/runtime/.*
contextual k8s.io/client-go/metadata/.*
contextual k8s.io/client-go/rest/.*
contextual k8s.io/client-go/tools/cache/.*
contextual k8s.io/client-go/tools/events/.*
contextual k8s.io/client-go/tools/record/.*
contextual k8s.io/component-helpers/.*
contextual k8s.io/cri-api/.*
contextual k8s.io/cri-client/.*
contextual k8s.io/csi-translation-lib/.*
contextual k8s.io/dynamic-resource-allocation/.*
contextual k8s.io/endpointslice/.*
contextual k8s.io/kms/.*
contextual k8s.io/kube-controller-manager/.*
contextual k8s.io/kube-proxy/.*
contextual k8s.io/kube-scheduler/.*
contextual k8s.io/sample-apiserver/.*
contextual k8s.io/sample-cli-plugin/.*
contextual k8s.io/sample-controller/.*
contextual k8s.io/kubernetes/cmd/kube-proxy/.*
contextual k8s.io/kubernetes/cmd/kube-scheduler/.*
contextual k8s.io/kubernetes/cmd/kubelet/.*
contextual k8s.io/kubernetes/pkg/controller/.*
contextual k8s.io/kubernetes/pkg/scheduler/.*
contextual k8s.io/kubernetes/test/e2e/dra/.*
contextual k8s.io/kubernetes/pkg/kubelet/cm/dra/.*
contextual k8s.io/kubernetes/pkg/kubelet/cm/memorymanager/.*
contextual k8s.io/kubernetes/pkg/kubelet/lifecycle/.*
contextual k8s.io/kubernetes/pkg/kubelet/pleg/.*
contextual k8s.io/kubernetes/pkg/kubelet/clustertrustbundle/.*
contextual k8s.io/kubernetes/pkg/kubelet/token/.*
contextual k8s.io/kubernetes/pkg/kubelet/cadvisor/.*
contextual k8s.io/kubernetes/pkg/kubelet/oom/.*
contextual k8s.io/kubernetes/pkg/kubelet/status/.*
contextual k8s.io/kubernetes/pkg/kubelet/sysctl/.*
contextual k8s.io/kubernetes/pkg/kubelet/apis/.*
contextual k8s.io/kubernetes/pkg/kubelet/kubeletconfig/.*
contextual k8s.io/kubernetes/pkg/kubelet/kuberuntime/.*
contextual k8s.io/kubernetes/pkg/kubelet/nodeshutdown/.*
contextual k8s.io/kubernetes/pkg/kubelet/pod/.*
contextual k8s.io/kubernetes/pkg/kubelet/preemption/.*
contextual k8s.io/kubernetes/pkg/kubelet/volumemanager/.*

# As long as contextual logging is alpha or beta, all WithName, WithValues,
# NewContext calls have to go through klog. Once it is GA, we can lift
# this restriction. Whether we then do a global search/replace remains
# to be decided.
with-helpers .*
