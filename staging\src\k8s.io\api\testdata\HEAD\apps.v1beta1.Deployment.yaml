apiVersion: apps/v1beta1
kind: Deployment
metadata:
  annotations:
    annotationsKey: annotationsValue
  creationTimestamp: "2008-01-01T01:01:01Z"
  deletionGracePeriodSeconds: 10
  deletionTimestamp: "2009-01-01T01:01:01Z"
  finalizers:
  - finalizersValue
  generateName: generateNameValue
  generation: 7
  labels:
    labelsKey: labelsValue
  managedFields:
  - apiVersion: apiVersionValue
    fieldsType: fieldsTypeValue
    fieldsV1: {}
    manager: managerValue
    operation: operationValue
    subresource: subresourceValue
    time: "2004-01-01T01:01:01Z"
  name: nameValue
  namespace: namespaceValue
  ownerReferences:
  - apiVersion: apiVersionValue
    blockOwnerDeletion: true
    controller: true
    kind: kindValue
    name: nameValue
    uid: uidValue
  resourceVersion: resourceVersionValue
  selfLink: selfLinkValue
  uid: uidValue
spec:
  minReadySeconds: 5
  paused: true
  progressDeadlineSeconds: 9
  replicas: 1
  revisionHistoryLimit: 6
  rollbackTo:
    revision: 1
  selector:
    matchExpressions:
    - key: keyValue
      operator: operatorValue
      values:
      - valuesValue
    matchLabels:
      matchLabelsKey: matchLabelsValue
  strategy:
    rollingUpdate:
      maxSurge: maxSurgeValue
      maxUnavailable: maxUnavailableValue
    type: typeValue
  template:
    metadata:
      annotations:
        annotationsKey: annotationsValue
      creationTimestamp: "2008-01-01T01:01:01Z"
      deletionGracePeriodSeconds: 10
      deletionTimestamp: "2009-01-01T01:01:01Z"
      finalizers:
      - finalizersValue
      generateName: generateNameValue
      generation: 7
      labels:
        labelsKey: labelsValue
      managedFields:
      - apiVersion: apiVersionValue
        fieldsType: fieldsTypeValue
        fieldsV1: {}
        manager: managerValue
        operation: operationValue
        subresource: subresourceValue
        time: "2004-01-01T01:01:01Z"
      name: nameValue
      namespace: namespaceValue
      ownerReferences:
      - apiVersion: apiVersionValue
        blockOwnerDeletion: true
        controller: true
        kind: kindValue
        name: nameValue
        uid: uidValue
      resourceVersion: resourceVersionValue
      selfLink: selfLinkValue
      uid: uidValue
    spec:
      activeDeadlineSeconds: 5
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
              matchFields:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
            weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
              matchFields:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
        podAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: keyValue
                  operator: operatorValue
                  values:
                  - valuesValue
                matchLabels:
                  matchLabelsKey: matchLabelsValue
              matchLabelKeys:
              - matchLabelKeysValue
              mismatchLabelKeys:
              - mismatchLabelKeysValue
              namespaceSelector:
                matchExpressions:
                - key: keyValue
                  operator: operatorValue
                  values:
                  - valuesValue
                matchLabels:
                  matchLabelsKey: matchLabelsValue
              namespaces:
              - namespacesValue
              topologyKey: topologyKeyValue
            weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
              matchLabels:
                matchLabelsKey: matchLabelsValue
            matchLabelKeys:
            - matchLabelKeysValue
            mismatchLabelKeys:
            - mismatchLabelKeysValue
            namespaceSelector:
              matchExpressions:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
              matchLabels:
                matchLabelsKey: matchLabelsValue
            namespaces:
            - namespacesValue
            topologyKey: topologyKeyValue
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: keyValue
                  operator: operatorValue
                  values:
                  - valuesValue
                matchLabels:
                  matchLabelsKey: matchLabelsValue
              matchLabelKeys:
              - matchLabelKeysValue
              mismatchLabelKeys:
              - mismatchLabelKeysValue
              namespaceSelector:
                matchExpressions:
                - key: keyValue
                  operator: operatorValue
                  values:
                  - valuesValue
                matchLabels:
                  matchLabelsKey: matchLabelsValue
              namespaces:
              - namespacesValue
              topologyKey: topologyKeyValue
            weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
              matchLabels:
                matchLabelsKey: matchLabelsValue
            matchLabelKeys:
            - matchLabelKeysValue
            mismatchLabelKeys:
            - mismatchLabelKeysValue
            namespaceSelector:
              matchExpressions:
              - key: keyValue
                operator: operatorValue
                values:
                - valuesValue
              matchLabels:
                matchLabelsKey: matchLabelsValue
            namespaces:
            - namespacesValue
            topologyKey: topologyKeyValue
      automountServiceAccountToken: true
      containers:
      - args:
        - argsValue
        command:
        - commandValue
        env:
        - name: nameValue
          value: valueValue
          valueFrom:
            configMapKeyRef:
              key: keyValue
              name: nameValue
              optional: true
            fieldRef:
              apiVersion: apiVersionValue
              fieldPath: fieldPathValue
            fileKeyRef:
              key: keyValue
              optional: true
              path: pathValue
              volumeName: volumeNameValue
            resourceFieldRef:
              containerName: containerNameValue
              divisor: "0"
              resource: resourceValue
            secretKeyRef:
              key: keyValue
              name: nameValue
              optional: true
        envFrom:
        - configMapRef:
            name: nameValue
            optional: true
          prefix: prefixValue
          secretRef:
            name: nameValue
            optional: true
        image: imageValue
        imagePullPolicy: imagePullPolicyValue
        lifecycle:
          postStart:
            exec:
              command:
              - commandValue
            httpGet:
              host: hostValue
              httpHeaders:
              - name: nameValue
                value: valueValue
              path: pathValue
              port: portValue
              scheme: schemeValue
            sleep:
              seconds: 1
            tcpSocket:
              host: hostValue
              port: portValue
          preStop:
            exec:
              command:
              - commandValue
            httpGet:
              host: hostValue
              httpHeaders:
              - name: nameValue
                value: valueValue
              path: pathValue
              port: portValue
              scheme: schemeValue
            sleep:
              seconds: 1
            tcpSocket:
              host: hostValue
              port: portValue
          stopSignal: stopSignalValue
        livenessProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        name: nameValue
        ports:
        - containerPort: 3
          hostIP: hostIPValue
          hostPort: 2
          name: nameValue
          protocol: protocolValue
        readinessProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        resizePolicy:
        - resourceName: resourceNameValue
          restartPolicy: restartPolicyValue
        resources:
          claims:
          - name: nameValue
            request: requestValue
          limits:
            limitsKey: "0"
          requests:
            requestsKey: "0"
        restartPolicy: restartPolicyValue
        securityContext:
          allowPrivilegeEscalation: true
          appArmorProfile:
            localhostProfile: localhostProfileValue
            type: typeValue
          capabilities:
            add:
            - addValue
            drop:
            - dropValue
          privileged: true
          procMount: procMountValue
          readOnlyRootFilesystem: true
          runAsGroup: 8
          runAsNonRoot: true
          runAsUser: 4
          seLinuxOptions:
            level: levelValue
            role: roleValue
            type: typeValue
            user: userValue
          seccompProfile:
            localhostProfile: localhostProfileValue
            type: typeValue
          windowsOptions:
            gmsaCredentialSpec: gmsaCredentialSpecValue
            gmsaCredentialSpecName: gmsaCredentialSpecNameValue
            hostProcess: true
            runAsUserName: runAsUserNameValue
        startupProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        stdin: true
        stdinOnce: true
        terminationMessagePath: terminationMessagePathValue
        terminationMessagePolicy: terminationMessagePolicyValue
        tty: true
        volumeDevices:
        - devicePath: devicePathValue
          name: nameValue
        volumeMounts:
        - mountPath: mountPathValue
          mountPropagation: mountPropagationValue
          name: nameValue
          readOnly: true
          recursiveReadOnly: recursiveReadOnlyValue
          subPath: subPathValue
          subPathExpr: subPathExprValue
        workingDir: workingDirValue
      dnsConfig:
        nameservers:
        - nameserversValue
        options:
        - name: nameValue
          value: valueValue
        searches:
        - searchesValue
      dnsPolicy: dnsPolicyValue
      enableServiceLinks: true
      ephemeralContainers:
      - args:
        - argsValue
        command:
        - commandValue
        env:
        - name: nameValue
          value: valueValue
          valueFrom:
            configMapKeyRef:
              key: keyValue
              name: nameValue
              optional: true
            fieldRef:
              apiVersion: apiVersionValue
              fieldPath: fieldPathValue
            fileKeyRef:
              key: keyValue
              optional: true
              path: pathValue
              volumeName: volumeNameValue
            resourceFieldRef:
              containerName: containerNameValue
              divisor: "0"
              resource: resourceValue
            secretKeyRef:
              key: keyValue
              name: nameValue
              optional: true
        envFrom:
        - configMapRef:
            name: nameValue
            optional: true
          prefix: prefixValue
          secretRef:
            name: nameValue
            optional: true
        image: imageValue
        imagePullPolicy: imagePullPolicyValue
        lifecycle:
          postStart:
            exec:
              command:
              - commandValue
            httpGet:
              host: hostValue
              httpHeaders:
              - name: nameValue
                value: valueValue
              path: pathValue
              port: portValue
              scheme: schemeValue
            sleep:
              seconds: 1
            tcpSocket:
              host: hostValue
              port: portValue
          preStop:
            exec:
              command:
              - commandValue
            httpGet:
              host: hostValue
              httpHeaders:
              - name: nameValue
                value: valueValue
              path: pathValue
              port: portValue
              scheme: schemeValue
            sleep:
              seconds: 1
            tcpSocket:
              host: hostValue
              port: portValue
          stopSignal: stopSignalValue
        livenessProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        name: nameValue
        ports:
        - containerPort: 3
          hostIP: hostIPValue
          hostPort: 2
          name: nameValue
          protocol: protocolValue
        readinessProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        resizePolicy:
        - resourceName: resourceNameValue
          restartPolicy: restartPolicyValue
        resources:
          claims:
          - name: nameValue
            request: requestValue
          limits:
            limitsKey: "0"
          requests:
            requestsKey: "0"
        restartPolicy: restartPolicyValue
        securityContext:
          allowPrivilegeEscalation: true
          appArmorProfile:
            localhostProfile: localhostProfileValue
            type: typeValue
          capabilities:
            add:
            - addValue
            drop:
            - dropValue
          privileged: true
          procMount: procMountValue
          readOnlyRootFilesystem: true
          runAsGroup: 8
          runAsNonRoot: true
          runAsUser: 4
          seLinuxOptions:
            level: levelValue
            role: roleValue
            type: typeValue
            user: userValue
          seccompProfile:
            localhostProfile: localhostProfileValue
            type: typeValue
          windowsOptions:
            gmsaCredentialSpec: gmsaCredentialSpecValue
            gmsaCredentialSpecName: gmsaCredentialSpecNameValue
            hostProcess: true
            runAsUserName: runAsUserNameValue
        startupProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        stdin: true
        stdinOnce: true
        targetContainerName: targetContainerNameValue
        terminationMessagePath: terminationMessagePathValue
        terminationMessagePolicy: terminationMessagePolicyValue
        tty: true
        volumeDevices:
        - devicePath: devicePathValue
          name: nameValue
        volumeMounts:
        - mountPath: mountPathValue
          mountPropagation: mountPropagationValue
          name: nameValue
          readOnly: true
          recursiveReadOnly: recursiveReadOnlyValue
          subPath: subPathValue
          subPathExpr: subPathExprValue
        workingDir: workingDirValue
      hostAliases:
      - hostnames:
        - hostnamesValue
        ip: ipValue
      hostIPC: true
      hostNetwork: true
      hostPID: true
      hostUsers: true
      hostname: hostnameValue
      hostnameOverride: hostnameOverrideValue
      imagePullSecrets:
      - name: nameValue
      initContainers:
      - args:
        - argsValue
        command:
        - commandValue
        env:
        - name: nameValue
          value: valueValue
          valueFrom:
            configMapKeyRef:
              key: keyValue
              name: nameValue
              optional: true
            fieldRef:
              apiVersion: apiVersionValue
              fieldPath: fieldPathValue
            fileKeyRef:
              key: keyValue
              optional: true
              path: pathValue
              volumeName: volumeNameValue
            resourceFieldRef:
              containerName: containerNameValue
              divisor: "0"
              resource: resourceValue
            secretKeyRef:
              key: keyValue
              name: nameValue
              optional: true
        envFrom:
        - configMapRef:
            name: nameValue
            optional: true
          prefix: prefixValue
          secretRef:
            name: nameValue
            optional: true
        image: imageValue
        imagePullPolicy: imagePullPolicyValue
        lifecycle:
          postStart:
            exec:
              command:
              - commandValue
            httpGet:
              host: hostValue
              httpHeaders:
              - name: nameValue
                value: valueValue
              path: pathValue
              port: portValue
              scheme: schemeValue
            sleep:
              seconds: 1
            tcpSocket:
              host: hostValue
              port: portValue
          preStop:
            exec:
              command:
              - commandValue
            httpGet:
              host: hostValue
              httpHeaders:
              - name: nameValue
                value: valueValue
              path: pathValue
              port: portValue
              scheme: schemeValue
            sleep:
              seconds: 1
            tcpSocket:
              host: hostValue
              port: portValue
          stopSignal: stopSignalValue
        livenessProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        name: nameValue
        ports:
        - containerPort: 3
          hostIP: hostIPValue
          hostPort: 2
          name: nameValue
          protocol: protocolValue
        readinessProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        resizePolicy:
        - resourceName: resourceNameValue
          restartPolicy: restartPolicyValue
        resources:
          claims:
          - name: nameValue
            request: requestValue
          limits:
            limitsKey: "0"
          requests:
            requestsKey: "0"
        restartPolicy: restartPolicyValue
        securityContext:
          allowPrivilegeEscalation: true
          appArmorProfile:
            localhostProfile: localhostProfileValue
            type: typeValue
          capabilities:
            add:
            - addValue
            drop:
            - dropValue
          privileged: true
          procMount: procMountValue
          readOnlyRootFilesystem: true
          runAsGroup: 8
          runAsNonRoot: true
          runAsUser: 4
          seLinuxOptions:
            level: levelValue
            role: roleValue
            type: typeValue
            user: userValue
          seccompProfile:
            localhostProfile: localhostProfileValue
            type: typeValue
          windowsOptions:
            gmsaCredentialSpec: gmsaCredentialSpecValue
            gmsaCredentialSpecName: gmsaCredentialSpecNameValue
            hostProcess: true
            runAsUserName: runAsUserNameValue
        startupProbe:
          exec:
            command:
            - commandValue
          failureThreshold: 6
          grpc:
            port: 1
            service: serviceValue
          httpGet:
            host: hostValue
            httpHeaders:
            - name: nameValue
              value: valueValue
            path: pathValue
            port: portValue
            scheme: schemeValue
          initialDelaySeconds: 2
          periodSeconds: 4
          successThreshold: 5
          tcpSocket:
            host: hostValue
            port: portValue
          terminationGracePeriodSeconds: 7
          timeoutSeconds: 3
        stdin: true
        stdinOnce: true
        terminationMessagePath: terminationMessagePathValue
        terminationMessagePolicy: terminationMessagePolicyValue
        tty: true
        volumeDevices:
        - devicePath: devicePathValue
          name: nameValue
        volumeMounts:
        - mountPath: mountPathValue
          mountPropagation: mountPropagationValue
          name: nameValue
          readOnly: true
          recursiveReadOnly: recursiveReadOnlyValue
          subPath: subPathValue
          subPathExpr: subPathExprValue
        workingDir: workingDirValue
      nodeName: nodeNameValue
      nodeSelector:
        nodeSelectorKey: nodeSelectorValue
      os:
        name: nameValue
      overhead:
        overheadKey: "0"
      preemptionPolicy: preemptionPolicyValue
      priority: 25
      priorityClassName: priorityClassNameValue
      readinessGates:
      - conditionType: conditionTypeValue
      resourceClaims:
      - name: nameValue
        resourceClaimName: resourceClaimNameValue
        resourceClaimTemplateName: resourceClaimTemplateNameValue
      resources:
        claims:
        - name: nameValue
          request: requestValue
        limits:
          limitsKey: "0"
        requests:
          requestsKey: "0"
      restartPolicy: restartPolicyValue
      runtimeClassName: runtimeClassNameValue
      schedulerName: schedulerNameValue
      schedulingGates:
      - name: nameValue
      securityContext:
        appArmorProfile:
          localhostProfile: localhostProfileValue
          type: typeValue
        fsGroup: 5
        fsGroupChangePolicy: fsGroupChangePolicyValue
        runAsGroup: 6
        runAsNonRoot: true
        runAsUser: 2
        seLinuxChangePolicy: seLinuxChangePolicyValue
        seLinuxOptions:
          level: levelValue
          role: roleValue
          type: typeValue
          user: userValue
        seccompProfile:
          localhostProfile: localhostProfileValue
          type: typeValue
        supplementalGroups:
        - 4
        supplementalGroupsPolicy: supplementalGroupsPolicyValue
        sysctls:
        - name: nameValue
          value: valueValue
        windowsOptions:
          gmsaCredentialSpec: gmsaCredentialSpecValue
          gmsaCredentialSpecName: gmsaCredentialSpecNameValue
          hostProcess: true
          runAsUserName: runAsUserNameValue
      serviceAccount: serviceAccountValue
      serviceAccountName: serviceAccountNameValue
      setHostnameAsFQDN: true
      shareProcessNamespace: true
      subdomain: subdomainValue
      terminationGracePeriodSeconds: 4
      tolerations:
      - effect: effectValue
        key: keyValue
        operator: operatorValue
        tolerationSeconds: 5
        value: valueValue
      topologySpreadConstraints:
      - labelSelector:
          matchExpressions:
          - key: keyValue
            operator: operatorValue
            values:
            - valuesValue
          matchLabels:
            matchLabelsKey: matchLabelsValue
        matchLabelKeys:
        - matchLabelKeysValue
        maxSkew: 1
        minDomains: 5
        nodeAffinityPolicy: nodeAffinityPolicyValue
        nodeTaintsPolicy: nodeTaintsPolicyValue
        topologyKey: topologyKeyValue
        whenUnsatisfiable: whenUnsatisfiableValue
      volumes:
      - awsElasticBlockStore:
          fsType: fsTypeValue
          partition: 3
          readOnly: true
          volumeID: volumeIDValue
        azureDisk:
          cachingMode: cachingModeValue
          diskName: diskNameValue
          diskURI: diskURIValue
          fsType: fsTypeValue
          kind: kindValue
          readOnly: true
        azureFile:
          readOnly: true
          secretName: secretNameValue
          shareName: shareNameValue
        cephfs:
          monitors:
          - monitorsValue
          path: pathValue
          readOnly: true
          secretFile: secretFileValue
          secretRef:
            name: nameValue
          user: userValue
        cinder:
          fsType: fsTypeValue
          readOnly: true
          secretRef:
            name: nameValue
          volumeID: volumeIDValue
        configMap:
          defaultMode: 3
          items:
          - key: keyValue
            mode: 3
            path: pathValue
          name: nameValue
          optional: true
        csi:
          driver: driverValue
          fsType: fsTypeValue
          nodePublishSecretRef:
            name: nameValue
          readOnly: true
          volumeAttributes:
            volumeAttributesKey: volumeAttributesValue
        downwardAPI:
          defaultMode: 2
          items:
          - fieldRef:
              apiVersion: apiVersionValue
              fieldPath: fieldPathValue
            mode: 4
            path: pathValue
            resourceFieldRef:
              containerName: containerNameValue
              divisor: "0"
              resource: resourceValue
        emptyDir:
          medium: mediumValue
          sizeLimit: "0"
        ephemeral:
          volumeClaimTemplate:
            metadata:
              annotations:
                annotationsKey: annotationsValue
              creationTimestamp: "2008-01-01T01:01:01Z"
              deletionGracePeriodSeconds: 10
              deletionTimestamp: "2009-01-01T01:01:01Z"
              finalizers:
              - finalizersValue
              generateName: generateNameValue
              generation: 7
              labels:
                labelsKey: labelsValue
              managedFields:
              - apiVersion: apiVersionValue
                fieldsType: fieldsTypeValue
                fieldsV1: {}
                manager: managerValue
                operation: operationValue
                subresource: subresourceValue
                time: "2004-01-01T01:01:01Z"
              name: nameValue
              namespace: namespaceValue
              ownerReferences:
              - apiVersion: apiVersionValue
                blockOwnerDeletion: true
                controller: true
                kind: kindValue
                name: nameValue
                uid: uidValue
              resourceVersion: resourceVersionValue
              selfLink: selfLinkValue
              uid: uidValue
            spec:
              accessModes:
              - accessModesValue
              dataSource:
                apiGroup: apiGroupValue
                kind: kindValue
                name: nameValue
              dataSourceRef:
                apiGroup: apiGroupValue
                kind: kindValue
                name: nameValue
                namespace: namespaceValue
              resources:
                limits:
                  limitsKey: "0"
                requests:
                  requestsKey: "0"
              selector:
                matchExpressions:
                - key: keyValue
                  operator: operatorValue
                  values:
                  - valuesValue
                matchLabels:
                  matchLabelsKey: matchLabelsValue
              storageClassName: storageClassNameValue
              volumeAttributesClassName: volumeAttributesClassNameValue
              volumeMode: volumeModeValue
              volumeName: volumeNameValue
        fc:
          fsType: fsTypeValue
          lun: 2
          readOnly: true
          targetWWNs:
          - targetWWNsValue
          wwids:
          - wwidsValue
        flexVolume:
          driver: driverValue
          fsType: fsTypeValue
          options:
            optionsKey: optionsValue
          readOnly: true
          secretRef:
            name: nameValue
        flocker:
          datasetName: datasetNameValue
          datasetUUID: datasetUUIDValue
        gcePersistentDisk:
          fsType: fsTypeValue
          partition: 3
          pdName: pdNameValue
          readOnly: true
        gitRepo:
          directory: directoryValue
          repository: repositoryValue
          revision: revisionValue
        glusterfs:
          endpoints: endpointsValue
          path: pathValue
          readOnly: true
        hostPath:
          path: pathValue
          type: typeValue
        image:
          pullPolicy: pullPolicyValue
          reference: referenceValue
        iscsi:
          chapAuthDiscovery: true
          chapAuthSession: true
          fsType: fsTypeValue
          initiatorName: initiatorNameValue
          iqn: iqnValue
          iscsiInterface: iscsiInterfaceValue
          lun: 3
          portals:
          - portalsValue
          readOnly: true
          secretRef:
            name: nameValue
          targetPortal: targetPortalValue
        name: nameValue
        nfs:
          path: pathValue
          readOnly: true
          server: serverValue
        persistentVolumeClaim:
          claimName: claimNameValue
          readOnly: true
        photonPersistentDisk:
          fsType: fsTypeValue
          pdID: pdIDValue
        portworxVolume:
          fsType: fsTypeValue
          readOnly: true
          volumeID: volumeIDValue
        projected:
          defaultMode: 2
          sources:
          - clusterTrustBundle:
              labelSelector:
                matchExpressions:
                - key: keyValue
                  operator: operatorValue
                  values:
                  - valuesValue
                matchLabels:
                  matchLabelsKey: matchLabelsValue
              name: nameValue
              optional: true
              path: pathValue
              signerName: signerNameValue
            configMap:
              items:
              - key: keyValue
                mode: 3
                path: pathValue
              name: nameValue
              optional: true
            downwardAPI:
              items:
              - fieldRef:
                  apiVersion: apiVersionValue
                  fieldPath: fieldPathValue
                mode: 4
                path: pathValue
                resourceFieldRef:
                  containerName: containerNameValue
                  divisor: "0"
                  resource: resourceValue
            podCertificate:
              certificateChainPath: certificateChainPathValue
              credentialBundlePath: credentialBundlePathValue
              keyPath: keyPathValue
              keyType: keyTypeValue
              maxExpirationSeconds: 3
              signerName: signerNameValue
            secret:
              items:
              - key: keyValue
                mode: 3
                path: pathValue
              name: nameValue
              optional: true
            serviceAccountToken:
              audience: audienceValue
              expirationSeconds: 2
              path: pathValue
        quobyte:
          group: groupValue
          readOnly: true
          registry: registryValue
          tenant: tenantValue
          user: userValue
          volume: volumeValue
        rbd:
          fsType: fsTypeValue
          image: imageValue
          keyring: keyringValue
          monitors:
          - monitorsValue
          pool: poolValue
          readOnly: true
          secretRef:
            name: nameValue
          user: userValue
        scaleIO:
          fsType: fsTypeValue
          gateway: gatewayValue
          protectionDomain: protectionDomainValue
          readOnly: true
          secretRef:
            name: nameValue
          sslEnabled: true
          storageMode: storageModeValue
          storagePool: storagePoolValue
          system: systemValue
          volumeName: volumeNameValue
        secret:
          defaultMode: 3
          items:
          - key: keyValue
            mode: 3
            path: pathValue
          optional: true
          secretName: secretNameValue
        storageos:
          fsType: fsTypeValue
          readOnly: true
          secretRef:
            name: nameValue
          volumeName: volumeNameValue
          volumeNamespace: volumeNamespaceValue
        vsphereVolume:
          fsType: fsTypeValue
          storagePolicyID: storagePolicyIDValue
          storagePolicyName: storagePolicyNameValue
          volumePath: volumePathValue
status:
  availableReplicas: 4
  collisionCount: 8
  conditions:
  - lastTransitionTime: "2007-01-01T01:01:01Z"
    lastUpdateTime: "2006-01-01T01:01:01Z"
    message: messageValue
    reason: reasonValue
    status: statusValue
    type: typeValue
  observedGeneration: 1
  readyReplicas: 7
  replicas: 2
  terminatingReplicas: 9
  unavailableReplicas: 5
  updatedReplicas: 3
