apiVersion: v1
items:
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:attachdetach-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    - persistentvolumes
    verbs:
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - nodes/status
    verbs:
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
  - apiGroups:
    - storage.k8s.io
    resources:
    - volumeattachments
    verbs:
    - create
    - delete
    - get
    - list
    - watch
  - apiGroups:
    - storage.k8s.io
    resources:
    - csidrivers
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - storage.k8s.io
    resources:
    - csinodes
    verbs:
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:certificate-controller
  rules:
  - apiGroups:
    - certificates.k8s.io
    resources:
    - certificatesigningrequests
    verbs:
    - delete
    - get
    - list
    - watch
  - apiGroups:
    - certificates.k8s.io
    resources:
    - certificatesigningrequests/approval
    - certificatesigningrequests/status
    verbs:
    - update
  - apiGroups:
    - certificates.k8s.io
    resourceNames:
    - kubernetes.io/kube-apiserver-client-kubelet
    resources:
    - signers
    verbs:
    - approve
  - apiGroups:
    - certificates.k8s.io
    resourceNames:
    - kubernetes.io/kube-apiserver-client
    - kubernetes.io/kube-apiserver-client-kubelet
    - kubernetes.io/kubelet-serving
    - kubernetes.io/legacy-unknown
    resources:
    - signers
    verbs:
    - sign
  - apiGroups:
    - authorization.k8s.io
    resources:
    - subjectaccessreviews
    verbs:
    - create
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:clusterrole-aggregation-controller
  rules:
  - apiGroups:
    - rbac.authorization.k8s.io
    resources:
    - clusterroles
    verbs:
    - escalate
    - get
    - list
    - patch
    - update
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:cronjob-controller
  rules:
  - apiGroups:
    - batch
    resources:
    - cronjobs
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - batch
    resources:
    - jobs
    verbs:
    - create
    - delete
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - batch
    resources:
    - cronjobs/status
    verbs:
    - update
  - apiGroups:
    - batch
    resources:
    - cronjobs/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - delete
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:daemon-set-controller
  rules:
  - apiGroups:
    - apps
    - extensions
    resources:
    - daemonsets
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - apps
    - extensions
    resources:
    - daemonsets/status
    verbs:
    - update
  - apiGroups:
    - apps
    - extensions
    resources:
    - daemonsets/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - create
    - delete
    - list
    - patch
    - watch
  - apiGroups:
    - ""
    resources:
    - pods/binding
    verbs:
    - create
  - apiGroups:
    - apps
    resources:
    - controllerrevisions
    verbs:
    - create
    - delete
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:deployment-controller
  rules:
  - apiGroups:
    - apps
    - extensions
    resources:
    - deployments
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - apps
    - extensions
    resources:
    - deployments/status
    verbs:
    - update
  - apiGroups:
    - apps
    - extensions
    resources:
    - deployments/finalizers
    verbs:
    - update
  - apiGroups:
    - apps
    - extensions
    resources:
    - replicasets
    verbs:
    - create
    - delete
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:disruption-controller
  rules:
  - apiGroups:
    - apps
    - extensions
    resources:
    - deployments
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - apps
    - extensions
    resources:
    - replicasets
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - replicationcontrollers
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - policy
    resources:
    - poddisruptionbudgets
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - apps
    resources:
    - statefulsets
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - policy
    resources:
    - poddisruptionbudgets/status
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - pods/status
    verbs:
    - patch
    - update
  - apiGroups:
    - '*'
    resources:
    - '*/scale'
    verbs:
    - get
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:endpoint-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - pods
    - services
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - endpoints
    verbs:
    - create
    - delete
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - endpoints/restricted
    verbs:
    - create
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:endpointslice-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - nodes
    - pods
    - services
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - services/finalizers
    verbs:
    - update
  - apiGroups:
    - discovery.k8s.io
    resources:
    - endpointslices
    verbs:
    - create
    - delete
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:endpointslicemirroring-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - endpoints
    - services
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - services/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - endpoints/finalizers
    verbs:
    - update
  - apiGroups:
    - discovery.k8s.io
    resources:
    - endpointslices
    verbs:
    - create
    - delete
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:ephemeral-volume-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - pods/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    verbs:
    - create
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:expand-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - persistentvolumes
    verbs:
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims/status
    verbs:
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - services
    verbs:
    - get
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:generic-garbage-collector
  rules:
  - apiGroups:
    - '*'
    resources:
    - '*'
    verbs:
    - delete
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:horizontal-pod-autoscaler
  rules:
  - apiGroups:
    - autoscaling
    resources:
    - horizontalpodautoscalers
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - autoscaling
    resources:
    - horizontalpodautoscalers/status
    verbs:
    - update
  - apiGroups:
    - '*'
    resources:
    - '*/scale'
    verbs:
    - get
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - list
    - watch
  - apiGroups:
    - metrics.k8s.io
    resources:
    - pods
    verbs:
    - list
    - watch
  - apiGroups:
    - custom.metrics.k8s.io
    resources:
    - '*'
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - external.metrics.k8s.io
    resources:
    - '*'
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:job-controller
  rules:
  - apiGroups:
    - batch
    resources:
    - jobs
    verbs:
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - batch
    resources:
    - jobs/status
    verbs:
    - update
  - apiGroups:
    - batch
    resources:
    - jobs/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - create
    - delete
    - list
    - patch
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:legacy-service-account-token-cleaner
  rules:
  - apiGroups:
    - ""
    resourceNames:
    - kube-apiserver-legacy-service-account-token-tracking
    resources:
    - configmaps
    verbs:
    - get
  - apiGroups:
    - ""
    resources:
    - secrets
    verbs:
    - delete
    - patch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:namespace-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - namespaces
    verbs:
    - delete
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - namespaces/finalize
    - namespaces/status
    verbs:
    - update
  - apiGroups:
    - '*'
    resources:
    - '*'
    verbs:
    - delete
    - deletecollection
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:node-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - delete
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - nodes/status
    verbs:
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - pods/status
    verbs:
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - delete
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:persistent-volume-binder
  rules:
  - apiGroups:
    - ""
    resources:
    - persistentvolumes
    verbs:
    - create
    - delete
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - persistentvolumes/status
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims/status
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - create
    - delete
    - get
    - list
    - watch
  - apiGroups:
    - storage.k8s.io
    resources:
    - storageclasses
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - events
    verbs:
    - watch
  - apiGroups:
    - ""
    resources:
    - services
    verbs:
    - get
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:pod-garbage-collector
  rules:
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - delete
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - pods/status
    verbs:
    - patch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:podcertificaterequestcleaner
  rules:
  - apiGroups:
    - certificates.k8s.io
    resources:
    - podcertificaterequests
    verbs:
    - delete
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:pv-protection-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - persistentvolumes
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:pvc-protection-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:replicaset-controller
  rules:
  - apiGroups:
    - apps
    - extensions
    resources:
    - replicasets
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - apps
    - extensions
    resources:
    - replicasets/status
    verbs:
    - update
  - apiGroups:
    - apps
    - extensions
    resources:
    - replicasets/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - create
    - delete
    - list
    - patch
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:replication-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - replicationcontrollers
    verbs:
    - get
    - list
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - replicationcontrollers/status
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - replicationcontrollers/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - create
    - delete
    - list
    - patch
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:resource-claim-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - pods/finalizers
    verbs:
    - update
  - apiGroups:
    - resource.k8s.io
    resources:
    - resourceclaims
    verbs:
    - create
    - delete
    - get
    - list
    - watch
  - apiGroups:
    - resource.k8s.io
    resources:
    - resourceclaims
    - resourceclaims/status
    verbs:
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - pods/status
    verbs:
    - patch
    - update
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:resourcequota-controller
  rules:
  - apiGroups:
    - '*'
    resources:
    - '*'
    verbs:
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - resourcequotas/status
    verbs:
    - update
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:root-ca-cert-publisher
  rules:
  - apiGroups:
    - ""
    resources:
    - configmaps
    verbs:
    - create
    - update
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:route-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - nodes/status
    verbs:
    - patch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:selinux-warning-controller
  rules:
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - persistentvolumes
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - storage.k8s.io
    resources:
    - csidrivers
    verbs:
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:service-account-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - serviceaccounts
    verbs:
    - create
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:service-cidrs-controller
  rules:
  - apiGroups:
    - networking.k8s.io
    resources:
    - servicecidrs
    verbs:
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - networking.k8s.io
    resources:
    - servicecidrs/finalizers
    verbs:
    - patch
    - update
  - apiGroups:
    - networking.k8s.io
    resources:
    - servicecidrs/status
    verbs:
    - patch
    - update
  - apiGroups:
    - networking.k8s.io
    resources:
    - ipaddresses
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:service-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - services
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - ""
    resources:
    - services/status
    verbs:
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:statefulset-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - list
    - watch
  - apiGroups:
    - apps
    resources:
    - statefulsets
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - apps
    resources:
    - statefulsets/status
    verbs:
    - update
  - apiGroups:
    - apps
    resources:
    - statefulsets/finalizers
    verbs:
    - update
  - apiGroups:
    - ""
    resources:
    - pods
    verbs:
    - create
    - delete
    - get
    - patch
    - update
  - apiGroups:
    - apps
    resources:
    - controllerrevisions
    verbs:
    - create
    - delete
    - get
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    verbs:
    - create
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
  - apiGroups:
    - ""
    resources:
    - persistentvolumeclaims
    verbs:
    - delete
    - update
  - apiGroups:
    - ""
    resources:
    - pods/finalizers
    verbs:
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:ttl-after-finished-controller
  rules:
  - apiGroups:
    - batch
    resources:
    - jobs
    verbs:
    - delete
    - get
    - list
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:ttl-controller
  rules:
  - apiGroups:
    - ""
    resources:
    - nodes
    verbs:
    - list
    - patch
    - update
    - watch
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
- apiVersion: rbac.authorization.k8s.io/v1
  kind: ClusterRole
  metadata:
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
    labels:
      kubernetes.io/bootstrapping: rbac-defaults
    name: system:controller:validatingadmissionpolicy-status-controller
  rules:
  - apiGroups:
    - admissionregistration.k8s.io
    resources:
    - validatingadmissionpolicies
    verbs:
    - get
    - list
    - watch
  - apiGroups:
    - admissionregistration.k8s.io
    resources:
    - validatingadmissionpolicies/status
    verbs:
    - get
    - patch
    - update
  - apiGroups:
    - ""
    - events.k8s.io
    resources:
    - events
    verbs:
    - create
    - patch
    - update
kind: List
metadata: {}
