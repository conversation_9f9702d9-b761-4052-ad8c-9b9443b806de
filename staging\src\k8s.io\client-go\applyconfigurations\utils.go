/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package applyconfigurations

import (
	v1 "k8s.io/api/admissionregistration/v1"
	v1alpha1 "k8s.io/api/admissionregistration/v1alpha1"
	v1beta1 "k8s.io/api/admissionregistration/v1beta1"
	apiserverinternalv1alpha1 "k8s.io/api/apiserverinternal/v1alpha1"
	appsv1 "k8s.io/api/apps/v1"
	appsv1beta1 "k8s.io/api/apps/v1beta1"
	v1beta2 "k8s.io/api/apps/v1beta2"
	autoscalingv1 "k8s.io/api/autoscaling/v1"
	v2 "k8s.io/api/autoscaling/v2"
	v2beta1 "k8s.io/api/autoscaling/v2beta1"
	v2beta2 "k8s.io/api/autoscaling/v2beta2"
	batchv1 "k8s.io/api/batch/v1"
	batchv1beta1 "k8s.io/api/batch/v1beta1"
	certificatesv1 "k8s.io/api/certificates/v1"
	certificatesv1alpha1 "k8s.io/api/certificates/v1alpha1"
	certificatesv1beta1 "k8s.io/api/certificates/v1beta1"
	coordinationv1 "k8s.io/api/coordination/v1"
	v1alpha2 "k8s.io/api/coordination/v1alpha2"
	coordinationv1beta1 "k8s.io/api/coordination/v1beta1"
	corev1 "k8s.io/api/core/v1"
	discoveryv1 "k8s.io/api/discovery/v1"
	discoveryv1beta1 "k8s.io/api/discovery/v1beta1"
	eventsv1 "k8s.io/api/events/v1"
	eventsv1beta1 "k8s.io/api/events/v1beta1"
	extensionsv1beta1 "k8s.io/api/extensions/v1beta1"
	flowcontrolv1 "k8s.io/api/flowcontrol/v1"
	flowcontrolv1beta1 "k8s.io/api/flowcontrol/v1beta1"
	flowcontrolv1beta2 "k8s.io/api/flowcontrol/v1beta2"
	v1beta3 "k8s.io/api/flowcontrol/v1beta3"
	imagepolicyv1alpha1 "k8s.io/api/imagepolicy/v1alpha1"
	networkingv1 "k8s.io/api/networking/v1"
	networkingv1beta1 "k8s.io/api/networking/v1beta1"
	nodev1 "k8s.io/api/node/v1"
	nodev1alpha1 "k8s.io/api/node/v1alpha1"
	nodev1beta1 "k8s.io/api/node/v1beta1"
	policyv1 "k8s.io/api/policy/v1"
	policyv1beta1 "k8s.io/api/policy/v1beta1"
	rbacv1 "k8s.io/api/rbac/v1"
	rbacv1alpha1 "k8s.io/api/rbac/v1alpha1"
	rbacv1beta1 "k8s.io/api/rbac/v1beta1"
	resourcev1 "k8s.io/api/resource/v1"
	v1alpha3 "k8s.io/api/resource/v1alpha3"
	resourcev1beta1 "k8s.io/api/resource/v1beta1"
	resourcev1beta2 "k8s.io/api/resource/v1beta2"
	schedulingv1 "k8s.io/api/scheduling/v1"
	schedulingv1alpha1 "k8s.io/api/scheduling/v1alpha1"
	schedulingv1beta1 "k8s.io/api/scheduling/v1beta1"
	storagev1 "k8s.io/api/storage/v1"
	storagev1alpha1 "k8s.io/api/storage/v1alpha1"
	storagev1beta1 "k8s.io/api/storage/v1beta1"
	storagemigrationv1alpha1 "k8s.io/api/storagemigration/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	managedfields "k8s.io/apimachinery/pkg/util/managedfields"
	admissionregistrationv1 "k8s.io/client-go/applyconfigurations/admissionregistration/v1"
	admissionregistrationv1alpha1 "k8s.io/client-go/applyconfigurations/admissionregistration/v1alpha1"
	admissionregistrationv1beta1 "k8s.io/client-go/applyconfigurations/admissionregistration/v1beta1"
	applyconfigurationsapiserverinternalv1alpha1 "k8s.io/client-go/applyconfigurations/apiserverinternal/v1alpha1"
	applyconfigurationsappsv1 "k8s.io/client-go/applyconfigurations/apps/v1"
	applyconfigurationsappsv1beta1 "k8s.io/client-go/applyconfigurations/apps/v1beta1"
	appsv1beta2 "k8s.io/client-go/applyconfigurations/apps/v1beta2"
	applyconfigurationsautoscalingv1 "k8s.io/client-go/applyconfigurations/autoscaling/v1"
	autoscalingv2 "k8s.io/client-go/applyconfigurations/autoscaling/v2"
	autoscalingv2beta1 "k8s.io/client-go/applyconfigurations/autoscaling/v2beta1"
	autoscalingv2beta2 "k8s.io/client-go/applyconfigurations/autoscaling/v2beta2"
	applyconfigurationsbatchv1 "k8s.io/client-go/applyconfigurations/batch/v1"
	applyconfigurationsbatchv1beta1 "k8s.io/client-go/applyconfigurations/batch/v1beta1"
	applyconfigurationscertificatesv1 "k8s.io/client-go/applyconfigurations/certificates/v1"
	applyconfigurationscertificatesv1alpha1 "k8s.io/client-go/applyconfigurations/certificates/v1alpha1"
	applyconfigurationscertificatesv1beta1 "k8s.io/client-go/applyconfigurations/certificates/v1beta1"
	applyconfigurationscoordinationv1 "k8s.io/client-go/applyconfigurations/coordination/v1"
	coordinationv1alpha2 "k8s.io/client-go/applyconfigurations/coordination/v1alpha2"
	applyconfigurationscoordinationv1beta1 "k8s.io/client-go/applyconfigurations/coordination/v1beta1"
	applyconfigurationscorev1 "k8s.io/client-go/applyconfigurations/core/v1"
	applyconfigurationsdiscoveryv1 "k8s.io/client-go/applyconfigurations/discovery/v1"
	applyconfigurationsdiscoveryv1beta1 "k8s.io/client-go/applyconfigurations/discovery/v1beta1"
	applyconfigurationseventsv1 "k8s.io/client-go/applyconfigurations/events/v1"
	applyconfigurationseventsv1beta1 "k8s.io/client-go/applyconfigurations/events/v1beta1"
	applyconfigurationsextensionsv1beta1 "k8s.io/client-go/applyconfigurations/extensions/v1beta1"
	applyconfigurationsflowcontrolv1 "k8s.io/client-go/applyconfigurations/flowcontrol/v1"
	applyconfigurationsflowcontrolv1beta1 "k8s.io/client-go/applyconfigurations/flowcontrol/v1beta1"
	applyconfigurationsflowcontrolv1beta2 "k8s.io/client-go/applyconfigurations/flowcontrol/v1beta2"
	flowcontrolv1beta3 "k8s.io/client-go/applyconfigurations/flowcontrol/v1beta3"
	applyconfigurationsimagepolicyv1alpha1 "k8s.io/client-go/applyconfigurations/imagepolicy/v1alpha1"
	internal "k8s.io/client-go/applyconfigurations/internal"
	applyconfigurationsmetav1 "k8s.io/client-go/applyconfigurations/meta/v1"
	applyconfigurationsnetworkingv1 "k8s.io/client-go/applyconfigurations/networking/v1"
	applyconfigurationsnetworkingv1beta1 "k8s.io/client-go/applyconfigurations/networking/v1beta1"
	applyconfigurationsnodev1 "k8s.io/client-go/applyconfigurations/node/v1"
	applyconfigurationsnodev1alpha1 "k8s.io/client-go/applyconfigurations/node/v1alpha1"
	applyconfigurationsnodev1beta1 "k8s.io/client-go/applyconfigurations/node/v1beta1"
	applyconfigurationspolicyv1 "k8s.io/client-go/applyconfigurations/policy/v1"
	applyconfigurationspolicyv1beta1 "k8s.io/client-go/applyconfigurations/policy/v1beta1"
	applyconfigurationsrbacv1 "k8s.io/client-go/applyconfigurations/rbac/v1"
	applyconfigurationsrbacv1alpha1 "k8s.io/client-go/applyconfigurations/rbac/v1alpha1"
	applyconfigurationsrbacv1beta1 "k8s.io/client-go/applyconfigurations/rbac/v1beta1"
	applyconfigurationsresourcev1 "k8s.io/client-go/applyconfigurations/resource/v1"
	resourcev1alpha3 "k8s.io/client-go/applyconfigurations/resource/v1alpha3"
	applyconfigurationsresourcev1beta1 "k8s.io/client-go/applyconfigurations/resource/v1beta1"
	applyconfigurationsresourcev1beta2 "k8s.io/client-go/applyconfigurations/resource/v1beta2"
	applyconfigurationsschedulingv1 "k8s.io/client-go/applyconfigurations/scheduling/v1"
	applyconfigurationsschedulingv1alpha1 "k8s.io/client-go/applyconfigurations/scheduling/v1alpha1"
	applyconfigurationsschedulingv1beta1 "k8s.io/client-go/applyconfigurations/scheduling/v1beta1"
	applyconfigurationsstoragev1 "k8s.io/client-go/applyconfigurations/storage/v1"
	applyconfigurationsstoragev1alpha1 "k8s.io/client-go/applyconfigurations/storage/v1alpha1"
	applyconfigurationsstoragev1beta1 "k8s.io/client-go/applyconfigurations/storage/v1beta1"
	applyconfigurationsstoragemigrationv1alpha1 "k8s.io/client-go/applyconfigurations/storagemigration/v1alpha1"
)

// ForKind returns an apply configuration type for the given GroupVersionKind, or nil if no
// apply configuration type exists for the given GroupVersionKind.
func ForKind(kind schema.GroupVersionKind) interface{} {
	switch kind {
	// Group=admissionregistration.k8s.io, Version=v1
	case v1.SchemeGroupVersion.WithKind("AuditAnnotation"):
		return &admissionregistrationv1.AuditAnnotationApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ExpressionWarning"):
		return &admissionregistrationv1.ExpressionWarningApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("MatchCondition"):
		return &admissionregistrationv1.MatchConditionApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("MatchResources"):
		return &admissionregistrationv1.MatchResourcesApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("MutatingWebhook"):
		return &admissionregistrationv1.MutatingWebhookApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("MutatingWebhookConfiguration"):
		return &admissionregistrationv1.MutatingWebhookConfigurationApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("NamedRuleWithOperations"):
		return &admissionregistrationv1.NamedRuleWithOperationsApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ParamKind"):
		return &admissionregistrationv1.ParamKindApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ParamRef"):
		return &admissionregistrationv1.ParamRefApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("Rule"):
		return &admissionregistrationv1.RuleApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("RuleWithOperations"):
		return &admissionregistrationv1.RuleWithOperationsApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ServiceReference"):
		return &admissionregistrationv1.ServiceReferenceApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("TypeChecking"):
		return &admissionregistrationv1.TypeCheckingApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicy"):
		return &admissionregistrationv1.ValidatingAdmissionPolicyApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyBinding"):
		return &admissionregistrationv1.ValidatingAdmissionPolicyBindingApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyBindingSpec"):
		return &admissionregistrationv1.ValidatingAdmissionPolicyBindingSpecApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicySpec"):
		return &admissionregistrationv1.ValidatingAdmissionPolicySpecApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyStatus"):
		return &admissionregistrationv1.ValidatingAdmissionPolicyStatusApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ValidatingWebhook"):
		return &admissionregistrationv1.ValidatingWebhookApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("ValidatingWebhookConfiguration"):
		return &admissionregistrationv1.ValidatingWebhookConfigurationApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("Validation"):
		return &admissionregistrationv1.ValidationApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("Variable"):
		return &admissionregistrationv1.VariableApplyConfiguration{}
	case v1.SchemeGroupVersion.WithKind("WebhookClientConfig"):
		return &admissionregistrationv1.WebhookClientConfigApplyConfiguration{}

		// Group=admissionregistration.k8s.io, Version=v1alpha1
	case v1alpha1.SchemeGroupVersion.WithKind("ApplyConfiguration"):
		return &admissionregistrationv1alpha1.ApplyConfigurationApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("AuditAnnotation"):
		return &admissionregistrationv1alpha1.AuditAnnotationApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ExpressionWarning"):
		return &admissionregistrationv1alpha1.ExpressionWarningApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("JSONPatch"):
		return &admissionregistrationv1alpha1.JSONPatchApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("MatchCondition"):
		return &admissionregistrationv1alpha1.MatchConditionApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("MatchResources"):
		return &admissionregistrationv1alpha1.MatchResourcesApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicy"):
		return &admissionregistrationv1alpha1.MutatingAdmissionPolicyApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicyBinding"):
		return &admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicyBindingSpec"):
		return &admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpecApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicySpec"):
		return &admissionregistrationv1alpha1.MutatingAdmissionPolicySpecApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("Mutation"):
		return &admissionregistrationv1alpha1.MutationApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("NamedRuleWithOperations"):
		return &admissionregistrationv1alpha1.NamedRuleWithOperationsApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ParamKind"):
		return &admissionregistrationv1alpha1.ParamKindApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ParamRef"):
		return &admissionregistrationv1alpha1.ParamRefApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("TypeChecking"):
		return &admissionregistrationv1alpha1.TypeCheckingApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicy"):
		return &admissionregistrationv1alpha1.ValidatingAdmissionPolicyApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyBinding"):
		return &admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyBindingSpec"):
		return &admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpecApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicySpec"):
		return &admissionregistrationv1alpha1.ValidatingAdmissionPolicySpecApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyStatus"):
		return &admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatusApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("Validation"):
		return &admissionregistrationv1alpha1.ValidationApplyConfiguration{}
	case v1alpha1.SchemeGroupVersion.WithKind("Variable"):
		return &admissionregistrationv1alpha1.VariableApplyConfiguration{}

		// Group=admissionregistration.k8s.io, Version=v1beta1
	case v1beta1.SchemeGroupVersion.WithKind("ApplyConfiguration"):
		return &admissionregistrationv1beta1.ApplyConfigurationApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("AuditAnnotation"):
		return &admissionregistrationv1beta1.AuditAnnotationApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ExpressionWarning"):
		return &admissionregistrationv1beta1.ExpressionWarningApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("JSONPatch"):
		return &admissionregistrationv1beta1.JSONPatchApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MatchCondition"):
		return &admissionregistrationv1beta1.MatchConditionApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MatchResources"):
		return &admissionregistrationv1beta1.MatchResourcesApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicy"):
		return &admissionregistrationv1beta1.MutatingAdmissionPolicyApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicyBinding"):
		return &admissionregistrationv1beta1.MutatingAdmissionPolicyBindingApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicyBindingSpec"):
		return &admissionregistrationv1beta1.MutatingAdmissionPolicyBindingSpecApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MutatingAdmissionPolicySpec"):
		return &admissionregistrationv1beta1.MutatingAdmissionPolicySpecApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MutatingWebhook"):
		return &admissionregistrationv1beta1.MutatingWebhookApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("MutatingWebhookConfiguration"):
		return &admissionregistrationv1beta1.MutatingWebhookConfigurationApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("Mutation"):
		return &admissionregistrationv1beta1.MutationApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("NamedRuleWithOperations"):
		return &admissionregistrationv1beta1.NamedRuleWithOperationsApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ParamKind"):
		return &admissionregistrationv1beta1.ParamKindApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ParamRef"):
		return &admissionregistrationv1beta1.ParamRefApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ServiceReference"):
		return &admissionregistrationv1beta1.ServiceReferenceApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("TypeChecking"):
		return &admissionregistrationv1beta1.TypeCheckingApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicy"):
		return &admissionregistrationv1beta1.ValidatingAdmissionPolicyApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyBinding"):
		return &admissionregistrationv1beta1.ValidatingAdmissionPolicyBindingApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyBindingSpec"):
		return &admissionregistrationv1beta1.ValidatingAdmissionPolicyBindingSpecApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicySpec"):
		return &admissionregistrationv1beta1.ValidatingAdmissionPolicySpecApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ValidatingAdmissionPolicyStatus"):
		return &admissionregistrationv1beta1.ValidatingAdmissionPolicyStatusApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ValidatingWebhook"):
		return &admissionregistrationv1beta1.ValidatingWebhookApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("ValidatingWebhookConfiguration"):
		return &admissionregistrationv1beta1.ValidatingWebhookConfigurationApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("Validation"):
		return &admissionregistrationv1beta1.ValidationApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("Variable"):
		return &admissionregistrationv1beta1.VariableApplyConfiguration{}
	case v1beta1.SchemeGroupVersion.WithKind("WebhookClientConfig"):
		return &admissionregistrationv1beta1.WebhookClientConfigApplyConfiguration{}

		// Group=apps, Version=v1
	case appsv1.SchemeGroupVersion.WithKind("ControllerRevision"):
		return &applyconfigurationsappsv1.ControllerRevisionApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DaemonSet"):
		return &applyconfigurationsappsv1.DaemonSetApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DaemonSetCondition"):
		return &applyconfigurationsappsv1.DaemonSetConditionApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DaemonSetSpec"):
		return &applyconfigurationsappsv1.DaemonSetSpecApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DaemonSetStatus"):
		return &applyconfigurationsappsv1.DaemonSetStatusApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DaemonSetUpdateStrategy"):
		return &applyconfigurationsappsv1.DaemonSetUpdateStrategyApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("Deployment"):
		return &applyconfigurationsappsv1.DeploymentApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DeploymentCondition"):
		return &applyconfigurationsappsv1.DeploymentConditionApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DeploymentSpec"):
		return &applyconfigurationsappsv1.DeploymentSpecApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DeploymentStatus"):
		return &applyconfigurationsappsv1.DeploymentStatusApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("DeploymentStrategy"):
		return &applyconfigurationsappsv1.DeploymentStrategyApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("ReplicaSet"):
		return &applyconfigurationsappsv1.ReplicaSetApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("ReplicaSetCondition"):
		return &applyconfigurationsappsv1.ReplicaSetConditionApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("ReplicaSetSpec"):
		return &applyconfigurationsappsv1.ReplicaSetSpecApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("ReplicaSetStatus"):
		return &applyconfigurationsappsv1.ReplicaSetStatusApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("RollingUpdateDaemonSet"):
		return &applyconfigurationsappsv1.RollingUpdateDaemonSetApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("RollingUpdateDeployment"):
		return &applyconfigurationsappsv1.RollingUpdateDeploymentApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("RollingUpdateStatefulSetStrategy"):
		return &applyconfigurationsappsv1.RollingUpdateStatefulSetStrategyApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("StatefulSet"):
		return &applyconfigurationsappsv1.StatefulSetApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("StatefulSetCondition"):
		return &applyconfigurationsappsv1.StatefulSetConditionApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("StatefulSetOrdinals"):
		return &applyconfigurationsappsv1.StatefulSetOrdinalsApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("StatefulSetPersistentVolumeClaimRetentionPolicy"):
		return &applyconfigurationsappsv1.StatefulSetPersistentVolumeClaimRetentionPolicyApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("StatefulSetSpec"):
		return &applyconfigurationsappsv1.StatefulSetSpecApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("StatefulSetStatus"):
		return &applyconfigurationsappsv1.StatefulSetStatusApplyConfiguration{}
	case appsv1.SchemeGroupVersion.WithKind("StatefulSetUpdateStrategy"):
		return &applyconfigurationsappsv1.StatefulSetUpdateStrategyApplyConfiguration{}

		// Group=apps, Version=v1beta1
	case appsv1beta1.SchemeGroupVersion.WithKind("ControllerRevision"):
		return &applyconfigurationsappsv1beta1.ControllerRevisionApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("Deployment"):
		return &applyconfigurationsappsv1beta1.DeploymentApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("DeploymentCondition"):
		return &applyconfigurationsappsv1beta1.DeploymentConditionApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("DeploymentSpec"):
		return &applyconfigurationsappsv1beta1.DeploymentSpecApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("DeploymentStatus"):
		return &applyconfigurationsappsv1beta1.DeploymentStatusApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("DeploymentStrategy"):
		return &applyconfigurationsappsv1beta1.DeploymentStrategyApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("RollbackConfig"):
		return &applyconfigurationsappsv1beta1.RollbackConfigApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("RollingUpdateDeployment"):
		return &applyconfigurationsappsv1beta1.RollingUpdateDeploymentApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("RollingUpdateStatefulSetStrategy"):
		return &applyconfigurationsappsv1beta1.RollingUpdateStatefulSetStrategyApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("StatefulSet"):
		return &applyconfigurationsappsv1beta1.StatefulSetApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("StatefulSetCondition"):
		return &applyconfigurationsappsv1beta1.StatefulSetConditionApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("StatefulSetOrdinals"):
		return &applyconfigurationsappsv1beta1.StatefulSetOrdinalsApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("StatefulSetPersistentVolumeClaimRetentionPolicy"):
		return &applyconfigurationsappsv1beta1.StatefulSetPersistentVolumeClaimRetentionPolicyApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("StatefulSetSpec"):
		return &applyconfigurationsappsv1beta1.StatefulSetSpecApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("StatefulSetStatus"):
		return &applyconfigurationsappsv1beta1.StatefulSetStatusApplyConfiguration{}
	case appsv1beta1.SchemeGroupVersion.WithKind("StatefulSetUpdateStrategy"):
		return &applyconfigurationsappsv1beta1.StatefulSetUpdateStrategyApplyConfiguration{}

		// Group=apps, Version=v1beta2
	case v1beta2.SchemeGroupVersion.WithKind("ControllerRevision"):
		return &appsv1beta2.ControllerRevisionApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DaemonSet"):
		return &appsv1beta2.DaemonSetApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DaemonSetCondition"):
		return &appsv1beta2.DaemonSetConditionApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DaemonSetSpec"):
		return &appsv1beta2.DaemonSetSpecApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DaemonSetStatus"):
		return &appsv1beta2.DaemonSetStatusApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DaemonSetUpdateStrategy"):
		return &appsv1beta2.DaemonSetUpdateStrategyApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("Deployment"):
		return &appsv1beta2.DeploymentApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DeploymentCondition"):
		return &appsv1beta2.DeploymentConditionApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DeploymentSpec"):
		return &appsv1beta2.DeploymentSpecApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DeploymentStatus"):
		return &appsv1beta2.DeploymentStatusApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("DeploymentStrategy"):
		return &appsv1beta2.DeploymentStrategyApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("ReplicaSet"):
		return &appsv1beta2.ReplicaSetApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("ReplicaSetCondition"):
		return &appsv1beta2.ReplicaSetConditionApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("ReplicaSetSpec"):
		return &appsv1beta2.ReplicaSetSpecApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("ReplicaSetStatus"):
		return &appsv1beta2.ReplicaSetStatusApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("RollingUpdateDaemonSet"):
		return &appsv1beta2.RollingUpdateDaemonSetApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("RollingUpdateDeployment"):
		return &appsv1beta2.RollingUpdateDeploymentApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("RollingUpdateStatefulSetStrategy"):
		return &appsv1beta2.RollingUpdateStatefulSetStrategyApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("Scale"):
		return &appsv1beta2.ScaleApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("StatefulSet"):
		return &appsv1beta2.StatefulSetApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("StatefulSetCondition"):
		return &appsv1beta2.StatefulSetConditionApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("StatefulSetOrdinals"):
		return &appsv1beta2.StatefulSetOrdinalsApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("StatefulSetPersistentVolumeClaimRetentionPolicy"):
		return &appsv1beta2.StatefulSetPersistentVolumeClaimRetentionPolicyApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("StatefulSetSpec"):
		return &appsv1beta2.StatefulSetSpecApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("StatefulSetStatus"):
		return &appsv1beta2.StatefulSetStatusApplyConfiguration{}
	case v1beta2.SchemeGroupVersion.WithKind("StatefulSetUpdateStrategy"):
		return &appsv1beta2.StatefulSetUpdateStrategyApplyConfiguration{}

		// Group=autoscaling, Version=v1
	case autoscalingv1.SchemeGroupVersion.WithKind("CrossVersionObjectReference"):
		return &applyconfigurationsautoscalingv1.CrossVersionObjectReferenceApplyConfiguration{}
	case autoscalingv1.SchemeGroupVersion.WithKind("HorizontalPodAutoscaler"):
		return &applyconfigurationsautoscalingv1.HorizontalPodAutoscalerApplyConfiguration{}
	case autoscalingv1.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerSpec"):
		return &applyconfigurationsautoscalingv1.HorizontalPodAutoscalerSpecApplyConfiguration{}
	case autoscalingv1.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerStatus"):
		return &applyconfigurationsautoscalingv1.HorizontalPodAutoscalerStatusApplyConfiguration{}
	case autoscalingv1.SchemeGroupVersion.WithKind("Scale"):
		return &applyconfigurationsautoscalingv1.ScaleApplyConfiguration{}
	case autoscalingv1.SchemeGroupVersion.WithKind("ScaleSpec"):
		return &applyconfigurationsautoscalingv1.ScaleSpecApplyConfiguration{}
	case autoscalingv1.SchemeGroupVersion.WithKind("ScaleStatus"):
		return &applyconfigurationsautoscalingv1.ScaleStatusApplyConfiguration{}

		// Group=autoscaling, Version=v2
	case v2.SchemeGroupVersion.WithKind("ContainerResourceMetricSource"):
		return &autoscalingv2.ContainerResourceMetricSourceApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("ContainerResourceMetricStatus"):
		return &autoscalingv2.ContainerResourceMetricStatusApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("CrossVersionObjectReference"):
		return &autoscalingv2.CrossVersionObjectReferenceApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("ExternalMetricSource"):
		return &autoscalingv2.ExternalMetricSourceApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("ExternalMetricStatus"):
		return &autoscalingv2.ExternalMetricStatusApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("HorizontalPodAutoscaler"):
		return &autoscalingv2.HorizontalPodAutoscalerApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerBehavior"):
		return &autoscalingv2.HorizontalPodAutoscalerBehaviorApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerCondition"):
		return &autoscalingv2.HorizontalPodAutoscalerConditionApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerSpec"):
		return &autoscalingv2.HorizontalPodAutoscalerSpecApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerStatus"):
		return &autoscalingv2.HorizontalPodAutoscalerStatusApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("HPAScalingPolicy"):
		return &autoscalingv2.HPAScalingPolicyApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("HPAScalingRules"):
		return &autoscalingv2.HPAScalingRulesApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("MetricIdentifier"):
		return &autoscalingv2.MetricIdentifierApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("MetricSpec"):
		return &autoscalingv2.MetricSpecApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("MetricStatus"):
		return &autoscalingv2.MetricStatusApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("MetricTarget"):
		return &autoscalingv2.MetricTargetApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("MetricValueStatus"):
		return &autoscalingv2.MetricValueStatusApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("ObjectMetricSource"):
		return &autoscalingv2.ObjectMetricSourceApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("ObjectMetricStatus"):
		return &autoscalingv2.ObjectMetricStatusApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("PodsMetricSource"):
		return &autoscalingv2.PodsMetricSourceApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("PodsMetricStatus"):
		return &autoscalingv2.PodsMetricStatusApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("ResourceMetricSource"):
		return &autoscalingv2.ResourceMetricSourceApplyConfiguration{}
	case v2.SchemeGroupVersion.WithKind("ResourceMetricStatus"):
		return &autoscalingv2.ResourceMetricStatusApplyConfiguration{}

		// Group=autoscaling, Version=v2beta1
	case v2beta1.SchemeGroupVersion.WithKind("ContainerResourceMetricSource"):
		return &autoscalingv2beta1.ContainerResourceMetricSourceApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("ContainerResourceMetricStatus"):
		return &autoscalingv2beta1.ContainerResourceMetricStatusApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("CrossVersionObjectReference"):
		return &autoscalingv2beta1.CrossVersionObjectReferenceApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("ExternalMetricSource"):
		return &autoscalingv2beta1.ExternalMetricSourceApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("ExternalMetricStatus"):
		return &autoscalingv2beta1.ExternalMetricStatusApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("HorizontalPodAutoscaler"):
		return &autoscalingv2beta1.HorizontalPodAutoscalerApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerCondition"):
		return &autoscalingv2beta1.HorizontalPodAutoscalerConditionApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerSpec"):
		return &autoscalingv2beta1.HorizontalPodAutoscalerSpecApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerStatus"):
		return &autoscalingv2beta1.HorizontalPodAutoscalerStatusApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("MetricSpec"):
		return &autoscalingv2beta1.MetricSpecApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("MetricStatus"):
		return &autoscalingv2beta1.MetricStatusApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("ObjectMetricSource"):
		return &autoscalingv2beta1.ObjectMetricSourceApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("ObjectMetricStatus"):
		return &autoscalingv2beta1.ObjectMetricStatusApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("PodsMetricSource"):
		return &autoscalingv2beta1.PodsMetricSourceApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("PodsMetricStatus"):
		return &autoscalingv2beta1.PodsMetricStatusApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("ResourceMetricSource"):
		return &autoscalingv2beta1.ResourceMetricSourceApplyConfiguration{}
	case v2beta1.SchemeGroupVersion.WithKind("ResourceMetricStatus"):
		return &autoscalingv2beta1.ResourceMetricStatusApplyConfiguration{}

		// Group=autoscaling, Version=v2beta2
	case v2beta2.SchemeGroupVersion.WithKind("ContainerResourceMetricSource"):
		return &autoscalingv2beta2.ContainerResourceMetricSourceApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("ContainerResourceMetricStatus"):
		return &autoscalingv2beta2.ContainerResourceMetricStatusApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("CrossVersionObjectReference"):
		return &autoscalingv2beta2.CrossVersionObjectReferenceApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("ExternalMetricSource"):
		return &autoscalingv2beta2.ExternalMetricSourceApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("ExternalMetricStatus"):
		return &autoscalingv2beta2.ExternalMetricStatusApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("HorizontalPodAutoscaler"):
		return &autoscalingv2beta2.HorizontalPodAutoscalerApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerBehavior"):
		return &autoscalingv2beta2.HorizontalPodAutoscalerBehaviorApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerCondition"):
		return &autoscalingv2beta2.HorizontalPodAutoscalerConditionApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerSpec"):
		return &autoscalingv2beta2.HorizontalPodAutoscalerSpecApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("HorizontalPodAutoscalerStatus"):
		return &autoscalingv2beta2.HorizontalPodAutoscalerStatusApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("HPAScalingPolicy"):
		return &autoscalingv2beta2.HPAScalingPolicyApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("HPAScalingRules"):
		return &autoscalingv2beta2.HPAScalingRulesApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("MetricIdentifier"):
		return &autoscalingv2beta2.MetricIdentifierApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("MetricSpec"):
		return &autoscalingv2beta2.MetricSpecApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("MetricStatus"):
		return &autoscalingv2beta2.MetricStatusApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("MetricTarget"):
		return &autoscalingv2beta2.MetricTargetApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("MetricValueStatus"):
		return &autoscalingv2beta2.MetricValueStatusApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("ObjectMetricSource"):
		return &autoscalingv2beta2.ObjectMetricSourceApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("ObjectMetricStatus"):
		return &autoscalingv2beta2.ObjectMetricStatusApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("PodsMetricSource"):
		return &autoscalingv2beta2.PodsMetricSourceApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("PodsMetricStatus"):
		return &autoscalingv2beta2.PodsMetricStatusApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("ResourceMetricSource"):
		return &autoscalingv2beta2.ResourceMetricSourceApplyConfiguration{}
	case v2beta2.SchemeGroupVersion.WithKind("ResourceMetricStatus"):
		return &autoscalingv2beta2.ResourceMetricStatusApplyConfiguration{}

		// Group=batch, Version=v1
	case batchv1.SchemeGroupVersion.WithKind("CronJob"):
		return &applyconfigurationsbatchv1.CronJobApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("CronJobSpec"):
		return &applyconfigurationsbatchv1.CronJobSpecApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("CronJobStatus"):
		return &applyconfigurationsbatchv1.CronJobStatusApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("Job"):
		return &applyconfigurationsbatchv1.JobApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("JobCondition"):
		return &applyconfigurationsbatchv1.JobConditionApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("JobSpec"):
		return &applyconfigurationsbatchv1.JobSpecApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("JobStatus"):
		return &applyconfigurationsbatchv1.JobStatusApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("JobTemplateSpec"):
		return &applyconfigurationsbatchv1.JobTemplateSpecApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("PodFailurePolicy"):
		return &applyconfigurationsbatchv1.PodFailurePolicyApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("PodFailurePolicyOnExitCodesRequirement"):
		return &applyconfigurationsbatchv1.PodFailurePolicyOnExitCodesRequirementApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("PodFailurePolicyOnPodConditionsPattern"):
		return &applyconfigurationsbatchv1.PodFailurePolicyOnPodConditionsPatternApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("PodFailurePolicyRule"):
		return &applyconfigurationsbatchv1.PodFailurePolicyRuleApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("SuccessPolicy"):
		return &applyconfigurationsbatchv1.SuccessPolicyApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("SuccessPolicyRule"):
		return &applyconfigurationsbatchv1.SuccessPolicyRuleApplyConfiguration{}
	case batchv1.SchemeGroupVersion.WithKind("UncountedTerminatedPods"):
		return &applyconfigurationsbatchv1.UncountedTerminatedPodsApplyConfiguration{}

		// Group=batch, Version=v1beta1
	case batchv1beta1.SchemeGroupVersion.WithKind("CronJob"):
		return &applyconfigurationsbatchv1beta1.CronJobApplyConfiguration{}
	case batchv1beta1.SchemeGroupVersion.WithKind("CronJobSpec"):
		return &applyconfigurationsbatchv1beta1.CronJobSpecApplyConfiguration{}
	case batchv1beta1.SchemeGroupVersion.WithKind("CronJobStatus"):
		return &applyconfigurationsbatchv1beta1.CronJobStatusApplyConfiguration{}
	case batchv1beta1.SchemeGroupVersion.WithKind("JobTemplateSpec"):
		return &applyconfigurationsbatchv1beta1.JobTemplateSpecApplyConfiguration{}

		// Group=certificates.k8s.io, Version=v1
	case certificatesv1.SchemeGroupVersion.WithKind("CertificateSigningRequest"):
		return &applyconfigurationscertificatesv1.CertificateSigningRequestApplyConfiguration{}
	case certificatesv1.SchemeGroupVersion.WithKind("CertificateSigningRequestCondition"):
		return &applyconfigurationscertificatesv1.CertificateSigningRequestConditionApplyConfiguration{}
	case certificatesv1.SchemeGroupVersion.WithKind("CertificateSigningRequestSpec"):
		return &applyconfigurationscertificatesv1.CertificateSigningRequestSpecApplyConfiguration{}
	case certificatesv1.SchemeGroupVersion.WithKind("CertificateSigningRequestStatus"):
		return &applyconfigurationscertificatesv1.CertificateSigningRequestStatusApplyConfiguration{}

		// Group=certificates.k8s.io, Version=v1alpha1
	case certificatesv1alpha1.SchemeGroupVersion.WithKind("ClusterTrustBundle"):
		return &applyconfigurationscertificatesv1alpha1.ClusterTrustBundleApplyConfiguration{}
	case certificatesv1alpha1.SchemeGroupVersion.WithKind("ClusterTrustBundleSpec"):
		return &applyconfigurationscertificatesv1alpha1.ClusterTrustBundleSpecApplyConfiguration{}
	case certificatesv1alpha1.SchemeGroupVersion.WithKind("PodCertificateRequest"):
		return &applyconfigurationscertificatesv1alpha1.PodCertificateRequestApplyConfiguration{}
	case certificatesv1alpha1.SchemeGroupVersion.WithKind("PodCertificateRequestSpec"):
		return &applyconfigurationscertificatesv1alpha1.PodCertificateRequestSpecApplyConfiguration{}
	case certificatesv1alpha1.SchemeGroupVersion.WithKind("PodCertificateRequestStatus"):
		return &applyconfigurationscertificatesv1alpha1.PodCertificateRequestStatusApplyConfiguration{}

		// Group=certificates.k8s.io, Version=v1beta1
	case certificatesv1beta1.SchemeGroupVersion.WithKind("CertificateSigningRequest"):
		return &applyconfigurationscertificatesv1beta1.CertificateSigningRequestApplyConfiguration{}
	case certificatesv1beta1.SchemeGroupVersion.WithKind("CertificateSigningRequestCondition"):
		return &applyconfigurationscertificatesv1beta1.CertificateSigningRequestConditionApplyConfiguration{}
	case certificatesv1beta1.SchemeGroupVersion.WithKind("CertificateSigningRequestSpec"):
		return &applyconfigurationscertificatesv1beta1.CertificateSigningRequestSpecApplyConfiguration{}
	case certificatesv1beta1.SchemeGroupVersion.WithKind("CertificateSigningRequestStatus"):
		return &applyconfigurationscertificatesv1beta1.CertificateSigningRequestStatusApplyConfiguration{}
	case certificatesv1beta1.SchemeGroupVersion.WithKind("ClusterTrustBundle"):
		return &applyconfigurationscertificatesv1beta1.ClusterTrustBundleApplyConfiguration{}
	case certificatesv1beta1.SchemeGroupVersion.WithKind("ClusterTrustBundleSpec"):
		return &applyconfigurationscertificatesv1beta1.ClusterTrustBundleSpecApplyConfiguration{}

		// Group=coordination.k8s.io, Version=v1
	case coordinationv1.SchemeGroupVersion.WithKind("Lease"):
		return &applyconfigurationscoordinationv1.LeaseApplyConfiguration{}
	case coordinationv1.SchemeGroupVersion.WithKind("LeaseSpec"):
		return &applyconfigurationscoordinationv1.LeaseSpecApplyConfiguration{}

		// Group=coordination.k8s.io, Version=v1alpha2
	case v1alpha2.SchemeGroupVersion.WithKind("LeaseCandidate"):
		return &coordinationv1alpha2.LeaseCandidateApplyConfiguration{}
	case v1alpha2.SchemeGroupVersion.WithKind("LeaseCandidateSpec"):
		return &coordinationv1alpha2.LeaseCandidateSpecApplyConfiguration{}

		// Group=coordination.k8s.io, Version=v1beta1
	case coordinationv1beta1.SchemeGroupVersion.WithKind("Lease"):
		return &applyconfigurationscoordinationv1beta1.LeaseApplyConfiguration{}
	case coordinationv1beta1.SchemeGroupVersion.WithKind("LeaseCandidate"):
		return &applyconfigurationscoordinationv1beta1.LeaseCandidateApplyConfiguration{}
	case coordinationv1beta1.SchemeGroupVersion.WithKind("LeaseCandidateSpec"):
		return &applyconfigurationscoordinationv1beta1.LeaseCandidateSpecApplyConfiguration{}
	case coordinationv1beta1.SchemeGroupVersion.WithKind("LeaseSpec"):
		return &applyconfigurationscoordinationv1beta1.LeaseSpecApplyConfiguration{}

		// Group=core, Version=v1
	case corev1.SchemeGroupVersion.WithKind("Affinity"):
		return &applyconfigurationscorev1.AffinityApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("AppArmorProfile"):
		return &applyconfigurationscorev1.AppArmorProfileApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("AttachedVolume"):
		return &applyconfigurationscorev1.AttachedVolumeApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("AWSElasticBlockStoreVolumeSource"):
		return &applyconfigurationscorev1.AWSElasticBlockStoreVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("AzureDiskVolumeSource"):
		return &applyconfigurationscorev1.AzureDiskVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("AzureFilePersistentVolumeSource"):
		return &applyconfigurationscorev1.AzureFilePersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("AzureFileVolumeSource"):
		return &applyconfigurationscorev1.AzureFileVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Capabilities"):
		return &applyconfigurationscorev1.CapabilitiesApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("CephFSPersistentVolumeSource"):
		return &applyconfigurationscorev1.CephFSPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("CephFSVolumeSource"):
		return &applyconfigurationscorev1.CephFSVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("CinderPersistentVolumeSource"):
		return &applyconfigurationscorev1.CinderPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("CinderVolumeSource"):
		return &applyconfigurationscorev1.CinderVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ClientIPConfig"):
		return &applyconfigurationscorev1.ClientIPConfigApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ClusterTrustBundleProjection"):
		return &applyconfigurationscorev1.ClusterTrustBundleProjectionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ComponentCondition"):
		return &applyconfigurationscorev1.ComponentConditionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ComponentStatus"):
		return &applyconfigurationscorev1.ComponentStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ConfigMap"):
		return &applyconfigurationscorev1.ConfigMapApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ConfigMapEnvSource"):
		return &applyconfigurationscorev1.ConfigMapEnvSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ConfigMapKeySelector"):
		return &applyconfigurationscorev1.ConfigMapKeySelectorApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ConfigMapNodeConfigSource"):
		return &applyconfigurationscorev1.ConfigMapNodeConfigSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ConfigMapProjection"):
		return &applyconfigurationscorev1.ConfigMapProjectionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ConfigMapVolumeSource"):
		return &applyconfigurationscorev1.ConfigMapVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Container"):
		return &applyconfigurationscorev1.ContainerApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerImage"):
		return &applyconfigurationscorev1.ContainerImageApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerPort"):
		return &applyconfigurationscorev1.ContainerPortApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerResizePolicy"):
		return &applyconfigurationscorev1.ContainerResizePolicyApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerState"):
		return &applyconfigurationscorev1.ContainerStateApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerStateRunning"):
		return &applyconfigurationscorev1.ContainerStateRunningApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerStateTerminated"):
		return &applyconfigurationscorev1.ContainerStateTerminatedApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerStateWaiting"):
		return &applyconfigurationscorev1.ContainerStateWaitingApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerStatus"):
		return &applyconfigurationscorev1.ContainerStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ContainerUser"):
		return &applyconfigurationscorev1.ContainerUserApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("CSIPersistentVolumeSource"):
		return &applyconfigurationscorev1.CSIPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("CSIVolumeSource"):
		return &applyconfigurationscorev1.CSIVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("DaemonEndpoint"):
		return &applyconfigurationscorev1.DaemonEndpointApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("DownwardAPIProjection"):
		return &applyconfigurationscorev1.DownwardAPIProjectionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("DownwardAPIVolumeFile"):
		return &applyconfigurationscorev1.DownwardAPIVolumeFileApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("DownwardAPIVolumeSource"):
		return &applyconfigurationscorev1.DownwardAPIVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EmptyDirVolumeSource"):
		return &applyconfigurationscorev1.EmptyDirVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EndpointAddress"):
		return &applyconfigurationscorev1.EndpointAddressApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EndpointPort"):
		return &applyconfigurationscorev1.EndpointPortApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Endpoints"):
		return &applyconfigurationscorev1.EndpointsApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EndpointSubset"):
		return &applyconfigurationscorev1.EndpointSubsetApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EnvFromSource"):
		return &applyconfigurationscorev1.EnvFromSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EnvVar"):
		return &applyconfigurationscorev1.EnvVarApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EnvVarSource"):
		return &applyconfigurationscorev1.EnvVarSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EphemeralContainer"):
		return &applyconfigurationscorev1.EphemeralContainerApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EphemeralContainerCommon"):
		return &applyconfigurationscorev1.EphemeralContainerCommonApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EphemeralVolumeSource"):
		return &applyconfigurationscorev1.EphemeralVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Event"):
		return &applyconfigurationscorev1.EventApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EventSeries"):
		return &applyconfigurationscorev1.EventSeriesApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("EventSource"):
		return &applyconfigurationscorev1.EventSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ExecAction"):
		return &applyconfigurationscorev1.ExecActionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("FCVolumeSource"):
		return &applyconfigurationscorev1.FCVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("FileKeySelector"):
		return &applyconfigurationscorev1.FileKeySelectorApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("FlexPersistentVolumeSource"):
		return &applyconfigurationscorev1.FlexPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("FlexVolumeSource"):
		return &applyconfigurationscorev1.FlexVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("FlockerVolumeSource"):
		return &applyconfigurationscorev1.FlockerVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("GCEPersistentDiskVolumeSource"):
		return &applyconfigurationscorev1.GCEPersistentDiskVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("GitRepoVolumeSource"):
		return &applyconfigurationscorev1.GitRepoVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("GlusterfsPersistentVolumeSource"):
		return &applyconfigurationscorev1.GlusterfsPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("GlusterfsVolumeSource"):
		return &applyconfigurationscorev1.GlusterfsVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("GRPCAction"):
		return &applyconfigurationscorev1.GRPCActionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("HostAlias"):
		return &applyconfigurationscorev1.HostAliasApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("HostIP"):
		return &applyconfigurationscorev1.HostIPApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("HostPathVolumeSource"):
		return &applyconfigurationscorev1.HostPathVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("HTTPGetAction"):
		return &applyconfigurationscorev1.HTTPGetActionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("HTTPHeader"):
		return &applyconfigurationscorev1.HTTPHeaderApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ImageVolumeSource"):
		return &applyconfigurationscorev1.ImageVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ISCSIPersistentVolumeSource"):
		return &applyconfigurationscorev1.ISCSIPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ISCSIVolumeSource"):
		return &applyconfigurationscorev1.ISCSIVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("KeyToPath"):
		return &applyconfigurationscorev1.KeyToPathApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Lifecycle"):
		return &applyconfigurationscorev1.LifecycleApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LifecycleHandler"):
		return &applyconfigurationscorev1.LifecycleHandlerApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LimitRange"):
		return &applyconfigurationscorev1.LimitRangeApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LimitRangeItem"):
		return &applyconfigurationscorev1.LimitRangeItemApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LimitRangeSpec"):
		return &applyconfigurationscorev1.LimitRangeSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LinuxContainerUser"):
		return &applyconfigurationscorev1.LinuxContainerUserApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LoadBalancerIngress"):
		return &applyconfigurationscorev1.LoadBalancerIngressApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LoadBalancerStatus"):
		return &applyconfigurationscorev1.LoadBalancerStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LocalObjectReference"):
		return &applyconfigurationscorev1.LocalObjectReferenceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("LocalVolumeSource"):
		return &applyconfigurationscorev1.LocalVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ModifyVolumeStatus"):
		return &applyconfigurationscorev1.ModifyVolumeStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Namespace"):
		return &applyconfigurationscorev1.NamespaceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NamespaceCondition"):
		return &applyconfigurationscorev1.NamespaceConditionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NamespaceSpec"):
		return &applyconfigurationscorev1.NamespaceSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NamespaceStatus"):
		return &applyconfigurationscorev1.NamespaceStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NFSVolumeSource"):
		return &applyconfigurationscorev1.NFSVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Node"):
		return &applyconfigurationscorev1.NodeApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeAddress"):
		return &applyconfigurationscorev1.NodeAddressApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeAffinity"):
		return &applyconfigurationscorev1.NodeAffinityApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeCondition"):
		return &applyconfigurationscorev1.NodeConditionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeConfigSource"):
		return &applyconfigurationscorev1.NodeConfigSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeConfigStatus"):
		return &applyconfigurationscorev1.NodeConfigStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeDaemonEndpoints"):
		return &applyconfigurationscorev1.NodeDaemonEndpointsApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeFeatures"):
		return &applyconfigurationscorev1.NodeFeaturesApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeRuntimeHandler"):
		return &applyconfigurationscorev1.NodeRuntimeHandlerApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeRuntimeHandlerFeatures"):
		return &applyconfigurationscorev1.NodeRuntimeHandlerFeaturesApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeSelector"):
		return &applyconfigurationscorev1.NodeSelectorApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeSelectorRequirement"):
		return &applyconfigurationscorev1.NodeSelectorRequirementApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeSelectorTerm"):
		return &applyconfigurationscorev1.NodeSelectorTermApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeSpec"):
		return &applyconfigurationscorev1.NodeSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeStatus"):
		return &applyconfigurationscorev1.NodeStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeSwapStatus"):
		return &applyconfigurationscorev1.NodeSwapStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("NodeSystemInfo"):
		return &applyconfigurationscorev1.NodeSystemInfoApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ObjectFieldSelector"):
		return &applyconfigurationscorev1.ObjectFieldSelectorApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ObjectReference"):
		return &applyconfigurationscorev1.ObjectReferenceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolume"):
		return &applyconfigurationscorev1.PersistentVolumeApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaim"):
		return &applyconfigurationscorev1.PersistentVolumeClaimApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaimCondition"):
		return &applyconfigurationscorev1.PersistentVolumeClaimConditionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaimSpec"):
		return &applyconfigurationscorev1.PersistentVolumeClaimSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaimStatus"):
		return &applyconfigurationscorev1.PersistentVolumeClaimStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaimTemplate"):
		return &applyconfigurationscorev1.PersistentVolumeClaimTemplateApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaimVolumeSource"):
		return &applyconfigurationscorev1.PersistentVolumeClaimVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeSource"):
		return &applyconfigurationscorev1.PersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeSpec"):
		return &applyconfigurationscorev1.PersistentVolumeSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeStatus"):
		return &applyconfigurationscorev1.PersistentVolumeStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PhotonPersistentDiskVolumeSource"):
		return &applyconfigurationscorev1.PhotonPersistentDiskVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Pod"):
		return &applyconfigurationscorev1.PodApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodAffinity"):
		return &applyconfigurationscorev1.PodAffinityApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodAffinityTerm"):
		return &applyconfigurationscorev1.PodAffinityTermApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodAntiAffinity"):
		return &applyconfigurationscorev1.PodAntiAffinityApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodCertificateProjection"):
		return &applyconfigurationscorev1.PodCertificateProjectionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodCondition"):
		return &applyconfigurationscorev1.PodConditionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodDNSConfig"):
		return &applyconfigurationscorev1.PodDNSConfigApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodDNSConfigOption"):
		return &applyconfigurationscorev1.PodDNSConfigOptionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodIP"):
		return &applyconfigurationscorev1.PodIPApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodOS"):
		return &applyconfigurationscorev1.PodOSApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodReadinessGate"):
		return &applyconfigurationscorev1.PodReadinessGateApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodResourceClaim"):
		return &applyconfigurationscorev1.PodResourceClaimApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodResourceClaimStatus"):
		return &applyconfigurationscorev1.PodResourceClaimStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodSchedulingGate"):
		return &applyconfigurationscorev1.PodSchedulingGateApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodSecurityContext"):
		return &applyconfigurationscorev1.PodSecurityContextApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodSpec"):
		return &applyconfigurationscorev1.PodSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodStatus"):
		return &applyconfigurationscorev1.PodStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodTemplate"):
		return &applyconfigurationscorev1.PodTemplateApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PodTemplateSpec"):
		return &applyconfigurationscorev1.PodTemplateSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PortStatus"):
		return &applyconfigurationscorev1.PortStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PortworxVolumeSource"):
		return &applyconfigurationscorev1.PortworxVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("PreferredSchedulingTerm"):
		return &applyconfigurationscorev1.PreferredSchedulingTermApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Probe"):
		return &applyconfigurationscorev1.ProbeApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ProbeHandler"):
		return &applyconfigurationscorev1.ProbeHandlerApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ProjectedVolumeSource"):
		return &applyconfigurationscorev1.ProjectedVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("QuobyteVolumeSource"):
		return &applyconfigurationscorev1.QuobyteVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("RBDPersistentVolumeSource"):
		return &applyconfigurationscorev1.RBDPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("RBDVolumeSource"):
		return &applyconfigurationscorev1.RBDVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ReplicationController"):
		return &applyconfigurationscorev1.ReplicationControllerApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ReplicationControllerCondition"):
		return &applyconfigurationscorev1.ReplicationControllerConditionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ReplicationControllerSpec"):
		return &applyconfigurationscorev1.ReplicationControllerSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ReplicationControllerStatus"):
		return &applyconfigurationscorev1.ReplicationControllerStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceClaim"):
		return &applyconfigurationscorev1.ResourceClaimApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceFieldSelector"):
		return &applyconfigurationscorev1.ResourceFieldSelectorApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceHealth"):
		return &applyconfigurationscorev1.ResourceHealthApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceQuota"):
		return &applyconfigurationscorev1.ResourceQuotaApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceQuotaSpec"):
		return &applyconfigurationscorev1.ResourceQuotaSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceQuotaStatus"):
		return &applyconfigurationscorev1.ResourceQuotaStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceRequirements"):
		return &applyconfigurationscorev1.ResourceRequirementsApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ResourceStatus"):
		return &applyconfigurationscorev1.ResourceStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ScaleIOPersistentVolumeSource"):
		return &applyconfigurationscorev1.ScaleIOPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ScaleIOVolumeSource"):
		return &applyconfigurationscorev1.ScaleIOVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ScopedResourceSelectorRequirement"):
		return &applyconfigurationscorev1.ScopedResourceSelectorRequirementApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ScopeSelector"):
		return &applyconfigurationscorev1.ScopeSelectorApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SeccompProfile"):
		return &applyconfigurationscorev1.SeccompProfileApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Secret"):
		return &applyconfigurationscorev1.SecretApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SecretEnvSource"):
		return &applyconfigurationscorev1.SecretEnvSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SecretKeySelector"):
		return &applyconfigurationscorev1.SecretKeySelectorApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SecretProjection"):
		return &applyconfigurationscorev1.SecretProjectionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SecretReference"):
		return &applyconfigurationscorev1.SecretReferenceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SecretVolumeSource"):
		return &applyconfigurationscorev1.SecretVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SecurityContext"):
		return &applyconfigurationscorev1.SecurityContextApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SELinuxOptions"):
		return &applyconfigurationscorev1.SELinuxOptionsApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Service"):
		return &applyconfigurationscorev1.ServiceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ServiceAccount"):
		return &applyconfigurationscorev1.ServiceAccountApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ServiceAccountTokenProjection"):
		return &applyconfigurationscorev1.ServiceAccountTokenProjectionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ServicePort"):
		return &applyconfigurationscorev1.ServicePortApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ServiceSpec"):
		return &applyconfigurationscorev1.ServiceSpecApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("ServiceStatus"):
		return &applyconfigurationscorev1.ServiceStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SessionAffinityConfig"):
		return &applyconfigurationscorev1.SessionAffinityConfigApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("SleepAction"):
		return &applyconfigurationscorev1.SleepActionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("StorageOSPersistentVolumeSource"):
		return &applyconfigurationscorev1.StorageOSPersistentVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("StorageOSVolumeSource"):
		return &applyconfigurationscorev1.StorageOSVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Sysctl"):
		return &applyconfigurationscorev1.SysctlApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Taint"):
		return &applyconfigurationscorev1.TaintApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("TCPSocketAction"):
		return &applyconfigurationscorev1.TCPSocketActionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Toleration"):
		return &applyconfigurationscorev1.TolerationApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("TopologySelectorLabelRequirement"):
		return &applyconfigurationscorev1.TopologySelectorLabelRequirementApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("TopologySelectorTerm"):
		return &applyconfigurationscorev1.TopologySelectorTermApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("TopologySpreadConstraint"):
		return &applyconfigurationscorev1.TopologySpreadConstraintApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("TypedLocalObjectReference"):
		return &applyconfigurationscorev1.TypedLocalObjectReferenceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("TypedObjectReference"):
		return &applyconfigurationscorev1.TypedObjectReferenceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("Volume"):
		return &applyconfigurationscorev1.VolumeApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VolumeDevice"):
		return &applyconfigurationscorev1.VolumeDeviceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VolumeMount"):
		return &applyconfigurationscorev1.VolumeMountApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VolumeMountStatus"):
		return &applyconfigurationscorev1.VolumeMountStatusApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VolumeNodeAffinity"):
		return &applyconfigurationscorev1.VolumeNodeAffinityApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VolumeProjection"):
		return &applyconfigurationscorev1.VolumeProjectionApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VolumeResourceRequirements"):
		return &applyconfigurationscorev1.VolumeResourceRequirementsApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VolumeSource"):
		return &applyconfigurationscorev1.VolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("VsphereVirtualDiskVolumeSource"):
		return &applyconfigurationscorev1.VsphereVirtualDiskVolumeSourceApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("WeightedPodAffinityTerm"):
		return &applyconfigurationscorev1.WeightedPodAffinityTermApplyConfiguration{}
	case corev1.SchemeGroupVersion.WithKind("WindowsSecurityContextOptions"):
		return &applyconfigurationscorev1.WindowsSecurityContextOptionsApplyConfiguration{}

		// Group=discovery.k8s.io, Version=v1
	case discoveryv1.SchemeGroupVersion.WithKind("Endpoint"):
		return &applyconfigurationsdiscoveryv1.EndpointApplyConfiguration{}
	case discoveryv1.SchemeGroupVersion.WithKind("EndpointConditions"):
		return &applyconfigurationsdiscoveryv1.EndpointConditionsApplyConfiguration{}
	case discoveryv1.SchemeGroupVersion.WithKind("EndpointHints"):
		return &applyconfigurationsdiscoveryv1.EndpointHintsApplyConfiguration{}
	case discoveryv1.SchemeGroupVersion.WithKind("EndpointPort"):
		return &applyconfigurationsdiscoveryv1.EndpointPortApplyConfiguration{}
	case discoveryv1.SchemeGroupVersion.WithKind("EndpointSlice"):
		return &applyconfigurationsdiscoveryv1.EndpointSliceApplyConfiguration{}
	case discoveryv1.SchemeGroupVersion.WithKind("ForNode"):
		return &applyconfigurationsdiscoveryv1.ForNodeApplyConfiguration{}
	case discoveryv1.SchemeGroupVersion.WithKind("ForZone"):
		return &applyconfigurationsdiscoveryv1.ForZoneApplyConfiguration{}

		// Group=discovery.k8s.io, Version=v1beta1
	case discoveryv1beta1.SchemeGroupVersion.WithKind("Endpoint"):
		return &applyconfigurationsdiscoveryv1beta1.EndpointApplyConfiguration{}
	case discoveryv1beta1.SchemeGroupVersion.WithKind("EndpointConditions"):
		return &applyconfigurationsdiscoveryv1beta1.EndpointConditionsApplyConfiguration{}
	case discoveryv1beta1.SchemeGroupVersion.WithKind("EndpointHints"):
		return &applyconfigurationsdiscoveryv1beta1.EndpointHintsApplyConfiguration{}
	case discoveryv1beta1.SchemeGroupVersion.WithKind("EndpointPort"):
		return &applyconfigurationsdiscoveryv1beta1.EndpointPortApplyConfiguration{}
	case discoveryv1beta1.SchemeGroupVersion.WithKind("EndpointSlice"):
		return &applyconfigurationsdiscoveryv1beta1.EndpointSliceApplyConfiguration{}
	case discoveryv1beta1.SchemeGroupVersion.WithKind("ForNode"):
		return &applyconfigurationsdiscoveryv1beta1.ForNodeApplyConfiguration{}
	case discoveryv1beta1.SchemeGroupVersion.WithKind("ForZone"):
		return &applyconfigurationsdiscoveryv1beta1.ForZoneApplyConfiguration{}

		// Group=events.k8s.io, Version=v1
	case eventsv1.SchemeGroupVersion.WithKind("Event"):
		return &applyconfigurationseventsv1.EventApplyConfiguration{}
	case eventsv1.SchemeGroupVersion.WithKind("EventSeries"):
		return &applyconfigurationseventsv1.EventSeriesApplyConfiguration{}

		// Group=events.k8s.io, Version=v1beta1
	case eventsv1beta1.SchemeGroupVersion.WithKind("Event"):
		return &applyconfigurationseventsv1beta1.EventApplyConfiguration{}
	case eventsv1beta1.SchemeGroupVersion.WithKind("EventSeries"):
		return &applyconfigurationseventsv1beta1.EventSeriesApplyConfiguration{}

		// Group=extensions, Version=v1beta1
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DaemonSet"):
		return &applyconfigurationsextensionsv1beta1.DaemonSetApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DaemonSetCondition"):
		return &applyconfigurationsextensionsv1beta1.DaemonSetConditionApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DaemonSetSpec"):
		return &applyconfigurationsextensionsv1beta1.DaemonSetSpecApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DaemonSetStatus"):
		return &applyconfigurationsextensionsv1beta1.DaemonSetStatusApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DaemonSetUpdateStrategy"):
		return &applyconfigurationsextensionsv1beta1.DaemonSetUpdateStrategyApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("Deployment"):
		return &applyconfigurationsextensionsv1beta1.DeploymentApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DeploymentCondition"):
		return &applyconfigurationsextensionsv1beta1.DeploymentConditionApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DeploymentSpec"):
		return &applyconfigurationsextensionsv1beta1.DeploymentSpecApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DeploymentStatus"):
		return &applyconfigurationsextensionsv1beta1.DeploymentStatusApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("DeploymentStrategy"):
		return &applyconfigurationsextensionsv1beta1.DeploymentStrategyApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("HTTPIngressPath"):
		return &applyconfigurationsextensionsv1beta1.HTTPIngressPathApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("HTTPIngressRuleValue"):
		return &applyconfigurationsextensionsv1beta1.HTTPIngressRuleValueApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("Ingress"):
		return &applyconfigurationsextensionsv1beta1.IngressApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressBackend"):
		return &applyconfigurationsextensionsv1beta1.IngressBackendApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressLoadBalancerIngress"):
		return &applyconfigurationsextensionsv1beta1.IngressLoadBalancerIngressApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressLoadBalancerStatus"):
		return &applyconfigurationsextensionsv1beta1.IngressLoadBalancerStatusApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressPortStatus"):
		return &applyconfigurationsextensionsv1beta1.IngressPortStatusApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressRule"):
		return &applyconfigurationsextensionsv1beta1.IngressRuleApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressRuleValue"):
		return &applyconfigurationsextensionsv1beta1.IngressRuleValueApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressSpec"):
		return &applyconfigurationsextensionsv1beta1.IngressSpecApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressStatus"):
		return &applyconfigurationsextensionsv1beta1.IngressStatusApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IngressTLS"):
		return &applyconfigurationsextensionsv1beta1.IngressTLSApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("IPBlock"):
		return &applyconfigurationsextensionsv1beta1.IPBlockApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("NetworkPolicy"):
		return &applyconfigurationsextensionsv1beta1.NetworkPolicyApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("NetworkPolicyEgressRule"):
		return &applyconfigurationsextensionsv1beta1.NetworkPolicyEgressRuleApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("NetworkPolicyIngressRule"):
		return &applyconfigurationsextensionsv1beta1.NetworkPolicyIngressRuleApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("NetworkPolicyPeer"):
		return &applyconfigurationsextensionsv1beta1.NetworkPolicyPeerApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("NetworkPolicyPort"):
		return &applyconfigurationsextensionsv1beta1.NetworkPolicyPortApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("NetworkPolicySpec"):
		return &applyconfigurationsextensionsv1beta1.NetworkPolicySpecApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("ReplicaSet"):
		return &applyconfigurationsextensionsv1beta1.ReplicaSetApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("ReplicaSetCondition"):
		return &applyconfigurationsextensionsv1beta1.ReplicaSetConditionApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("ReplicaSetSpec"):
		return &applyconfigurationsextensionsv1beta1.ReplicaSetSpecApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("ReplicaSetStatus"):
		return &applyconfigurationsextensionsv1beta1.ReplicaSetStatusApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("RollbackConfig"):
		return &applyconfigurationsextensionsv1beta1.RollbackConfigApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("RollingUpdateDaemonSet"):
		return &applyconfigurationsextensionsv1beta1.RollingUpdateDaemonSetApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("RollingUpdateDeployment"):
		return &applyconfigurationsextensionsv1beta1.RollingUpdateDeploymentApplyConfiguration{}
	case extensionsv1beta1.SchemeGroupVersion.WithKind("Scale"):
		return &applyconfigurationsextensionsv1beta1.ScaleApplyConfiguration{}

		// Group=flowcontrol.apiserver.k8s.io, Version=v1
	case flowcontrolv1.SchemeGroupVersion.WithKind("ExemptPriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1.ExemptPriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("FlowDistinguisherMethod"):
		return &applyconfigurationsflowcontrolv1.FlowDistinguisherMethodApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("FlowSchema"):
		return &applyconfigurationsflowcontrolv1.FlowSchemaApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("FlowSchemaCondition"):
		return &applyconfigurationsflowcontrolv1.FlowSchemaConditionApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("FlowSchemaSpec"):
		return &applyconfigurationsflowcontrolv1.FlowSchemaSpecApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("FlowSchemaStatus"):
		return &applyconfigurationsflowcontrolv1.FlowSchemaStatusApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("GroupSubject"):
		return &applyconfigurationsflowcontrolv1.GroupSubjectApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("LimitedPriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1.LimitedPriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("LimitResponse"):
		return &applyconfigurationsflowcontrolv1.LimitResponseApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("NonResourcePolicyRule"):
		return &applyconfigurationsflowcontrolv1.NonResourcePolicyRuleApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("PolicyRulesWithSubjects"):
		return &applyconfigurationsflowcontrolv1.PolicyRulesWithSubjectsApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("PriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1.PriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationCondition"):
		return &applyconfigurationsflowcontrolv1.PriorityLevelConfigurationConditionApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationReference"):
		return &applyconfigurationsflowcontrolv1.PriorityLevelConfigurationReferenceApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationSpec"):
		return &applyconfigurationsflowcontrolv1.PriorityLevelConfigurationSpecApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationStatus"):
		return &applyconfigurationsflowcontrolv1.PriorityLevelConfigurationStatusApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("QueuingConfiguration"):
		return &applyconfigurationsflowcontrolv1.QueuingConfigurationApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("ResourcePolicyRule"):
		return &applyconfigurationsflowcontrolv1.ResourcePolicyRuleApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("ServiceAccountSubject"):
		return &applyconfigurationsflowcontrolv1.ServiceAccountSubjectApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("Subject"):
		return &applyconfigurationsflowcontrolv1.SubjectApplyConfiguration{}
	case flowcontrolv1.SchemeGroupVersion.WithKind("UserSubject"):
		return &applyconfigurationsflowcontrolv1.UserSubjectApplyConfiguration{}

		// Group=flowcontrol.apiserver.k8s.io, Version=v1beta1
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("ExemptPriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1beta1.ExemptPriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("FlowDistinguisherMethod"):
		return &applyconfigurationsflowcontrolv1beta1.FlowDistinguisherMethodApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("FlowSchema"):
		return &applyconfigurationsflowcontrolv1beta1.FlowSchemaApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("FlowSchemaCondition"):
		return &applyconfigurationsflowcontrolv1beta1.FlowSchemaConditionApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("FlowSchemaSpec"):
		return &applyconfigurationsflowcontrolv1beta1.FlowSchemaSpecApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("FlowSchemaStatus"):
		return &applyconfigurationsflowcontrolv1beta1.FlowSchemaStatusApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("GroupSubject"):
		return &applyconfigurationsflowcontrolv1beta1.GroupSubjectApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("LimitedPriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1beta1.LimitedPriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("LimitResponse"):
		return &applyconfigurationsflowcontrolv1beta1.LimitResponseApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("NonResourcePolicyRule"):
		return &applyconfigurationsflowcontrolv1beta1.NonResourcePolicyRuleApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("PolicyRulesWithSubjects"):
		return &applyconfigurationsflowcontrolv1beta1.PolicyRulesWithSubjectsApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("PriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1beta1.PriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationCondition"):
		return &applyconfigurationsflowcontrolv1beta1.PriorityLevelConfigurationConditionApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationReference"):
		return &applyconfigurationsflowcontrolv1beta1.PriorityLevelConfigurationReferenceApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationSpec"):
		return &applyconfigurationsflowcontrolv1beta1.PriorityLevelConfigurationSpecApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("PriorityLevelConfigurationStatus"):
		return &applyconfigurationsflowcontrolv1beta1.PriorityLevelConfigurationStatusApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("QueuingConfiguration"):
		return &applyconfigurationsflowcontrolv1beta1.QueuingConfigurationApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("ResourcePolicyRule"):
		return &applyconfigurationsflowcontrolv1beta1.ResourcePolicyRuleApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("ServiceAccountSubject"):
		return &applyconfigurationsflowcontrolv1beta1.ServiceAccountSubjectApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("Subject"):
		return &applyconfigurationsflowcontrolv1beta1.SubjectApplyConfiguration{}
	case flowcontrolv1beta1.SchemeGroupVersion.WithKind("UserSubject"):
		return &applyconfigurationsflowcontrolv1beta1.UserSubjectApplyConfiguration{}

		// Group=flowcontrol.apiserver.k8s.io, Version=v1beta2
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("ExemptPriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1beta2.ExemptPriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("FlowDistinguisherMethod"):
		return &applyconfigurationsflowcontrolv1beta2.FlowDistinguisherMethodApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("FlowSchema"):
		return &applyconfigurationsflowcontrolv1beta2.FlowSchemaApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("FlowSchemaCondition"):
		return &applyconfigurationsflowcontrolv1beta2.FlowSchemaConditionApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("FlowSchemaSpec"):
		return &applyconfigurationsflowcontrolv1beta2.FlowSchemaSpecApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("FlowSchemaStatus"):
		return &applyconfigurationsflowcontrolv1beta2.FlowSchemaStatusApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("GroupSubject"):
		return &applyconfigurationsflowcontrolv1beta2.GroupSubjectApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("LimitedPriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1beta2.LimitedPriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("LimitResponse"):
		return &applyconfigurationsflowcontrolv1beta2.LimitResponseApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("NonResourcePolicyRule"):
		return &applyconfigurationsflowcontrolv1beta2.NonResourcePolicyRuleApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("PolicyRulesWithSubjects"):
		return &applyconfigurationsflowcontrolv1beta2.PolicyRulesWithSubjectsApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("PriorityLevelConfiguration"):
		return &applyconfigurationsflowcontrolv1beta2.PriorityLevelConfigurationApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("PriorityLevelConfigurationCondition"):
		return &applyconfigurationsflowcontrolv1beta2.PriorityLevelConfigurationConditionApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("PriorityLevelConfigurationReference"):
		return &applyconfigurationsflowcontrolv1beta2.PriorityLevelConfigurationReferenceApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("PriorityLevelConfigurationSpec"):
		return &applyconfigurationsflowcontrolv1beta2.PriorityLevelConfigurationSpecApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("PriorityLevelConfigurationStatus"):
		return &applyconfigurationsflowcontrolv1beta2.PriorityLevelConfigurationStatusApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("QueuingConfiguration"):
		return &applyconfigurationsflowcontrolv1beta2.QueuingConfigurationApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("ResourcePolicyRule"):
		return &applyconfigurationsflowcontrolv1beta2.ResourcePolicyRuleApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("ServiceAccountSubject"):
		return &applyconfigurationsflowcontrolv1beta2.ServiceAccountSubjectApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("Subject"):
		return &applyconfigurationsflowcontrolv1beta2.SubjectApplyConfiguration{}
	case flowcontrolv1beta2.SchemeGroupVersion.WithKind("UserSubject"):
		return &applyconfigurationsflowcontrolv1beta2.UserSubjectApplyConfiguration{}

		// Group=flowcontrol.apiserver.k8s.io, Version=v1beta3
	case v1beta3.SchemeGroupVersion.WithKind("ExemptPriorityLevelConfiguration"):
		return &flowcontrolv1beta3.ExemptPriorityLevelConfigurationApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("FlowDistinguisherMethod"):
		return &flowcontrolv1beta3.FlowDistinguisherMethodApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("FlowSchema"):
		return &flowcontrolv1beta3.FlowSchemaApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("FlowSchemaCondition"):
		return &flowcontrolv1beta3.FlowSchemaConditionApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("FlowSchemaSpec"):
		return &flowcontrolv1beta3.FlowSchemaSpecApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("FlowSchemaStatus"):
		return &flowcontrolv1beta3.FlowSchemaStatusApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("GroupSubject"):
		return &flowcontrolv1beta3.GroupSubjectApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("LimitedPriorityLevelConfiguration"):
		return &flowcontrolv1beta3.LimitedPriorityLevelConfigurationApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("LimitResponse"):
		return &flowcontrolv1beta3.LimitResponseApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("NonResourcePolicyRule"):
		return &flowcontrolv1beta3.NonResourcePolicyRuleApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("PolicyRulesWithSubjects"):
		return &flowcontrolv1beta3.PolicyRulesWithSubjectsApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("PriorityLevelConfiguration"):
		return &flowcontrolv1beta3.PriorityLevelConfigurationApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("PriorityLevelConfigurationCondition"):
		return &flowcontrolv1beta3.PriorityLevelConfigurationConditionApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("PriorityLevelConfigurationReference"):
		return &flowcontrolv1beta3.PriorityLevelConfigurationReferenceApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("PriorityLevelConfigurationSpec"):
		return &flowcontrolv1beta3.PriorityLevelConfigurationSpecApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("PriorityLevelConfigurationStatus"):
		return &flowcontrolv1beta3.PriorityLevelConfigurationStatusApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("QueuingConfiguration"):
		return &flowcontrolv1beta3.QueuingConfigurationApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("ResourcePolicyRule"):
		return &flowcontrolv1beta3.ResourcePolicyRuleApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("ServiceAccountSubject"):
		return &flowcontrolv1beta3.ServiceAccountSubjectApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("Subject"):
		return &flowcontrolv1beta3.SubjectApplyConfiguration{}
	case v1beta3.SchemeGroupVersion.WithKind("UserSubject"):
		return &flowcontrolv1beta3.UserSubjectApplyConfiguration{}

		// Group=imagepolicy.k8s.io, Version=v1alpha1
	case imagepolicyv1alpha1.SchemeGroupVersion.WithKind("ImageReview"):
		return &applyconfigurationsimagepolicyv1alpha1.ImageReviewApplyConfiguration{}
	case imagepolicyv1alpha1.SchemeGroupVersion.WithKind("ImageReviewContainerSpec"):
		return &applyconfigurationsimagepolicyv1alpha1.ImageReviewContainerSpecApplyConfiguration{}
	case imagepolicyv1alpha1.SchemeGroupVersion.WithKind("ImageReviewSpec"):
		return &applyconfigurationsimagepolicyv1alpha1.ImageReviewSpecApplyConfiguration{}
	case imagepolicyv1alpha1.SchemeGroupVersion.WithKind("ImageReviewStatus"):
		return &applyconfigurationsimagepolicyv1alpha1.ImageReviewStatusApplyConfiguration{}

		// Group=internal.apiserver.k8s.io, Version=v1alpha1
	case apiserverinternalv1alpha1.SchemeGroupVersion.WithKind("ServerStorageVersion"):
		return &applyconfigurationsapiserverinternalv1alpha1.ServerStorageVersionApplyConfiguration{}
	case apiserverinternalv1alpha1.SchemeGroupVersion.WithKind("StorageVersion"):
		return &applyconfigurationsapiserverinternalv1alpha1.StorageVersionApplyConfiguration{}
	case apiserverinternalv1alpha1.SchemeGroupVersion.WithKind("StorageVersionCondition"):
		return &applyconfigurationsapiserverinternalv1alpha1.StorageVersionConditionApplyConfiguration{}
	case apiserverinternalv1alpha1.SchemeGroupVersion.WithKind("StorageVersionStatus"):
		return &applyconfigurationsapiserverinternalv1alpha1.StorageVersionStatusApplyConfiguration{}

		// Group=meta.k8s.io, Version=v1
	case metav1.SchemeGroupVersion.WithKind("Condition"):
		return &applyconfigurationsmetav1.ConditionApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("DeleteOptions"):
		return &applyconfigurationsmetav1.DeleteOptionsApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("LabelSelector"):
		return &applyconfigurationsmetav1.LabelSelectorApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("LabelSelectorRequirement"):
		return &applyconfigurationsmetav1.LabelSelectorRequirementApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("ManagedFieldsEntry"):
		return &applyconfigurationsmetav1.ManagedFieldsEntryApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("ObjectMeta"):
		return &applyconfigurationsmetav1.ObjectMetaApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("OwnerReference"):
		return &applyconfigurationsmetav1.OwnerReferenceApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("Preconditions"):
		return &applyconfigurationsmetav1.PreconditionsApplyConfiguration{}
	case metav1.SchemeGroupVersion.WithKind("TypeMeta"):
		return &applyconfigurationsmetav1.TypeMetaApplyConfiguration{}

		// Group=networking.k8s.io, Version=v1
	case networkingv1.SchemeGroupVersion.WithKind("HTTPIngressPath"):
		return &applyconfigurationsnetworkingv1.HTTPIngressPathApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("HTTPIngressRuleValue"):
		return &applyconfigurationsnetworkingv1.HTTPIngressRuleValueApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("Ingress"):
		return &applyconfigurationsnetworkingv1.IngressApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressBackend"):
		return &applyconfigurationsnetworkingv1.IngressBackendApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressClass"):
		return &applyconfigurationsnetworkingv1.IngressClassApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressClassParametersReference"):
		return &applyconfigurationsnetworkingv1.IngressClassParametersReferenceApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressClassSpec"):
		return &applyconfigurationsnetworkingv1.IngressClassSpecApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressLoadBalancerIngress"):
		return &applyconfigurationsnetworkingv1.IngressLoadBalancerIngressApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressLoadBalancerStatus"):
		return &applyconfigurationsnetworkingv1.IngressLoadBalancerStatusApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressPortStatus"):
		return &applyconfigurationsnetworkingv1.IngressPortStatusApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressRule"):
		return &applyconfigurationsnetworkingv1.IngressRuleApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressRuleValue"):
		return &applyconfigurationsnetworkingv1.IngressRuleValueApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressServiceBackend"):
		return &applyconfigurationsnetworkingv1.IngressServiceBackendApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressSpec"):
		return &applyconfigurationsnetworkingv1.IngressSpecApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressStatus"):
		return &applyconfigurationsnetworkingv1.IngressStatusApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IngressTLS"):
		return &applyconfigurationsnetworkingv1.IngressTLSApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IPAddress"):
		return &applyconfigurationsnetworkingv1.IPAddressApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IPAddressSpec"):
		return &applyconfigurationsnetworkingv1.IPAddressSpecApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("IPBlock"):
		return &applyconfigurationsnetworkingv1.IPBlockApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("NetworkPolicy"):
		return &applyconfigurationsnetworkingv1.NetworkPolicyApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("NetworkPolicyEgressRule"):
		return &applyconfigurationsnetworkingv1.NetworkPolicyEgressRuleApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("NetworkPolicyIngressRule"):
		return &applyconfigurationsnetworkingv1.NetworkPolicyIngressRuleApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("NetworkPolicyPeer"):
		return &applyconfigurationsnetworkingv1.NetworkPolicyPeerApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("NetworkPolicyPort"):
		return &applyconfigurationsnetworkingv1.NetworkPolicyPortApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("NetworkPolicySpec"):
		return &applyconfigurationsnetworkingv1.NetworkPolicySpecApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("ParentReference"):
		return &applyconfigurationsnetworkingv1.ParentReferenceApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("ServiceBackendPort"):
		return &applyconfigurationsnetworkingv1.ServiceBackendPortApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("ServiceCIDR"):
		return &applyconfigurationsnetworkingv1.ServiceCIDRApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("ServiceCIDRSpec"):
		return &applyconfigurationsnetworkingv1.ServiceCIDRSpecApplyConfiguration{}
	case networkingv1.SchemeGroupVersion.WithKind("ServiceCIDRStatus"):
		return &applyconfigurationsnetworkingv1.ServiceCIDRStatusApplyConfiguration{}

		// Group=networking.k8s.io, Version=v1beta1
	case networkingv1beta1.SchemeGroupVersion.WithKind("HTTPIngressPath"):
		return &applyconfigurationsnetworkingv1beta1.HTTPIngressPathApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("HTTPIngressRuleValue"):
		return &applyconfigurationsnetworkingv1beta1.HTTPIngressRuleValueApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("Ingress"):
		return &applyconfigurationsnetworkingv1beta1.IngressApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressBackend"):
		return &applyconfigurationsnetworkingv1beta1.IngressBackendApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressClass"):
		return &applyconfigurationsnetworkingv1beta1.IngressClassApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressClassParametersReference"):
		return &applyconfigurationsnetworkingv1beta1.IngressClassParametersReferenceApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressClassSpec"):
		return &applyconfigurationsnetworkingv1beta1.IngressClassSpecApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressLoadBalancerIngress"):
		return &applyconfigurationsnetworkingv1beta1.IngressLoadBalancerIngressApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressLoadBalancerStatus"):
		return &applyconfigurationsnetworkingv1beta1.IngressLoadBalancerStatusApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressPortStatus"):
		return &applyconfigurationsnetworkingv1beta1.IngressPortStatusApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressRule"):
		return &applyconfigurationsnetworkingv1beta1.IngressRuleApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressRuleValue"):
		return &applyconfigurationsnetworkingv1beta1.IngressRuleValueApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressSpec"):
		return &applyconfigurationsnetworkingv1beta1.IngressSpecApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressStatus"):
		return &applyconfigurationsnetworkingv1beta1.IngressStatusApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IngressTLS"):
		return &applyconfigurationsnetworkingv1beta1.IngressTLSApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IPAddress"):
		return &applyconfigurationsnetworkingv1beta1.IPAddressApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("IPAddressSpec"):
		return &applyconfigurationsnetworkingv1beta1.IPAddressSpecApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("ParentReference"):
		return &applyconfigurationsnetworkingv1beta1.ParentReferenceApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("ServiceCIDR"):
		return &applyconfigurationsnetworkingv1beta1.ServiceCIDRApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("ServiceCIDRSpec"):
		return &applyconfigurationsnetworkingv1beta1.ServiceCIDRSpecApplyConfiguration{}
	case networkingv1beta1.SchemeGroupVersion.WithKind("ServiceCIDRStatus"):
		return &applyconfigurationsnetworkingv1beta1.ServiceCIDRStatusApplyConfiguration{}

		// Group=node.k8s.io, Version=v1
	case nodev1.SchemeGroupVersion.WithKind("Overhead"):
		return &applyconfigurationsnodev1.OverheadApplyConfiguration{}
	case nodev1.SchemeGroupVersion.WithKind("RuntimeClass"):
		return &applyconfigurationsnodev1.RuntimeClassApplyConfiguration{}
	case nodev1.SchemeGroupVersion.WithKind("Scheduling"):
		return &applyconfigurationsnodev1.SchedulingApplyConfiguration{}

		// Group=node.k8s.io, Version=v1alpha1
	case nodev1alpha1.SchemeGroupVersion.WithKind("Overhead"):
		return &applyconfigurationsnodev1alpha1.OverheadApplyConfiguration{}
	case nodev1alpha1.SchemeGroupVersion.WithKind("RuntimeClass"):
		return &applyconfigurationsnodev1alpha1.RuntimeClassApplyConfiguration{}
	case nodev1alpha1.SchemeGroupVersion.WithKind("RuntimeClassSpec"):
		return &applyconfigurationsnodev1alpha1.RuntimeClassSpecApplyConfiguration{}
	case nodev1alpha1.SchemeGroupVersion.WithKind("Scheduling"):
		return &applyconfigurationsnodev1alpha1.SchedulingApplyConfiguration{}

		// Group=node.k8s.io, Version=v1beta1
	case nodev1beta1.SchemeGroupVersion.WithKind("Overhead"):
		return &applyconfigurationsnodev1beta1.OverheadApplyConfiguration{}
	case nodev1beta1.SchemeGroupVersion.WithKind("RuntimeClass"):
		return &applyconfigurationsnodev1beta1.RuntimeClassApplyConfiguration{}
	case nodev1beta1.SchemeGroupVersion.WithKind("Scheduling"):
		return &applyconfigurationsnodev1beta1.SchedulingApplyConfiguration{}

		// Group=policy, Version=v1
	case policyv1.SchemeGroupVersion.WithKind("Eviction"):
		return &applyconfigurationspolicyv1.EvictionApplyConfiguration{}
	case policyv1.SchemeGroupVersion.WithKind("PodDisruptionBudget"):
		return &applyconfigurationspolicyv1.PodDisruptionBudgetApplyConfiguration{}
	case policyv1.SchemeGroupVersion.WithKind("PodDisruptionBudgetSpec"):
		return &applyconfigurationspolicyv1.PodDisruptionBudgetSpecApplyConfiguration{}
	case policyv1.SchemeGroupVersion.WithKind("PodDisruptionBudgetStatus"):
		return &applyconfigurationspolicyv1.PodDisruptionBudgetStatusApplyConfiguration{}

		// Group=policy, Version=v1beta1
	case policyv1beta1.SchemeGroupVersion.WithKind("Eviction"):
		return &applyconfigurationspolicyv1beta1.EvictionApplyConfiguration{}
	case policyv1beta1.SchemeGroupVersion.WithKind("PodDisruptionBudget"):
		return &applyconfigurationspolicyv1beta1.PodDisruptionBudgetApplyConfiguration{}
	case policyv1beta1.SchemeGroupVersion.WithKind("PodDisruptionBudgetSpec"):
		return &applyconfigurationspolicyv1beta1.PodDisruptionBudgetSpecApplyConfiguration{}
	case policyv1beta1.SchemeGroupVersion.WithKind("PodDisruptionBudgetStatus"):
		return &applyconfigurationspolicyv1beta1.PodDisruptionBudgetStatusApplyConfiguration{}

		// Group=rbac.authorization.k8s.io, Version=v1
	case rbacv1.SchemeGroupVersion.WithKind("AggregationRule"):
		return &applyconfigurationsrbacv1.AggregationRuleApplyConfiguration{}
	case rbacv1.SchemeGroupVersion.WithKind("ClusterRole"):
		return &applyconfigurationsrbacv1.ClusterRoleApplyConfiguration{}
	case rbacv1.SchemeGroupVersion.WithKind("ClusterRoleBinding"):
		return &applyconfigurationsrbacv1.ClusterRoleBindingApplyConfiguration{}
	case rbacv1.SchemeGroupVersion.WithKind("PolicyRule"):
		return &applyconfigurationsrbacv1.PolicyRuleApplyConfiguration{}
	case rbacv1.SchemeGroupVersion.WithKind("Role"):
		return &applyconfigurationsrbacv1.RoleApplyConfiguration{}
	case rbacv1.SchemeGroupVersion.WithKind("RoleBinding"):
		return &applyconfigurationsrbacv1.RoleBindingApplyConfiguration{}
	case rbacv1.SchemeGroupVersion.WithKind("RoleRef"):
		return &applyconfigurationsrbacv1.RoleRefApplyConfiguration{}
	case rbacv1.SchemeGroupVersion.WithKind("Subject"):
		return &applyconfigurationsrbacv1.SubjectApplyConfiguration{}

		// Group=rbac.authorization.k8s.io, Version=v1alpha1
	case rbacv1alpha1.SchemeGroupVersion.WithKind("AggregationRule"):
		return &applyconfigurationsrbacv1alpha1.AggregationRuleApplyConfiguration{}
	case rbacv1alpha1.SchemeGroupVersion.WithKind("ClusterRole"):
		return &applyconfigurationsrbacv1alpha1.ClusterRoleApplyConfiguration{}
	case rbacv1alpha1.SchemeGroupVersion.WithKind("ClusterRoleBinding"):
		return &applyconfigurationsrbacv1alpha1.ClusterRoleBindingApplyConfiguration{}
	case rbacv1alpha1.SchemeGroupVersion.WithKind("PolicyRule"):
		return &applyconfigurationsrbacv1alpha1.PolicyRuleApplyConfiguration{}
	case rbacv1alpha1.SchemeGroupVersion.WithKind("Role"):
		return &applyconfigurationsrbacv1alpha1.RoleApplyConfiguration{}
	case rbacv1alpha1.SchemeGroupVersion.WithKind("RoleBinding"):
		return &applyconfigurationsrbacv1alpha1.RoleBindingApplyConfiguration{}
	case rbacv1alpha1.SchemeGroupVersion.WithKind("RoleRef"):
		return &applyconfigurationsrbacv1alpha1.RoleRefApplyConfiguration{}
	case rbacv1alpha1.SchemeGroupVersion.WithKind("Subject"):
		return &applyconfigurationsrbacv1alpha1.SubjectApplyConfiguration{}

		// Group=rbac.authorization.k8s.io, Version=v1beta1
	case rbacv1beta1.SchemeGroupVersion.WithKind("AggregationRule"):
		return &applyconfigurationsrbacv1beta1.AggregationRuleApplyConfiguration{}
	case rbacv1beta1.SchemeGroupVersion.WithKind("ClusterRole"):
		return &applyconfigurationsrbacv1beta1.ClusterRoleApplyConfiguration{}
	case rbacv1beta1.SchemeGroupVersion.WithKind("ClusterRoleBinding"):
		return &applyconfigurationsrbacv1beta1.ClusterRoleBindingApplyConfiguration{}
	case rbacv1beta1.SchemeGroupVersion.WithKind("PolicyRule"):
		return &applyconfigurationsrbacv1beta1.PolicyRuleApplyConfiguration{}
	case rbacv1beta1.SchemeGroupVersion.WithKind("Role"):
		return &applyconfigurationsrbacv1beta1.RoleApplyConfiguration{}
	case rbacv1beta1.SchemeGroupVersion.WithKind("RoleBinding"):
		return &applyconfigurationsrbacv1beta1.RoleBindingApplyConfiguration{}
	case rbacv1beta1.SchemeGroupVersion.WithKind("RoleRef"):
		return &applyconfigurationsrbacv1beta1.RoleRefApplyConfiguration{}
	case rbacv1beta1.SchemeGroupVersion.WithKind("Subject"):
		return &applyconfigurationsrbacv1beta1.SubjectApplyConfiguration{}

		// Group=resource.k8s.io, Version=v1
	case resourcev1.SchemeGroupVersion.WithKind("AllocatedDeviceStatus"):
		return &applyconfigurationsresourcev1.AllocatedDeviceStatusApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("AllocationResult"):
		return &applyconfigurationsresourcev1.AllocationResultApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("CELDeviceSelector"):
		return &applyconfigurationsresourcev1.CELDeviceSelectorApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("Counter"):
		return &applyconfigurationsresourcev1.CounterApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("CounterSet"):
		return &applyconfigurationsresourcev1.CounterSetApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("Device"):
		return &applyconfigurationsresourcev1.DeviceApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceAllocationConfiguration"):
		return &applyconfigurationsresourcev1.DeviceAllocationConfigurationApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceAllocationResult"):
		return &applyconfigurationsresourcev1.DeviceAllocationResultApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceAttribute"):
		return &applyconfigurationsresourcev1.DeviceAttributeApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceCapacity"):
		return &applyconfigurationsresourcev1.DeviceCapacityApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceClaim"):
		return &applyconfigurationsresourcev1.DeviceClaimApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceClaimConfiguration"):
		return &applyconfigurationsresourcev1.DeviceClaimConfigurationApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceClass"):
		return &applyconfigurationsresourcev1.DeviceClassApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceClassConfiguration"):
		return &applyconfigurationsresourcev1.DeviceClassConfigurationApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceClassSpec"):
		return &applyconfigurationsresourcev1.DeviceClassSpecApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceConfiguration"):
		return &applyconfigurationsresourcev1.DeviceConfigurationApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceConstraint"):
		return &applyconfigurationsresourcev1.DeviceConstraintApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceCounterConsumption"):
		return &applyconfigurationsresourcev1.DeviceCounterConsumptionApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceRequest"):
		return &applyconfigurationsresourcev1.DeviceRequestApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceRequestAllocationResult"):
		return &applyconfigurationsresourcev1.DeviceRequestAllocationResultApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceSelector"):
		return &applyconfigurationsresourcev1.DeviceSelectorApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceSubRequest"):
		return &applyconfigurationsresourcev1.DeviceSubRequestApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceTaint"):
		return &applyconfigurationsresourcev1.DeviceTaintApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("DeviceToleration"):
		return &applyconfigurationsresourcev1.DeviceTolerationApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ExactDeviceRequest"):
		return &applyconfigurationsresourcev1.ExactDeviceRequestApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("NetworkDeviceData"):
		return &applyconfigurationsresourcev1.NetworkDeviceDataApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("OpaqueDeviceConfiguration"):
		return &applyconfigurationsresourcev1.OpaqueDeviceConfigurationApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceClaim"):
		return &applyconfigurationsresourcev1.ResourceClaimApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceClaimConsumerReference"):
		return &applyconfigurationsresourcev1.ResourceClaimConsumerReferenceApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceClaimSpec"):
		return &applyconfigurationsresourcev1.ResourceClaimSpecApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceClaimStatus"):
		return &applyconfigurationsresourcev1.ResourceClaimStatusApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceClaimTemplate"):
		return &applyconfigurationsresourcev1.ResourceClaimTemplateApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceClaimTemplateSpec"):
		return &applyconfigurationsresourcev1.ResourceClaimTemplateSpecApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourcePool"):
		return &applyconfigurationsresourcev1.ResourcePoolApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceSlice"):
		return &applyconfigurationsresourcev1.ResourceSliceApplyConfiguration{}
	case resourcev1.SchemeGroupVersion.WithKind("ResourceSliceSpec"):
		return &applyconfigurationsresourcev1.ResourceSliceSpecApplyConfiguration{}

		// Group=resource.k8s.io, Version=v1alpha3
	case v1alpha3.SchemeGroupVersion.WithKind("CELDeviceSelector"):
		return &resourcev1alpha3.CELDeviceSelectorApplyConfiguration{}
	case v1alpha3.SchemeGroupVersion.WithKind("DeviceSelector"):
		return &resourcev1alpha3.DeviceSelectorApplyConfiguration{}
	case v1alpha3.SchemeGroupVersion.WithKind("DeviceTaint"):
		return &resourcev1alpha3.DeviceTaintApplyConfiguration{}
	case v1alpha3.SchemeGroupVersion.WithKind("DeviceTaintRule"):
		return &resourcev1alpha3.DeviceTaintRuleApplyConfiguration{}
	case v1alpha3.SchemeGroupVersion.WithKind("DeviceTaintRuleSpec"):
		return &resourcev1alpha3.DeviceTaintRuleSpecApplyConfiguration{}
	case v1alpha3.SchemeGroupVersion.WithKind("DeviceTaintSelector"):
		return &resourcev1alpha3.DeviceTaintSelectorApplyConfiguration{}

		// Group=resource.k8s.io, Version=v1beta1
	case resourcev1beta1.SchemeGroupVersion.WithKind("AllocatedDeviceStatus"):
		return &applyconfigurationsresourcev1beta1.AllocatedDeviceStatusApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("AllocationResult"):
		return &applyconfigurationsresourcev1beta1.AllocationResultApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("BasicDevice"):
		return &applyconfigurationsresourcev1beta1.BasicDeviceApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("CELDeviceSelector"):
		return &applyconfigurationsresourcev1beta1.CELDeviceSelectorApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("Counter"):
		return &applyconfigurationsresourcev1beta1.CounterApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("CounterSet"):
		return &applyconfigurationsresourcev1beta1.CounterSetApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("Device"):
		return &applyconfigurationsresourcev1beta1.DeviceApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceAllocationConfiguration"):
		return &applyconfigurationsresourcev1beta1.DeviceAllocationConfigurationApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceAllocationResult"):
		return &applyconfigurationsresourcev1beta1.DeviceAllocationResultApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceAttribute"):
		return &applyconfigurationsresourcev1beta1.DeviceAttributeApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceCapacity"):
		return &applyconfigurationsresourcev1beta1.DeviceCapacityApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceClaim"):
		return &applyconfigurationsresourcev1beta1.DeviceClaimApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceClaimConfiguration"):
		return &applyconfigurationsresourcev1beta1.DeviceClaimConfigurationApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceClass"):
		return &applyconfigurationsresourcev1beta1.DeviceClassApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceClassConfiguration"):
		return &applyconfigurationsresourcev1beta1.DeviceClassConfigurationApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceClassSpec"):
		return &applyconfigurationsresourcev1beta1.DeviceClassSpecApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceConfiguration"):
		return &applyconfigurationsresourcev1beta1.DeviceConfigurationApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceConstraint"):
		return &applyconfigurationsresourcev1beta1.DeviceConstraintApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceCounterConsumption"):
		return &applyconfigurationsresourcev1beta1.DeviceCounterConsumptionApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceRequest"):
		return &applyconfigurationsresourcev1beta1.DeviceRequestApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceRequestAllocationResult"):
		return &applyconfigurationsresourcev1beta1.DeviceRequestAllocationResultApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceSelector"):
		return &applyconfigurationsresourcev1beta1.DeviceSelectorApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceSubRequest"):
		return &applyconfigurationsresourcev1beta1.DeviceSubRequestApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceTaint"):
		return &applyconfigurationsresourcev1beta1.DeviceTaintApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("DeviceToleration"):
		return &applyconfigurationsresourcev1beta1.DeviceTolerationApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("NetworkDeviceData"):
		return &applyconfigurationsresourcev1beta1.NetworkDeviceDataApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("OpaqueDeviceConfiguration"):
		return &applyconfigurationsresourcev1beta1.OpaqueDeviceConfigurationApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceClaim"):
		return &applyconfigurationsresourcev1beta1.ResourceClaimApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceClaimConsumerReference"):
		return &applyconfigurationsresourcev1beta1.ResourceClaimConsumerReferenceApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceClaimSpec"):
		return &applyconfigurationsresourcev1beta1.ResourceClaimSpecApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceClaimStatus"):
		return &applyconfigurationsresourcev1beta1.ResourceClaimStatusApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceClaimTemplate"):
		return &applyconfigurationsresourcev1beta1.ResourceClaimTemplateApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceClaimTemplateSpec"):
		return &applyconfigurationsresourcev1beta1.ResourceClaimTemplateSpecApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourcePool"):
		return &applyconfigurationsresourcev1beta1.ResourcePoolApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceSlice"):
		return &applyconfigurationsresourcev1beta1.ResourceSliceApplyConfiguration{}
	case resourcev1beta1.SchemeGroupVersion.WithKind("ResourceSliceSpec"):
		return &applyconfigurationsresourcev1beta1.ResourceSliceSpecApplyConfiguration{}

		// Group=resource.k8s.io, Version=v1beta2
	case resourcev1beta2.SchemeGroupVersion.WithKind("AllocatedDeviceStatus"):
		return &applyconfigurationsresourcev1beta2.AllocatedDeviceStatusApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("AllocationResult"):
		return &applyconfigurationsresourcev1beta2.AllocationResultApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("CELDeviceSelector"):
		return &applyconfigurationsresourcev1beta2.CELDeviceSelectorApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("Counter"):
		return &applyconfigurationsresourcev1beta2.CounterApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("CounterSet"):
		return &applyconfigurationsresourcev1beta2.CounterSetApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("Device"):
		return &applyconfigurationsresourcev1beta2.DeviceApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceAllocationConfiguration"):
		return &applyconfigurationsresourcev1beta2.DeviceAllocationConfigurationApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceAllocationResult"):
		return &applyconfigurationsresourcev1beta2.DeviceAllocationResultApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceAttribute"):
		return &applyconfigurationsresourcev1beta2.DeviceAttributeApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceCapacity"):
		return &applyconfigurationsresourcev1beta2.DeviceCapacityApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceClaim"):
		return &applyconfigurationsresourcev1beta2.DeviceClaimApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceClaimConfiguration"):
		return &applyconfigurationsresourcev1beta2.DeviceClaimConfigurationApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceClass"):
		return &applyconfigurationsresourcev1beta2.DeviceClassApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceClassConfiguration"):
		return &applyconfigurationsresourcev1beta2.DeviceClassConfigurationApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceClassSpec"):
		return &applyconfigurationsresourcev1beta2.DeviceClassSpecApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceConfiguration"):
		return &applyconfigurationsresourcev1beta2.DeviceConfigurationApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceConstraint"):
		return &applyconfigurationsresourcev1beta2.DeviceConstraintApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceCounterConsumption"):
		return &applyconfigurationsresourcev1beta2.DeviceCounterConsumptionApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceRequest"):
		return &applyconfigurationsresourcev1beta2.DeviceRequestApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceRequestAllocationResult"):
		return &applyconfigurationsresourcev1beta2.DeviceRequestAllocationResultApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceSelector"):
		return &applyconfigurationsresourcev1beta2.DeviceSelectorApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceSubRequest"):
		return &applyconfigurationsresourcev1beta2.DeviceSubRequestApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceTaint"):
		return &applyconfigurationsresourcev1beta2.DeviceTaintApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("DeviceToleration"):
		return &applyconfigurationsresourcev1beta2.DeviceTolerationApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ExactDeviceRequest"):
		return &applyconfigurationsresourcev1beta2.ExactDeviceRequestApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("NetworkDeviceData"):
		return &applyconfigurationsresourcev1beta2.NetworkDeviceDataApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("OpaqueDeviceConfiguration"):
		return &applyconfigurationsresourcev1beta2.OpaqueDeviceConfigurationApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceClaim"):
		return &applyconfigurationsresourcev1beta2.ResourceClaimApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceClaimConsumerReference"):
		return &applyconfigurationsresourcev1beta2.ResourceClaimConsumerReferenceApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceClaimSpec"):
		return &applyconfigurationsresourcev1beta2.ResourceClaimSpecApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceClaimStatus"):
		return &applyconfigurationsresourcev1beta2.ResourceClaimStatusApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceClaimTemplate"):
		return &applyconfigurationsresourcev1beta2.ResourceClaimTemplateApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceClaimTemplateSpec"):
		return &applyconfigurationsresourcev1beta2.ResourceClaimTemplateSpecApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourcePool"):
		return &applyconfigurationsresourcev1beta2.ResourcePoolApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceSlice"):
		return &applyconfigurationsresourcev1beta2.ResourceSliceApplyConfiguration{}
	case resourcev1beta2.SchemeGroupVersion.WithKind("ResourceSliceSpec"):
		return &applyconfigurationsresourcev1beta2.ResourceSliceSpecApplyConfiguration{}

		// Group=scheduling.k8s.io, Version=v1
	case schedulingv1.SchemeGroupVersion.WithKind("PriorityClass"):
		return &applyconfigurationsschedulingv1.PriorityClassApplyConfiguration{}

		// Group=scheduling.k8s.io, Version=v1alpha1
	case schedulingv1alpha1.SchemeGroupVersion.WithKind("PriorityClass"):
		return &applyconfigurationsschedulingv1alpha1.PriorityClassApplyConfiguration{}

		// Group=scheduling.k8s.io, Version=v1beta1
	case schedulingv1beta1.SchemeGroupVersion.WithKind("PriorityClass"):
		return &applyconfigurationsschedulingv1beta1.PriorityClassApplyConfiguration{}

		// Group=storage.k8s.io, Version=v1
	case storagev1.SchemeGroupVersion.WithKind("CSIDriver"):
		return &applyconfigurationsstoragev1.CSIDriverApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("CSIDriverSpec"):
		return &applyconfigurationsstoragev1.CSIDriverSpecApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("CSINode"):
		return &applyconfigurationsstoragev1.CSINodeApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("CSINodeDriver"):
		return &applyconfigurationsstoragev1.CSINodeDriverApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("CSINodeSpec"):
		return &applyconfigurationsstoragev1.CSINodeSpecApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("CSIStorageCapacity"):
		return &applyconfigurationsstoragev1.CSIStorageCapacityApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("StorageClass"):
		return &applyconfigurationsstoragev1.StorageClassApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("TokenRequest"):
		return &applyconfigurationsstoragev1.TokenRequestApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("VolumeAttachment"):
		return &applyconfigurationsstoragev1.VolumeAttachmentApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("VolumeAttachmentSource"):
		return &applyconfigurationsstoragev1.VolumeAttachmentSourceApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("VolumeAttachmentSpec"):
		return &applyconfigurationsstoragev1.VolumeAttachmentSpecApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("VolumeAttachmentStatus"):
		return &applyconfigurationsstoragev1.VolumeAttachmentStatusApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("VolumeError"):
		return &applyconfigurationsstoragev1.VolumeErrorApplyConfiguration{}
	case storagev1.SchemeGroupVersion.WithKind("VolumeNodeResources"):
		return &applyconfigurationsstoragev1.VolumeNodeResourcesApplyConfiguration{}

		// Group=storage.k8s.io, Version=v1alpha1
	case storagev1alpha1.SchemeGroupVersion.WithKind("CSIStorageCapacity"):
		return &applyconfigurationsstoragev1alpha1.CSIStorageCapacityApplyConfiguration{}
	case storagev1alpha1.SchemeGroupVersion.WithKind("VolumeAttachment"):
		return &applyconfigurationsstoragev1alpha1.VolumeAttachmentApplyConfiguration{}
	case storagev1alpha1.SchemeGroupVersion.WithKind("VolumeAttachmentSource"):
		return &applyconfigurationsstoragev1alpha1.VolumeAttachmentSourceApplyConfiguration{}
	case storagev1alpha1.SchemeGroupVersion.WithKind("VolumeAttachmentSpec"):
		return &applyconfigurationsstoragev1alpha1.VolumeAttachmentSpecApplyConfiguration{}
	case storagev1alpha1.SchemeGroupVersion.WithKind("VolumeAttachmentStatus"):
		return &applyconfigurationsstoragev1alpha1.VolumeAttachmentStatusApplyConfiguration{}
	case storagev1alpha1.SchemeGroupVersion.WithKind("VolumeAttributesClass"):
		return &applyconfigurationsstoragev1alpha1.VolumeAttributesClassApplyConfiguration{}
	case storagev1alpha1.SchemeGroupVersion.WithKind("VolumeError"):
		return &applyconfigurationsstoragev1alpha1.VolumeErrorApplyConfiguration{}

		// Group=storage.k8s.io, Version=v1beta1
	case storagev1beta1.SchemeGroupVersion.WithKind("CSIDriver"):
		return &applyconfigurationsstoragev1beta1.CSIDriverApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("CSIDriverSpec"):
		return &applyconfigurationsstoragev1beta1.CSIDriverSpecApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("CSINode"):
		return &applyconfigurationsstoragev1beta1.CSINodeApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("CSINodeDriver"):
		return &applyconfigurationsstoragev1beta1.CSINodeDriverApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("CSINodeSpec"):
		return &applyconfigurationsstoragev1beta1.CSINodeSpecApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("CSIStorageCapacity"):
		return &applyconfigurationsstoragev1beta1.CSIStorageCapacityApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("StorageClass"):
		return &applyconfigurationsstoragev1beta1.StorageClassApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("TokenRequest"):
		return &applyconfigurationsstoragev1beta1.TokenRequestApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("VolumeAttachment"):
		return &applyconfigurationsstoragev1beta1.VolumeAttachmentApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("VolumeAttachmentSource"):
		return &applyconfigurationsstoragev1beta1.VolumeAttachmentSourceApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("VolumeAttachmentSpec"):
		return &applyconfigurationsstoragev1beta1.VolumeAttachmentSpecApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("VolumeAttachmentStatus"):
		return &applyconfigurationsstoragev1beta1.VolumeAttachmentStatusApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("VolumeAttributesClass"):
		return &applyconfigurationsstoragev1beta1.VolumeAttributesClassApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("VolumeError"):
		return &applyconfigurationsstoragev1beta1.VolumeErrorApplyConfiguration{}
	case storagev1beta1.SchemeGroupVersion.WithKind("VolumeNodeResources"):
		return &applyconfigurationsstoragev1beta1.VolumeNodeResourcesApplyConfiguration{}

		// Group=storagemigration.k8s.io, Version=v1alpha1
	case storagemigrationv1alpha1.SchemeGroupVersion.WithKind("GroupVersionResource"):
		return &applyconfigurationsstoragemigrationv1alpha1.GroupVersionResourceApplyConfiguration{}
	case storagemigrationv1alpha1.SchemeGroupVersion.WithKind("MigrationCondition"):
		return &applyconfigurationsstoragemigrationv1alpha1.MigrationConditionApplyConfiguration{}
	case storagemigrationv1alpha1.SchemeGroupVersion.WithKind("StorageVersionMigration"):
		return &applyconfigurationsstoragemigrationv1alpha1.StorageVersionMigrationApplyConfiguration{}
	case storagemigrationv1alpha1.SchemeGroupVersion.WithKind("StorageVersionMigrationSpec"):
		return &applyconfigurationsstoragemigrationv1alpha1.StorageVersionMigrationSpecApplyConfiguration{}
	case storagemigrationv1alpha1.SchemeGroupVersion.WithKind("StorageVersionMigrationStatus"):
		return &applyconfigurationsstoragemigrationv1alpha1.StorageVersionMigrationStatusApplyConfiguration{}

	}
	return nil
}

func NewTypeConverter(scheme *runtime.Scheme) managedfields.TypeConverter {
	return managedfields.NewSchemeTypeConverter(scheme, internal.Parser())
}
