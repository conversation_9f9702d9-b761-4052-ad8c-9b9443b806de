// This is a generated file. Do not edit directly.

module k8s.io/apimachinery

go 1.24.0

godebug default=go1.24

require (
	github.com/armon/go-socks5 v0.0.0-20160902184237-e75332964ef5
	github.com/davecgh/go-spew v1.1.1
	github.com/fxamacker/cbor/v2 v2.9.0
	github.com/gogo/protobuf v1.3.2
	github.com/google/gnostic-models v0.7.0
	github.com/google/go-cmp v0.7.0
	github.com/google/uuid v1.6.0
	github.com/moby/spdystream v0.5.0
	github.com/mxk/go-flowrate v0.0.0-20140419014527-cca7078d478f
	github.com/pmezard/go-difflib v1.0.0
	github.com/spf13/pflag v1.0.6
	github.com/stretchr/testify v1.10.0
	golang.org/x/net v0.38.0
	golang.org/x/time v0.9.0
	gopkg.in/evanphx/json-patch.v4 v4.12.0
	gopkg.in/inf.v0 v0.9.1
	k8s.io/klog/v2 v2.130.1
	k8s.io/kube-openapi v0.0.0-20250710124328-f3f2b991d03b
	k8s.io/utils v0.0.0-20250604170112-4c0f3b243397
	sigs.k8s.io/json v0.0.0-20241014173422-cfa47c3a1cc8
	sigs.k8s.io/randfill v1.0.0
	sigs.k8s.io/structured-merge-diff/v6 v6.3.0
	sigs.k8s.io/yaml v1.5.0
)

require (
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-openapi/jsonpointer v0.21.0 // indirect
	github.com/go-openapi/jsonreference v0.20.2 // indirect
	github.com/go-openapi/swag v0.23.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.3-0.20250322232337-35a7c28c31ee // indirect
	github.com/onsi/ginkgo/v2 v2.21.0 // indirect
	github.com/onsi/gomega v1.35.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/rogpeppe/go-internal v1.13.1 // indirect
	github.com/x448/float16 v0.8.4 // indirect
	go.yaml.in/yaml/v2 v2.4.2 // indirect
	go.yaml.in/yaml/v3 v3.0.4 // indirect
	golang.org/x/text v0.23.0 // indirect
	google.golang.org/protobuf v1.36.5 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
