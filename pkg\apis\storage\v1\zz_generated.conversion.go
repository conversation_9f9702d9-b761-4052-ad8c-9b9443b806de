//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by conversion-gen. DO NOT EDIT.

package v1

import (
	unsafe "unsafe"

	corev1 "k8s.io/api/core/v1"
	storagev1 "k8s.io/api/storage/v1"
	resource "k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
	core "k8s.io/kubernetes/pkg/apis/core"
	apiscorev1 "k8s.io/kubernetes/pkg/apis/core/v1"
	storage "k8s.io/kubernetes/pkg/apis/storage"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*storagev1.CSIDriver)(nil), (*storage.CSIDriver)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIDriver_To_storage_CSIDriver(a.(*storagev1.CSIDriver), b.(*storage.CSIDriver), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSIDriver)(nil), (*storagev1.CSIDriver)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSIDriver_To_v1_CSIDriver(a.(*storage.CSIDriver), b.(*storagev1.CSIDriver), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSIDriverList)(nil), (*storage.CSIDriverList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIDriverList_To_storage_CSIDriverList(a.(*storagev1.CSIDriverList), b.(*storage.CSIDriverList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSIDriverList)(nil), (*storagev1.CSIDriverList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSIDriverList_To_v1_CSIDriverList(a.(*storage.CSIDriverList), b.(*storagev1.CSIDriverList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSIDriverSpec)(nil), (*storage.CSIDriverSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIDriverSpec_To_storage_CSIDriverSpec(a.(*storagev1.CSIDriverSpec), b.(*storage.CSIDriverSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSIDriverSpec)(nil), (*storagev1.CSIDriverSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSIDriverSpec_To_v1_CSIDriverSpec(a.(*storage.CSIDriverSpec), b.(*storagev1.CSIDriverSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSINode)(nil), (*storage.CSINode)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSINode_To_storage_CSINode(a.(*storagev1.CSINode), b.(*storage.CSINode), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSINode)(nil), (*storagev1.CSINode)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSINode_To_v1_CSINode(a.(*storage.CSINode), b.(*storagev1.CSINode), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSINodeDriver)(nil), (*storage.CSINodeDriver)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSINodeDriver_To_storage_CSINodeDriver(a.(*storagev1.CSINodeDriver), b.(*storage.CSINodeDriver), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSINodeDriver)(nil), (*storagev1.CSINodeDriver)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSINodeDriver_To_v1_CSINodeDriver(a.(*storage.CSINodeDriver), b.(*storagev1.CSINodeDriver), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSINodeList)(nil), (*storage.CSINodeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSINodeList_To_storage_CSINodeList(a.(*storagev1.CSINodeList), b.(*storage.CSINodeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSINodeList)(nil), (*storagev1.CSINodeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSINodeList_To_v1_CSINodeList(a.(*storage.CSINodeList), b.(*storagev1.CSINodeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSINodeSpec)(nil), (*storage.CSINodeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSINodeSpec_To_storage_CSINodeSpec(a.(*storagev1.CSINodeSpec), b.(*storage.CSINodeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSINodeSpec)(nil), (*storagev1.CSINodeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSINodeSpec_To_v1_CSINodeSpec(a.(*storage.CSINodeSpec), b.(*storagev1.CSINodeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSIStorageCapacity)(nil), (*storage.CSIStorageCapacity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIStorageCapacity_To_storage_CSIStorageCapacity(a.(*storagev1.CSIStorageCapacity), b.(*storage.CSIStorageCapacity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSIStorageCapacity)(nil), (*storagev1.CSIStorageCapacity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSIStorageCapacity_To_v1_CSIStorageCapacity(a.(*storage.CSIStorageCapacity), b.(*storagev1.CSIStorageCapacity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.CSIStorageCapacityList)(nil), (*storage.CSIStorageCapacityList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIStorageCapacityList_To_storage_CSIStorageCapacityList(a.(*storagev1.CSIStorageCapacityList), b.(*storage.CSIStorageCapacityList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.CSIStorageCapacityList)(nil), (*storagev1.CSIStorageCapacityList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_CSIStorageCapacityList_To_v1_CSIStorageCapacityList(a.(*storage.CSIStorageCapacityList), b.(*storagev1.CSIStorageCapacityList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.StorageClass)(nil), (*storage.StorageClass)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageClass_To_storage_StorageClass(a.(*storagev1.StorageClass), b.(*storage.StorageClass), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.StorageClass)(nil), (*storagev1.StorageClass)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_StorageClass_To_v1_StorageClass(a.(*storage.StorageClass), b.(*storagev1.StorageClass), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.StorageClassList)(nil), (*storage.StorageClassList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageClassList_To_storage_StorageClassList(a.(*storagev1.StorageClassList), b.(*storage.StorageClassList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.StorageClassList)(nil), (*storagev1.StorageClassList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_StorageClassList_To_v1_StorageClassList(a.(*storage.StorageClassList), b.(*storagev1.StorageClassList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.TokenRequest)(nil), (*storage.TokenRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TokenRequest_To_storage_TokenRequest(a.(*storagev1.TokenRequest), b.(*storage.TokenRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.TokenRequest)(nil), (*storagev1.TokenRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_TokenRequest_To_v1_TokenRequest(a.(*storage.TokenRequest), b.(*storagev1.TokenRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.VolumeAttachment)(nil), (*storage.VolumeAttachment)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeAttachment_To_storage_VolumeAttachment(a.(*storagev1.VolumeAttachment), b.(*storage.VolumeAttachment), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.VolumeAttachment)(nil), (*storagev1.VolumeAttachment)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_VolumeAttachment_To_v1_VolumeAttachment(a.(*storage.VolumeAttachment), b.(*storagev1.VolumeAttachment), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.VolumeAttachmentList)(nil), (*storage.VolumeAttachmentList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeAttachmentList_To_storage_VolumeAttachmentList(a.(*storagev1.VolumeAttachmentList), b.(*storage.VolumeAttachmentList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.VolumeAttachmentList)(nil), (*storagev1.VolumeAttachmentList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_VolumeAttachmentList_To_v1_VolumeAttachmentList(a.(*storage.VolumeAttachmentList), b.(*storagev1.VolumeAttachmentList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.VolumeAttachmentSource)(nil), (*storage.VolumeAttachmentSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeAttachmentSource_To_storage_VolumeAttachmentSource(a.(*storagev1.VolumeAttachmentSource), b.(*storage.VolumeAttachmentSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.VolumeAttachmentSource)(nil), (*storagev1.VolumeAttachmentSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_VolumeAttachmentSource_To_v1_VolumeAttachmentSource(a.(*storage.VolumeAttachmentSource), b.(*storagev1.VolumeAttachmentSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.VolumeAttachmentSpec)(nil), (*storage.VolumeAttachmentSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeAttachmentSpec_To_storage_VolumeAttachmentSpec(a.(*storagev1.VolumeAttachmentSpec), b.(*storage.VolumeAttachmentSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.VolumeAttachmentSpec)(nil), (*storagev1.VolumeAttachmentSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_VolumeAttachmentSpec_To_v1_VolumeAttachmentSpec(a.(*storage.VolumeAttachmentSpec), b.(*storagev1.VolumeAttachmentSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.VolumeAttachmentStatus)(nil), (*storage.VolumeAttachmentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeAttachmentStatus_To_storage_VolumeAttachmentStatus(a.(*storagev1.VolumeAttachmentStatus), b.(*storage.VolumeAttachmentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.VolumeAttachmentStatus)(nil), (*storagev1.VolumeAttachmentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_VolumeAttachmentStatus_To_v1_VolumeAttachmentStatus(a.(*storage.VolumeAttachmentStatus), b.(*storagev1.VolumeAttachmentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.VolumeError)(nil), (*storage.VolumeError)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeError_To_storage_VolumeError(a.(*storagev1.VolumeError), b.(*storage.VolumeError), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.VolumeError)(nil), (*storagev1.VolumeError)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_VolumeError_To_v1_VolumeError(a.(*storage.VolumeError), b.(*storagev1.VolumeError), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storagev1.VolumeNodeResources)(nil), (*storage.VolumeNodeResources)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeNodeResources_To_storage_VolumeNodeResources(a.(*storagev1.VolumeNodeResources), b.(*storage.VolumeNodeResources), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*storage.VolumeNodeResources)(nil), (*storagev1.VolumeNodeResources)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_storage_VolumeNodeResources_To_v1_VolumeNodeResources(a.(*storage.VolumeNodeResources), b.(*storagev1.VolumeNodeResources), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1_CSIDriver_To_storage_CSIDriver(in *storagev1.CSIDriver, out *storage.CSIDriver, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_CSIDriverSpec_To_storage_CSIDriverSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_CSIDriver_To_storage_CSIDriver is an autogenerated conversion function.
func Convert_v1_CSIDriver_To_storage_CSIDriver(in *storagev1.CSIDriver, out *storage.CSIDriver, s conversion.Scope) error {
	return autoConvert_v1_CSIDriver_To_storage_CSIDriver(in, out, s)
}

func autoConvert_storage_CSIDriver_To_v1_CSIDriver(in *storage.CSIDriver, out *storagev1.CSIDriver, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_storage_CSIDriverSpec_To_v1_CSIDriverSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_storage_CSIDriver_To_v1_CSIDriver is an autogenerated conversion function.
func Convert_storage_CSIDriver_To_v1_CSIDriver(in *storage.CSIDriver, out *storagev1.CSIDriver, s conversion.Scope) error {
	return autoConvert_storage_CSIDriver_To_v1_CSIDriver(in, out, s)
}

func autoConvert_v1_CSIDriverList_To_storage_CSIDriverList(in *storagev1.CSIDriverList, out *storage.CSIDriverList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]storage.CSIDriver, len(*in))
		for i := range *in {
			if err := Convert_v1_CSIDriver_To_storage_CSIDriver(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_CSIDriverList_To_storage_CSIDriverList is an autogenerated conversion function.
func Convert_v1_CSIDriverList_To_storage_CSIDriverList(in *storagev1.CSIDriverList, out *storage.CSIDriverList, s conversion.Scope) error {
	return autoConvert_v1_CSIDriverList_To_storage_CSIDriverList(in, out, s)
}

func autoConvert_storage_CSIDriverList_To_v1_CSIDriverList(in *storage.CSIDriverList, out *storagev1.CSIDriverList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]storagev1.CSIDriver, len(*in))
		for i := range *in {
			if err := Convert_storage_CSIDriver_To_v1_CSIDriver(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_storage_CSIDriverList_To_v1_CSIDriverList is an autogenerated conversion function.
func Convert_storage_CSIDriverList_To_v1_CSIDriverList(in *storage.CSIDriverList, out *storagev1.CSIDriverList, s conversion.Scope) error {
	return autoConvert_storage_CSIDriverList_To_v1_CSIDriverList(in, out, s)
}

func autoConvert_v1_CSIDriverSpec_To_storage_CSIDriverSpec(in *storagev1.CSIDriverSpec, out *storage.CSIDriverSpec, s conversion.Scope) error {
	out.AttachRequired = (*bool)(unsafe.Pointer(in.AttachRequired))
	out.PodInfoOnMount = (*bool)(unsafe.Pointer(in.PodInfoOnMount))
	out.VolumeLifecycleModes = *(*[]storage.VolumeLifecycleMode)(unsafe.Pointer(&in.VolumeLifecycleModes))
	out.StorageCapacity = (*bool)(unsafe.Pointer(in.StorageCapacity))
	out.FSGroupPolicy = (*storage.FSGroupPolicy)(unsafe.Pointer(in.FSGroupPolicy))
	out.TokenRequests = *(*[]storage.TokenRequest)(unsafe.Pointer(&in.TokenRequests))
	out.RequiresRepublish = (*bool)(unsafe.Pointer(in.RequiresRepublish))
	out.SELinuxMount = (*bool)(unsafe.Pointer(in.SELinuxMount))
	out.NodeAllocatableUpdatePeriodSeconds = (*int64)(unsafe.Pointer(in.NodeAllocatableUpdatePeriodSeconds))
	return nil
}

// Convert_v1_CSIDriverSpec_To_storage_CSIDriverSpec is an autogenerated conversion function.
func Convert_v1_CSIDriverSpec_To_storage_CSIDriverSpec(in *storagev1.CSIDriverSpec, out *storage.CSIDriverSpec, s conversion.Scope) error {
	return autoConvert_v1_CSIDriverSpec_To_storage_CSIDriverSpec(in, out, s)
}

func autoConvert_storage_CSIDriverSpec_To_v1_CSIDriverSpec(in *storage.CSIDriverSpec, out *storagev1.CSIDriverSpec, s conversion.Scope) error {
	out.AttachRequired = (*bool)(unsafe.Pointer(in.AttachRequired))
	out.FSGroupPolicy = (*storagev1.FSGroupPolicy)(unsafe.Pointer(in.FSGroupPolicy))
	out.PodInfoOnMount = (*bool)(unsafe.Pointer(in.PodInfoOnMount))
	out.VolumeLifecycleModes = *(*[]storagev1.VolumeLifecycleMode)(unsafe.Pointer(&in.VolumeLifecycleModes))
	out.StorageCapacity = (*bool)(unsafe.Pointer(in.StorageCapacity))
	out.TokenRequests = *(*[]storagev1.TokenRequest)(unsafe.Pointer(&in.TokenRequests))
	out.RequiresRepublish = (*bool)(unsafe.Pointer(in.RequiresRepublish))
	out.SELinuxMount = (*bool)(unsafe.Pointer(in.SELinuxMount))
	out.NodeAllocatableUpdatePeriodSeconds = (*int64)(unsafe.Pointer(in.NodeAllocatableUpdatePeriodSeconds))
	return nil
}

// Convert_storage_CSIDriverSpec_To_v1_CSIDriverSpec is an autogenerated conversion function.
func Convert_storage_CSIDriverSpec_To_v1_CSIDriverSpec(in *storage.CSIDriverSpec, out *storagev1.CSIDriverSpec, s conversion.Scope) error {
	return autoConvert_storage_CSIDriverSpec_To_v1_CSIDriverSpec(in, out, s)
}

func autoConvert_v1_CSINode_To_storage_CSINode(in *storagev1.CSINode, out *storage.CSINode, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_CSINodeSpec_To_storage_CSINodeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_CSINode_To_storage_CSINode is an autogenerated conversion function.
func Convert_v1_CSINode_To_storage_CSINode(in *storagev1.CSINode, out *storage.CSINode, s conversion.Scope) error {
	return autoConvert_v1_CSINode_To_storage_CSINode(in, out, s)
}

func autoConvert_storage_CSINode_To_v1_CSINode(in *storage.CSINode, out *storagev1.CSINode, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_storage_CSINodeSpec_To_v1_CSINodeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_storage_CSINode_To_v1_CSINode is an autogenerated conversion function.
func Convert_storage_CSINode_To_v1_CSINode(in *storage.CSINode, out *storagev1.CSINode, s conversion.Scope) error {
	return autoConvert_storage_CSINode_To_v1_CSINode(in, out, s)
}

func autoConvert_v1_CSINodeDriver_To_storage_CSINodeDriver(in *storagev1.CSINodeDriver, out *storage.CSINodeDriver, s conversion.Scope) error {
	out.Name = in.Name
	out.NodeID = in.NodeID
	out.TopologyKeys = *(*[]string)(unsafe.Pointer(&in.TopologyKeys))
	out.Allocatable = (*storage.VolumeNodeResources)(unsafe.Pointer(in.Allocatable))
	return nil
}

// Convert_v1_CSINodeDriver_To_storage_CSINodeDriver is an autogenerated conversion function.
func Convert_v1_CSINodeDriver_To_storage_CSINodeDriver(in *storagev1.CSINodeDriver, out *storage.CSINodeDriver, s conversion.Scope) error {
	return autoConvert_v1_CSINodeDriver_To_storage_CSINodeDriver(in, out, s)
}

func autoConvert_storage_CSINodeDriver_To_v1_CSINodeDriver(in *storage.CSINodeDriver, out *storagev1.CSINodeDriver, s conversion.Scope) error {
	out.Name = in.Name
	out.NodeID = in.NodeID
	out.TopologyKeys = *(*[]string)(unsafe.Pointer(&in.TopologyKeys))
	out.Allocatable = (*storagev1.VolumeNodeResources)(unsafe.Pointer(in.Allocatable))
	return nil
}

// Convert_storage_CSINodeDriver_To_v1_CSINodeDriver is an autogenerated conversion function.
func Convert_storage_CSINodeDriver_To_v1_CSINodeDriver(in *storage.CSINodeDriver, out *storagev1.CSINodeDriver, s conversion.Scope) error {
	return autoConvert_storage_CSINodeDriver_To_v1_CSINodeDriver(in, out, s)
}

func autoConvert_v1_CSINodeList_To_storage_CSINodeList(in *storagev1.CSINodeList, out *storage.CSINodeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]storage.CSINode)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_CSINodeList_To_storage_CSINodeList is an autogenerated conversion function.
func Convert_v1_CSINodeList_To_storage_CSINodeList(in *storagev1.CSINodeList, out *storage.CSINodeList, s conversion.Scope) error {
	return autoConvert_v1_CSINodeList_To_storage_CSINodeList(in, out, s)
}

func autoConvert_storage_CSINodeList_To_v1_CSINodeList(in *storage.CSINodeList, out *storagev1.CSINodeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]storagev1.CSINode)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_storage_CSINodeList_To_v1_CSINodeList is an autogenerated conversion function.
func Convert_storage_CSINodeList_To_v1_CSINodeList(in *storage.CSINodeList, out *storagev1.CSINodeList, s conversion.Scope) error {
	return autoConvert_storage_CSINodeList_To_v1_CSINodeList(in, out, s)
}

func autoConvert_v1_CSINodeSpec_To_storage_CSINodeSpec(in *storagev1.CSINodeSpec, out *storage.CSINodeSpec, s conversion.Scope) error {
	out.Drivers = *(*[]storage.CSINodeDriver)(unsafe.Pointer(&in.Drivers))
	return nil
}

// Convert_v1_CSINodeSpec_To_storage_CSINodeSpec is an autogenerated conversion function.
func Convert_v1_CSINodeSpec_To_storage_CSINodeSpec(in *storagev1.CSINodeSpec, out *storage.CSINodeSpec, s conversion.Scope) error {
	return autoConvert_v1_CSINodeSpec_To_storage_CSINodeSpec(in, out, s)
}

func autoConvert_storage_CSINodeSpec_To_v1_CSINodeSpec(in *storage.CSINodeSpec, out *storagev1.CSINodeSpec, s conversion.Scope) error {
	out.Drivers = *(*[]storagev1.CSINodeDriver)(unsafe.Pointer(&in.Drivers))
	return nil
}

// Convert_storage_CSINodeSpec_To_v1_CSINodeSpec is an autogenerated conversion function.
func Convert_storage_CSINodeSpec_To_v1_CSINodeSpec(in *storage.CSINodeSpec, out *storagev1.CSINodeSpec, s conversion.Scope) error {
	return autoConvert_storage_CSINodeSpec_To_v1_CSINodeSpec(in, out, s)
}

func autoConvert_v1_CSIStorageCapacity_To_storage_CSIStorageCapacity(in *storagev1.CSIStorageCapacity, out *storage.CSIStorageCapacity, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.NodeTopology = (*metav1.LabelSelector)(unsafe.Pointer(in.NodeTopology))
	out.StorageClassName = in.StorageClassName
	out.Capacity = (*resource.Quantity)(unsafe.Pointer(in.Capacity))
	out.MaximumVolumeSize = (*resource.Quantity)(unsafe.Pointer(in.MaximumVolumeSize))
	return nil
}

// Convert_v1_CSIStorageCapacity_To_storage_CSIStorageCapacity is an autogenerated conversion function.
func Convert_v1_CSIStorageCapacity_To_storage_CSIStorageCapacity(in *storagev1.CSIStorageCapacity, out *storage.CSIStorageCapacity, s conversion.Scope) error {
	return autoConvert_v1_CSIStorageCapacity_To_storage_CSIStorageCapacity(in, out, s)
}

func autoConvert_storage_CSIStorageCapacity_To_v1_CSIStorageCapacity(in *storage.CSIStorageCapacity, out *storagev1.CSIStorageCapacity, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.NodeTopology = (*metav1.LabelSelector)(unsafe.Pointer(in.NodeTopology))
	out.StorageClassName = in.StorageClassName
	out.Capacity = (*resource.Quantity)(unsafe.Pointer(in.Capacity))
	out.MaximumVolumeSize = (*resource.Quantity)(unsafe.Pointer(in.MaximumVolumeSize))
	return nil
}

// Convert_storage_CSIStorageCapacity_To_v1_CSIStorageCapacity is an autogenerated conversion function.
func Convert_storage_CSIStorageCapacity_To_v1_CSIStorageCapacity(in *storage.CSIStorageCapacity, out *storagev1.CSIStorageCapacity, s conversion.Scope) error {
	return autoConvert_storage_CSIStorageCapacity_To_v1_CSIStorageCapacity(in, out, s)
}

func autoConvert_v1_CSIStorageCapacityList_To_storage_CSIStorageCapacityList(in *storagev1.CSIStorageCapacityList, out *storage.CSIStorageCapacityList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]storage.CSIStorageCapacity)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_CSIStorageCapacityList_To_storage_CSIStorageCapacityList is an autogenerated conversion function.
func Convert_v1_CSIStorageCapacityList_To_storage_CSIStorageCapacityList(in *storagev1.CSIStorageCapacityList, out *storage.CSIStorageCapacityList, s conversion.Scope) error {
	return autoConvert_v1_CSIStorageCapacityList_To_storage_CSIStorageCapacityList(in, out, s)
}

func autoConvert_storage_CSIStorageCapacityList_To_v1_CSIStorageCapacityList(in *storage.CSIStorageCapacityList, out *storagev1.CSIStorageCapacityList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]storagev1.CSIStorageCapacity)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_storage_CSIStorageCapacityList_To_v1_CSIStorageCapacityList is an autogenerated conversion function.
func Convert_storage_CSIStorageCapacityList_To_v1_CSIStorageCapacityList(in *storage.CSIStorageCapacityList, out *storagev1.CSIStorageCapacityList, s conversion.Scope) error {
	return autoConvert_storage_CSIStorageCapacityList_To_v1_CSIStorageCapacityList(in, out, s)
}

func autoConvert_v1_StorageClass_To_storage_StorageClass(in *storagev1.StorageClass, out *storage.StorageClass, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Provisioner = in.Provisioner
	out.Parameters = *(*map[string]string)(unsafe.Pointer(&in.Parameters))
	out.ReclaimPolicy = (*core.PersistentVolumeReclaimPolicy)(unsafe.Pointer(in.ReclaimPolicy))
	out.MountOptions = *(*[]string)(unsafe.Pointer(&in.MountOptions))
	out.AllowVolumeExpansion = (*bool)(unsafe.Pointer(in.AllowVolumeExpansion))
	out.VolumeBindingMode = (*storage.VolumeBindingMode)(unsafe.Pointer(in.VolumeBindingMode))
	out.AllowedTopologies = *(*[]core.TopologySelectorTerm)(unsafe.Pointer(&in.AllowedTopologies))
	return nil
}

// Convert_v1_StorageClass_To_storage_StorageClass is an autogenerated conversion function.
func Convert_v1_StorageClass_To_storage_StorageClass(in *storagev1.StorageClass, out *storage.StorageClass, s conversion.Scope) error {
	return autoConvert_v1_StorageClass_To_storage_StorageClass(in, out, s)
}

func autoConvert_storage_StorageClass_To_v1_StorageClass(in *storage.StorageClass, out *storagev1.StorageClass, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Provisioner = in.Provisioner
	out.Parameters = *(*map[string]string)(unsafe.Pointer(&in.Parameters))
	out.ReclaimPolicy = (*corev1.PersistentVolumeReclaimPolicy)(unsafe.Pointer(in.ReclaimPolicy))
	out.MountOptions = *(*[]string)(unsafe.Pointer(&in.MountOptions))
	out.AllowVolumeExpansion = (*bool)(unsafe.Pointer(in.AllowVolumeExpansion))
	out.VolumeBindingMode = (*storagev1.VolumeBindingMode)(unsafe.Pointer(in.VolumeBindingMode))
	out.AllowedTopologies = *(*[]corev1.TopologySelectorTerm)(unsafe.Pointer(&in.AllowedTopologies))
	return nil
}

// Convert_storage_StorageClass_To_v1_StorageClass is an autogenerated conversion function.
func Convert_storage_StorageClass_To_v1_StorageClass(in *storage.StorageClass, out *storagev1.StorageClass, s conversion.Scope) error {
	return autoConvert_storage_StorageClass_To_v1_StorageClass(in, out, s)
}

func autoConvert_v1_StorageClassList_To_storage_StorageClassList(in *storagev1.StorageClassList, out *storage.StorageClassList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]storage.StorageClass)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_StorageClassList_To_storage_StorageClassList is an autogenerated conversion function.
func Convert_v1_StorageClassList_To_storage_StorageClassList(in *storagev1.StorageClassList, out *storage.StorageClassList, s conversion.Scope) error {
	return autoConvert_v1_StorageClassList_To_storage_StorageClassList(in, out, s)
}

func autoConvert_storage_StorageClassList_To_v1_StorageClassList(in *storage.StorageClassList, out *storagev1.StorageClassList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]storagev1.StorageClass)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_storage_StorageClassList_To_v1_StorageClassList is an autogenerated conversion function.
func Convert_storage_StorageClassList_To_v1_StorageClassList(in *storage.StorageClassList, out *storagev1.StorageClassList, s conversion.Scope) error {
	return autoConvert_storage_StorageClassList_To_v1_StorageClassList(in, out, s)
}

func autoConvert_v1_TokenRequest_To_storage_TokenRequest(in *storagev1.TokenRequest, out *storage.TokenRequest, s conversion.Scope) error {
	out.Audience = in.Audience
	out.ExpirationSeconds = (*int64)(unsafe.Pointer(in.ExpirationSeconds))
	return nil
}

// Convert_v1_TokenRequest_To_storage_TokenRequest is an autogenerated conversion function.
func Convert_v1_TokenRequest_To_storage_TokenRequest(in *storagev1.TokenRequest, out *storage.TokenRequest, s conversion.Scope) error {
	return autoConvert_v1_TokenRequest_To_storage_TokenRequest(in, out, s)
}

func autoConvert_storage_TokenRequest_To_v1_TokenRequest(in *storage.TokenRequest, out *storagev1.TokenRequest, s conversion.Scope) error {
	out.Audience = in.Audience
	out.ExpirationSeconds = (*int64)(unsafe.Pointer(in.ExpirationSeconds))
	return nil
}

// Convert_storage_TokenRequest_To_v1_TokenRequest is an autogenerated conversion function.
func Convert_storage_TokenRequest_To_v1_TokenRequest(in *storage.TokenRequest, out *storagev1.TokenRequest, s conversion.Scope) error {
	return autoConvert_storage_TokenRequest_To_v1_TokenRequest(in, out, s)
}

func autoConvert_v1_VolumeAttachment_To_storage_VolumeAttachment(in *storagev1.VolumeAttachment, out *storage.VolumeAttachment, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_VolumeAttachmentSpec_To_storage_VolumeAttachmentSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_VolumeAttachmentStatus_To_storage_VolumeAttachmentStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_VolumeAttachment_To_storage_VolumeAttachment is an autogenerated conversion function.
func Convert_v1_VolumeAttachment_To_storage_VolumeAttachment(in *storagev1.VolumeAttachment, out *storage.VolumeAttachment, s conversion.Scope) error {
	return autoConvert_v1_VolumeAttachment_To_storage_VolumeAttachment(in, out, s)
}

func autoConvert_storage_VolumeAttachment_To_v1_VolumeAttachment(in *storage.VolumeAttachment, out *storagev1.VolumeAttachment, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_storage_VolumeAttachmentSpec_To_v1_VolumeAttachmentSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_storage_VolumeAttachmentStatus_To_v1_VolumeAttachmentStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_storage_VolumeAttachment_To_v1_VolumeAttachment is an autogenerated conversion function.
func Convert_storage_VolumeAttachment_To_v1_VolumeAttachment(in *storage.VolumeAttachment, out *storagev1.VolumeAttachment, s conversion.Scope) error {
	return autoConvert_storage_VolumeAttachment_To_v1_VolumeAttachment(in, out, s)
}

func autoConvert_v1_VolumeAttachmentList_To_storage_VolumeAttachmentList(in *storagev1.VolumeAttachmentList, out *storage.VolumeAttachmentList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]storage.VolumeAttachment, len(*in))
		for i := range *in {
			if err := Convert_v1_VolumeAttachment_To_storage_VolumeAttachment(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_VolumeAttachmentList_To_storage_VolumeAttachmentList is an autogenerated conversion function.
func Convert_v1_VolumeAttachmentList_To_storage_VolumeAttachmentList(in *storagev1.VolumeAttachmentList, out *storage.VolumeAttachmentList, s conversion.Scope) error {
	return autoConvert_v1_VolumeAttachmentList_To_storage_VolumeAttachmentList(in, out, s)
}

func autoConvert_storage_VolumeAttachmentList_To_v1_VolumeAttachmentList(in *storage.VolumeAttachmentList, out *storagev1.VolumeAttachmentList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]storagev1.VolumeAttachment, len(*in))
		for i := range *in {
			if err := Convert_storage_VolumeAttachment_To_v1_VolumeAttachment(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_storage_VolumeAttachmentList_To_v1_VolumeAttachmentList is an autogenerated conversion function.
func Convert_storage_VolumeAttachmentList_To_v1_VolumeAttachmentList(in *storage.VolumeAttachmentList, out *storagev1.VolumeAttachmentList, s conversion.Scope) error {
	return autoConvert_storage_VolumeAttachmentList_To_v1_VolumeAttachmentList(in, out, s)
}

func autoConvert_v1_VolumeAttachmentSource_To_storage_VolumeAttachmentSource(in *storagev1.VolumeAttachmentSource, out *storage.VolumeAttachmentSource, s conversion.Scope) error {
	out.PersistentVolumeName = (*string)(unsafe.Pointer(in.PersistentVolumeName))
	if in.InlineVolumeSpec != nil {
		in, out := &in.InlineVolumeSpec, &out.InlineVolumeSpec
		*out = new(core.PersistentVolumeSpec)
		if err := apiscorev1.Convert_v1_PersistentVolumeSpec_To_core_PersistentVolumeSpec(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.InlineVolumeSpec = nil
	}
	return nil
}

// Convert_v1_VolumeAttachmentSource_To_storage_VolumeAttachmentSource is an autogenerated conversion function.
func Convert_v1_VolumeAttachmentSource_To_storage_VolumeAttachmentSource(in *storagev1.VolumeAttachmentSource, out *storage.VolumeAttachmentSource, s conversion.Scope) error {
	return autoConvert_v1_VolumeAttachmentSource_To_storage_VolumeAttachmentSource(in, out, s)
}

func autoConvert_storage_VolumeAttachmentSource_To_v1_VolumeAttachmentSource(in *storage.VolumeAttachmentSource, out *storagev1.VolumeAttachmentSource, s conversion.Scope) error {
	out.PersistentVolumeName = (*string)(unsafe.Pointer(in.PersistentVolumeName))
	if in.InlineVolumeSpec != nil {
		in, out := &in.InlineVolumeSpec, &out.InlineVolumeSpec
		*out = new(corev1.PersistentVolumeSpec)
		if err := apiscorev1.Convert_core_PersistentVolumeSpec_To_v1_PersistentVolumeSpec(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.InlineVolumeSpec = nil
	}
	return nil
}

// Convert_storage_VolumeAttachmentSource_To_v1_VolumeAttachmentSource is an autogenerated conversion function.
func Convert_storage_VolumeAttachmentSource_To_v1_VolumeAttachmentSource(in *storage.VolumeAttachmentSource, out *storagev1.VolumeAttachmentSource, s conversion.Scope) error {
	return autoConvert_storage_VolumeAttachmentSource_To_v1_VolumeAttachmentSource(in, out, s)
}

func autoConvert_v1_VolumeAttachmentSpec_To_storage_VolumeAttachmentSpec(in *storagev1.VolumeAttachmentSpec, out *storage.VolumeAttachmentSpec, s conversion.Scope) error {
	out.Attacher = in.Attacher
	if err := Convert_v1_VolumeAttachmentSource_To_storage_VolumeAttachmentSource(&in.Source, &out.Source, s); err != nil {
		return err
	}
	out.NodeName = in.NodeName
	return nil
}

// Convert_v1_VolumeAttachmentSpec_To_storage_VolumeAttachmentSpec is an autogenerated conversion function.
func Convert_v1_VolumeAttachmentSpec_To_storage_VolumeAttachmentSpec(in *storagev1.VolumeAttachmentSpec, out *storage.VolumeAttachmentSpec, s conversion.Scope) error {
	return autoConvert_v1_VolumeAttachmentSpec_To_storage_VolumeAttachmentSpec(in, out, s)
}

func autoConvert_storage_VolumeAttachmentSpec_To_v1_VolumeAttachmentSpec(in *storage.VolumeAttachmentSpec, out *storagev1.VolumeAttachmentSpec, s conversion.Scope) error {
	out.Attacher = in.Attacher
	if err := Convert_storage_VolumeAttachmentSource_To_v1_VolumeAttachmentSource(&in.Source, &out.Source, s); err != nil {
		return err
	}
	out.NodeName = in.NodeName
	return nil
}

// Convert_storage_VolumeAttachmentSpec_To_v1_VolumeAttachmentSpec is an autogenerated conversion function.
func Convert_storage_VolumeAttachmentSpec_To_v1_VolumeAttachmentSpec(in *storage.VolumeAttachmentSpec, out *storagev1.VolumeAttachmentSpec, s conversion.Scope) error {
	return autoConvert_storage_VolumeAttachmentSpec_To_v1_VolumeAttachmentSpec(in, out, s)
}

func autoConvert_v1_VolumeAttachmentStatus_To_storage_VolumeAttachmentStatus(in *storagev1.VolumeAttachmentStatus, out *storage.VolumeAttachmentStatus, s conversion.Scope) error {
	out.Attached = in.Attached
	out.AttachmentMetadata = *(*map[string]string)(unsafe.Pointer(&in.AttachmentMetadata))
	out.AttachError = (*storage.VolumeError)(unsafe.Pointer(in.AttachError))
	out.DetachError = (*storage.VolumeError)(unsafe.Pointer(in.DetachError))
	return nil
}

// Convert_v1_VolumeAttachmentStatus_To_storage_VolumeAttachmentStatus is an autogenerated conversion function.
func Convert_v1_VolumeAttachmentStatus_To_storage_VolumeAttachmentStatus(in *storagev1.VolumeAttachmentStatus, out *storage.VolumeAttachmentStatus, s conversion.Scope) error {
	return autoConvert_v1_VolumeAttachmentStatus_To_storage_VolumeAttachmentStatus(in, out, s)
}

func autoConvert_storage_VolumeAttachmentStatus_To_v1_VolumeAttachmentStatus(in *storage.VolumeAttachmentStatus, out *storagev1.VolumeAttachmentStatus, s conversion.Scope) error {
	out.Attached = in.Attached
	out.AttachmentMetadata = *(*map[string]string)(unsafe.Pointer(&in.AttachmentMetadata))
	out.AttachError = (*storagev1.VolumeError)(unsafe.Pointer(in.AttachError))
	out.DetachError = (*storagev1.VolumeError)(unsafe.Pointer(in.DetachError))
	return nil
}

// Convert_storage_VolumeAttachmentStatus_To_v1_VolumeAttachmentStatus is an autogenerated conversion function.
func Convert_storage_VolumeAttachmentStatus_To_v1_VolumeAttachmentStatus(in *storage.VolumeAttachmentStatus, out *storagev1.VolumeAttachmentStatus, s conversion.Scope) error {
	return autoConvert_storage_VolumeAttachmentStatus_To_v1_VolumeAttachmentStatus(in, out, s)
}

func autoConvert_v1_VolumeError_To_storage_VolumeError(in *storagev1.VolumeError, out *storage.VolumeError, s conversion.Scope) error {
	out.Time = in.Time
	out.Message = in.Message
	out.ErrorCode = (*int32)(unsafe.Pointer(in.ErrorCode))
	return nil
}

// Convert_v1_VolumeError_To_storage_VolumeError is an autogenerated conversion function.
func Convert_v1_VolumeError_To_storage_VolumeError(in *storagev1.VolumeError, out *storage.VolumeError, s conversion.Scope) error {
	return autoConvert_v1_VolumeError_To_storage_VolumeError(in, out, s)
}

func autoConvert_storage_VolumeError_To_v1_VolumeError(in *storage.VolumeError, out *storagev1.VolumeError, s conversion.Scope) error {
	out.Time = in.Time
	out.Message = in.Message
	out.ErrorCode = (*int32)(unsafe.Pointer(in.ErrorCode))
	return nil
}

// Convert_storage_VolumeError_To_v1_VolumeError is an autogenerated conversion function.
func Convert_storage_VolumeError_To_v1_VolumeError(in *storage.VolumeError, out *storagev1.VolumeError, s conversion.Scope) error {
	return autoConvert_storage_VolumeError_To_v1_VolumeError(in, out, s)
}

func autoConvert_v1_VolumeNodeResources_To_storage_VolumeNodeResources(in *storagev1.VolumeNodeResources, out *storage.VolumeNodeResources, s conversion.Scope) error {
	out.Count = (*int32)(unsafe.Pointer(in.Count))
	return nil
}

// Convert_v1_VolumeNodeResources_To_storage_VolumeNodeResources is an autogenerated conversion function.
func Convert_v1_VolumeNodeResources_To_storage_VolumeNodeResources(in *storagev1.VolumeNodeResources, out *storage.VolumeNodeResources, s conversion.Scope) error {
	return autoConvert_v1_VolumeNodeResources_To_storage_VolumeNodeResources(in, out, s)
}

func autoConvert_storage_VolumeNodeResources_To_v1_VolumeNodeResources(in *storage.VolumeNodeResources, out *storagev1.VolumeNodeResources, s conversion.Scope) error {
	out.Count = (*int32)(unsafe.Pointer(in.Count))
	return nil
}

// Convert_storage_VolumeNodeResources_To_v1_VolumeNodeResources is an autogenerated conversion function.
func Convert_storage_VolumeNodeResources_To_v1_VolumeNodeResources(in *storage.VolumeNodeResources, out *storagev1.VolumeNodeResources, s conversion.Scope) error {
	return autoConvert_storage_VolumeNodeResources_To_v1_VolumeNodeResources(in, out, s)
}
