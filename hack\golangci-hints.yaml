# golangci-lint is used in Kubernetes with different configurations that
# enable an increasing amount of checks:
# - golangci.yaml is the most permissive configuration. All existing code
#   passed.
# - golangci-hints.yaml adds checks for code patterns where developer
#   and reviewer may decide whether findings should get addressed before
#   merging. Beware that the golangci-lint output includes also the
#   issues that must be fixed and doesn't indicate how severe each issue
#   is (https://gophers.slack.com/archives/CS0TBRKPC/p1685721815275349).
#
# All three flavors are generated from golangci.yaml.in with
# hack/update-golangci-lint-config.sh.

run:
  timeout: 30m

  # The default is relative to the configuration, which is confusing because
  # then all paths start with ../ to move out of the "hack" directory.
  # `gomod` mirrors the current behavior of `golangci-lint.sh` changing into
  # the root of the repository. Because we are operating in a workspace,
  # the module picked by `gomod` is the main one
  relative-path-mode: gomod

version: "2"

formatters:
  exclusions:
    paths:
      - third_party

output:
  formats:
    text:
      path: stderr

issues:
  max-issues-per-linter: 0
  max-same-issues: 0

linters:
  exclusions:
    paths:
      - third_party

    # Log a warning if an exclusion rule is unused.
    #
    # Uncomment when investigating whether the configuration can be simplified,
    # but beware that golangci-lint then needs to be invoked for the entire
    # repository. Invoking it for individual packages may trigger these warning
    # when the rules are only needed elsewhere.
    #
    # warn-unused: true

    # Excluding configuration per-path, per-linter, per-text and per-source.
    rules:

      # https://github.com/kubernetes/kubernetes/issues/117288#issuecomment-1507012435
      - linters:
          - gocritic
        text: "ifElseChain: rewrite if-else to switch statement"

      # Only packages listed here opt into the strict "exported symbols must be documented".
      #
      # Exclude texts from https://github.com/golangci/golangci-lint/blob/ab3c3cd69e602ff53bb4c3e2c188f0caeb80305d/pkg/config/issues.go#L11-L103
      - linters:
          - revive
          - staticcheck
        text: comment on exported (method|function|type|const)|should have( a package)? comment|comment should be of the form|comment on exported (method|function|type|const)|should have( a package)? comment|comment should be of the form|exported (.+) should have comment( \(or a comment on this block\))? or be unexported|package comment should be of the form "(.+)...|comment on exported (.+) should be of the form "(.+)...|should have a package comment
        path-except: cmd/kubeadm

      # The unused linter that comes from staticcheck currently does not handle types which implement
      # a generic interface. The linter incorrectly reports the implementations of unexported
      # interface methods as unused. See https://github.com/dominikh/go-tools/issues/1294.
      # Rather than exporting the interface methods, which makes the error go away but changes the
      # semantics of the code, we ignore this error for affected files.
      # This can be removed when the staticcheck implementation of this rule is fixed, which may
      # depend on https://github.com/golang/go/issues/63982.
      - linters:
          - unused
        path: staging/src/k8s.io/client-go/util/workqueue/metrics.go

      # SSA Extract calls are allowed in tests.
      - linters:
          - forbidigo
        text: should not be used because managedFields was removed
        path: _test.go$

      # Adding unversioned feature gates is allowed in tests
      - linters:
          - forbidigo
        text: should not use Add, use AddVersioned instead
        path: _test.go$

      # The Kubernetes naming convention for conversion functions uses underscores
      # and intentionally deviates from normal Go conventions to make those function
      # names more readable. Same for SetDefaults_*.
      #
      # https://github.com/kubernetes/kubernetes/issues/117288#issuecomment-1507028627
      # https://github.com/kubernetes/kubernetes/issues/117288#issuecomment-1514201592
      - linters:
          - staticcheck
          - revive
        text: "(ST1003: should not use underscores in Go names; func ([cC]onvert_.*_To_.*|[sS]etDefaults_)|exported: exported function (Convert|SetDefaults)_.* should be of the form)"

      # The generated swagger docs also don't follow the naming convention.
      - linters:
          - staticcheck
        text: "ST1003: should not use underscores in Go names"
        path: types_swagger_doc_generated.go$

      - path: (.+)\.go$
        # staticcheck: Developers tend to write in C-style with an explicit 'break' in a 'switch', so it's ok to ignore
        text: ineffective break statement. Did you mean to break out of the outer loop

      # exclude ineffassign linter for generated files for conversion
      - path: conversion\.go
        linters:
          - ineffassign

      # Kube-API-Linter should only be run on the API definitions
      - linters:
        - kubeapilinter
        path-except: staging/src/k8s.io/api/.*
        
      # Exceptions for kube-api-linter.
      # Exceptions are used for kube-api-linter to ignore existing issues that cannot be fixed without breaking changes.

  default: standard
  enable: # please keep this alphabetized
    - depguard
    - forbidigo
    - ginkgolinter
    - gocritic
    - govet
    - errorlint
    - ineffassign
    - kubeapilinter
    - logcheck
    - revive
    - sorted
    - staticcheck
    - testifylint
    - unused
    - usestdlibvars

  settings: # please keep this alphabetized
    custom:
      logcheck:
        # Installed there by hack/verify-golangci-lint.sh.
        path: _output/local/bin/logcheck.so
        description: structured logging checker
        original-url: k8s.io/logtools/logcheck
        settings:
          config: |
            # hack/logcheck.conf contains regular expressions that are matched against <pkg>/<file>,
            # for example k8s.io/cmd/kube-scheduler/app/config/config.go.
            #
            # By default, structured logging call parameters are checked, but usage of
            # those calls is not required. That is changed on a per-file basis.
            #
            # Remember to clean the golangci-lint cache when changing the configuration and
            # running the verify-golangci-lint.sh script multiple times, otherwise
            # golangci-lint will report stale results:
            #    _output/local/bin/golangci-lint cache clean
            
            # At this point we don't enforce the usage structured logging calls except in
            # those packages that were migrated. This disables the check for other files.
            -structured .*
            
            # Now enable it again for migrated packages.
            structured k8s.io/kubernetes/pkg/kubelet/.*
            structured k8s.io/kubernetes/pkg/proxy/.*
            structured k8s.io/kms/.*
            structured k8s.io/apiserver/pkg/storage/value/.*
            structured k8s.io/apiserver/pkg/server/options/encryptionconfig/.*
            
            # The following packages have been migrated to contextual logging.
            # Packages matched here do not have to be listed above because
            # "contextual" implies "structured".
            contextual k8s.io/api/.*
            contextual k8s.io/apimachinery/pkg/util/runtime/.*
            contextual k8s.io/client-go/metadata/.*
            contextual k8s.io/client-go/rest/.*
            contextual k8s.io/client-go/tools/cache/.*
            contextual k8s.io/client-go/tools/events/.*
            contextual k8s.io/client-go/tools/record/.*
            contextual k8s.io/component-helpers/.*
            contextual k8s.io/cri-api/.*
            contextual k8s.io/cri-client/.*
            contextual k8s.io/csi-translation-lib/.*
            contextual k8s.io/dynamic-resource-allocation/.*
            contextual k8s.io/endpointslice/.*
            contextual k8s.io/kms/.*
            contextual k8s.io/kube-controller-manager/.*
            contextual k8s.io/kube-proxy/.*
            contextual k8s.io/kube-scheduler/.*
            contextual k8s.io/sample-apiserver/.*
            contextual k8s.io/sample-cli-plugin/.*
            contextual k8s.io/sample-controller/.*
            contextual k8s.io/kubernetes/cmd/kube-proxy/.*
            contextual k8s.io/kubernetes/cmd/kube-scheduler/.*
            contextual k8s.io/kubernetes/cmd/kubelet/.*
            contextual k8s.io/kubernetes/pkg/controller/.*
            contextual k8s.io/kubernetes/pkg/scheduler/.*
            contextual k8s.io/kubernetes/test/e2e/dra/.*
            contextual k8s.io/kubernetes/pkg/kubelet/cm/dra/.*
            contextual k8s.io/kubernetes/pkg/kubelet/cm/memorymanager/.*
            contextual k8s.io/kubernetes/pkg/kubelet/lifecycle/.*
            contextual k8s.io/kubernetes/pkg/kubelet/pleg/.*
            contextual k8s.io/kubernetes/pkg/kubelet/clustertrustbundle/.*
            contextual k8s.io/kubernetes/pkg/kubelet/token/.*
            contextual k8s.io/kubernetes/pkg/kubelet/cadvisor/.*
            contextual k8s.io/kubernetes/pkg/kubelet/oom/.*
            contextual k8s.io/kubernetes/pkg/kubelet/status/.*
            contextual k8s.io/kubernetes/pkg/kubelet/sysctl/.*
            contextual k8s.io/kubernetes/pkg/kubelet/apis/.*
            contextual k8s.io/kubernetes/pkg/kubelet/kubeletconfig/.*
            contextual k8s.io/kubernetes/pkg/kubelet/kuberuntime/.*
            contextual k8s.io/kubernetes/pkg/kubelet/nodeshutdown/.*
            contextual k8s.io/kubernetes/pkg/kubelet/pod/.*
            contextual k8s.io/kubernetes/pkg/kubelet/preemption/.*
            contextual k8s.io/kubernetes/pkg/kubelet/volumemanager/.*
            
            # As long as contextual logging is alpha or beta, all WithName, WithValues,
            # NewContext calls have to go through klog. Once it is GA, we can lift
            # this restriction. Whether we then do a global search/replace remains
            # to be decided.
            with-helpers .*
      sorted:
        # Installed there by hack/verify-golangci-lint.sh.
        path: _output/local/bin/sorted.so
        description: check if feature gates are sorted
        original-url: k8s.io/kubernetes/hack/tools/golangci-lint/sorted
        settings:
          files:
            - cmd/kubeadm/app/features/features.go
            - pkg/features/kube_features.go
            - staging/src/k8s.io/apiserver/pkg/features/kube_features.go
            - staging/src/k8s.io/client-go/features/known_features.go
            - staging/src/k8s.io/controller-manager/pkg/features/kube_features.go
            - staging/src/k8s.io/apiextensions-apiserver/pkg/features/kube_features.go
            - test/e2e/feature/feature.go
            - test/e2e/environment/environment.go
      kubeapilinter:
        path: _output/local/bin/kube-api-linter.so
        description: kube-api-linter and lints Kube like APIs based on API conventions and best practices.
        original-url: sigs.k8s.io/kube-api-linter
        settings:
          linters:
            disable:
              - '*'
            enable:
              # - "commentstart" # Ensure comments start with the serialized version of the field name.
              # - "conditions" # Ensure conditions have the correct json tags and markers.
              # - "integers" # Ensure only int32 and int64 are used for integers.
              # - "jsontags" # Ensure every field has a json tag.
              # - "maxlength" # Ensure all strings and arrays have maximum lengths/maximum items. ONLY for CRDs until declarative markers exist in core types.
              # - "nobools" # Bools do not evolve over time, should use enums instead.
              # - "nofloats" # Ensure floats are not used.
              # - "nomaps" # Ensure maps are not used, unless they are `map[string]string` (for labels/annotations/etc).
              # - "nophase" # Ensure field names do not have the word "phase" in them.
              # - "optionalorrequired" # Every field should be marked as `+optional` xor `+required`.
              # - "requiredfields" # Required fields should not be pointers, and should not have `omitempty`.
          lintersConfig:
            # conditions:
            #   isFirstField: Warn # Require conditions to be the first field in the status struct.
            #   usePatchStrategy: SuggestFix # Conditions should not use the patch strategy on CRDs.
            #   useProtobuf: SuggestFix # We don't use protobuf, so protobuf tags are not required.
            # jsonTags:
            #   jsonTagRegex: "^[a-z][a-z0-9]*(?:[A-Z][a-z0-9]*)*$" # The default regex is appropriate for our use case.
            # nomaps:
            #   policy: AllowStringToStringMaps # Determines how the linter should handle maps of basic types. Maps of objects are always disallowed.
            # optionalOrRequired:
            #   preferredOptionalMarker: optional # The preferred optional marker to use, fixes will suggest to use this marker. Defaults to `optional`.
            #   preferredRequiredMarker: required # The preferred required marker to use, fixes will suggest to use this marker. Defaults to `required`.
            # requiredFields:
            #   pointerPolicy: SuggestFix # Defaults to `SuggestFix`. We want our required fields to not be pointers.
    depguard:
      rules:
        go-cmp:
          files:
            - $all
            - "!$test"
            - "!**/test/**"
            - "!**/testing/**"
            - "!**/apitesting/**"
          deny:
            - pkg: "github.com/google/go-cmp/cmp"
              desc: "cmp is allowed only in test files"
            - pkg: "html/template"
              desc: "template is allowed only in test files as it disables dead code elimination"
    forbidigo:
      analyze-types: true
      forbid:
        - pattern: ^managedfields\.ExtractInto$
          pkg: ^k8s\.io/apimachinery/pkg/util/managedfields$
          msg: should not be used because managedFields was removed
        - pattern: \.Extract
          pkg: ^k8s\.io/client-go/applyconfigurations/
          msg: should not be used because managedFields was removed
        - pattern: \.Add$
          pkg: ^k8s\.io/component-base/featuregate$         
          msg: should not use Add, use AddVersioned instead
        - pattern: ^gomega\.BeTrue$
          pkg: ^github.com/onsi/gomega$
          msg: "it does not produce a good failure message - use BeTrueBecause with an explicit printf-style failure message instead, or plain Go: if ... { ginkgo.Fail(...) }"
        - pattern: ^gomega\.BeFalse$
          pkg: ^github.com/onsi/gomega$
          msg: "it does not produce a good failure message - use BeFalseBecause with an explicit printf-style failure message instead, or plain Go: if ... { ginkgo.Fail(...) }"
    revive:
      # Only these rules are enabled.
      rules:
        - name: exported
          arguments:
            - disableStutteringCheck
    staticcheck:
      checks:
        - "all"
    testifylint:
      enable-all: true
