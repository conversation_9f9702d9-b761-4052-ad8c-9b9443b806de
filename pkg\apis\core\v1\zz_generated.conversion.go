//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by conversion-gen. DO NOT EDIT.

package v1

import (
	url "net/url"
	unsafe "unsafe"

	corev1 "k8s.io/api/core/v1"
	resource "k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
	types "k8s.io/apimachinery/pkg/types"
	apps "k8s.io/kubernetes/pkg/apis/apps"
	core "k8s.io/kubernetes/pkg/apis/core"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*corev1.AWSElasticBlockStoreVolumeSource)(nil), (*core.AWSElasticBlockStoreVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AWSElasticBlockStoreVolumeSource_To_core_AWSElasticBlockStoreVolumeSource(a.(*corev1.AWSElasticBlockStoreVolumeSource), b.(*core.AWSElasticBlockStoreVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.AWSElasticBlockStoreVolumeSource)(nil), (*corev1.AWSElasticBlockStoreVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_AWSElasticBlockStoreVolumeSource_To_v1_AWSElasticBlockStoreVolumeSource(a.(*core.AWSElasticBlockStoreVolumeSource), b.(*corev1.AWSElasticBlockStoreVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Affinity)(nil), (*core.Affinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Affinity_To_core_Affinity(a.(*corev1.Affinity), b.(*core.Affinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Affinity)(nil), (*corev1.Affinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Affinity_To_v1_Affinity(a.(*core.Affinity), b.(*corev1.Affinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.AppArmorProfile)(nil), (*core.AppArmorProfile)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AppArmorProfile_To_core_AppArmorProfile(a.(*corev1.AppArmorProfile), b.(*core.AppArmorProfile), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.AppArmorProfile)(nil), (*corev1.AppArmorProfile)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_AppArmorProfile_To_v1_AppArmorProfile(a.(*core.AppArmorProfile), b.(*corev1.AppArmorProfile), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.AttachedVolume)(nil), (*core.AttachedVolume)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AttachedVolume_To_core_AttachedVolume(a.(*corev1.AttachedVolume), b.(*core.AttachedVolume), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.AttachedVolume)(nil), (*corev1.AttachedVolume)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_AttachedVolume_To_v1_AttachedVolume(a.(*core.AttachedVolume), b.(*corev1.AttachedVolume), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.AvoidPods)(nil), (*core.AvoidPods)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AvoidPods_To_core_AvoidPods(a.(*corev1.AvoidPods), b.(*core.AvoidPods), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.AvoidPods)(nil), (*corev1.AvoidPods)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_AvoidPods_To_v1_AvoidPods(a.(*core.AvoidPods), b.(*corev1.AvoidPods), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.AzureDiskVolumeSource)(nil), (*core.AzureDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AzureDiskVolumeSource_To_core_AzureDiskVolumeSource(a.(*corev1.AzureDiskVolumeSource), b.(*core.AzureDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.AzureDiskVolumeSource)(nil), (*corev1.AzureDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_AzureDiskVolumeSource_To_v1_AzureDiskVolumeSource(a.(*core.AzureDiskVolumeSource), b.(*corev1.AzureDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.AzureFilePersistentVolumeSource)(nil), (*core.AzureFilePersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AzureFilePersistentVolumeSource_To_core_AzureFilePersistentVolumeSource(a.(*corev1.AzureFilePersistentVolumeSource), b.(*core.AzureFilePersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.AzureFilePersistentVolumeSource)(nil), (*corev1.AzureFilePersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_AzureFilePersistentVolumeSource_To_v1_AzureFilePersistentVolumeSource(a.(*core.AzureFilePersistentVolumeSource), b.(*corev1.AzureFilePersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.AzureFileVolumeSource)(nil), (*core.AzureFileVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AzureFileVolumeSource_To_core_AzureFileVolumeSource(a.(*corev1.AzureFileVolumeSource), b.(*core.AzureFileVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.AzureFileVolumeSource)(nil), (*corev1.AzureFileVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_AzureFileVolumeSource_To_v1_AzureFileVolumeSource(a.(*core.AzureFileVolumeSource), b.(*corev1.AzureFileVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Binding)(nil), (*core.Binding)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Binding_To_core_Binding(a.(*corev1.Binding), b.(*core.Binding), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Binding)(nil), (*corev1.Binding)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Binding_To_v1_Binding(a.(*core.Binding), b.(*corev1.Binding), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.CSIPersistentVolumeSource)(nil), (*core.CSIPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIPersistentVolumeSource_To_core_CSIPersistentVolumeSource(a.(*corev1.CSIPersistentVolumeSource), b.(*core.CSIPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.CSIPersistentVolumeSource)(nil), (*corev1.CSIPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_CSIPersistentVolumeSource_To_v1_CSIPersistentVolumeSource(a.(*core.CSIPersistentVolumeSource), b.(*corev1.CSIPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.CSIVolumeSource)(nil), (*core.CSIVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIVolumeSource_To_core_CSIVolumeSource(a.(*corev1.CSIVolumeSource), b.(*core.CSIVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.CSIVolumeSource)(nil), (*corev1.CSIVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_CSIVolumeSource_To_v1_CSIVolumeSource(a.(*core.CSIVolumeSource), b.(*corev1.CSIVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Capabilities)(nil), (*core.Capabilities)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Capabilities_To_core_Capabilities(a.(*corev1.Capabilities), b.(*core.Capabilities), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Capabilities)(nil), (*corev1.Capabilities)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Capabilities_To_v1_Capabilities(a.(*core.Capabilities), b.(*corev1.Capabilities), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.CephFSPersistentVolumeSource)(nil), (*core.CephFSPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CephFSPersistentVolumeSource_To_core_CephFSPersistentVolumeSource(a.(*corev1.CephFSPersistentVolumeSource), b.(*core.CephFSPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.CephFSPersistentVolumeSource)(nil), (*corev1.CephFSPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_CephFSPersistentVolumeSource_To_v1_CephFSPersistentVolumeSource(a.(*core.CephFSPersistentVolumeSource), b.(*corev1.CephFSPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.CephFSVolumeSource)(nil), (*core.CephFSVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CephFSVolumeSource_To_core_CephFSVolumeSource(a.(*corev1.CephFSVolumeSource), b.(*core.CephFSVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.CephFSVolumeSource)(nil), (*corev1.CephFSVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_CephFSVolumeSource_To_v1_CephFSVolumeSource(a.(*core.CephFSVolumeSource), b.(*corev1.CephFSVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.CinderPersistentVolumeSource)(nil), (*core.CinderPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CinderPersistentVolumeSource_To_core_CinderPersistentVolumeSource(a.(*corev1.CinderPersistentVolumeSource), b.(*core.CinderPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.CinderPersistentVolumeSource)(nil), (*corev1.CinderPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_CinderPersistentVolumeSource_To_v1_CinderPersistentVolumeSource(a.(*core.CinderPersistentVolumeSource), b.(*corev1.CinderPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.CinderVolumeSource)(nil), (*core.CinderVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CinderVolumeSource_To_core_CinderVolumeSource(a.(*corev1.CinderVolumeSource), b.(*core.CinderVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.CinderVolumeSource)(nil), (*corev1.CinderVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_CinderVolumeSource_To_v1_CinderVolumeSource(a.(*core.CinderVolumeSource), b.(*corev1.CinderVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ClientIPConfig)(nil), (*core.ClientIPConfig)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClientIPConfig_To_core_ClientIPConfig(a.(*corev1.ClientIPConfig), b.(*core.ClientIPConfig), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ClientIPConfig)(nil), (*corev1.ClientIPConfig)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ClientIPConfig_To_v1_ClientIPConfig(a.(*core.ClientIPConfig), b.(*corev1.ClientIPConfig), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ClusterTrustBundleProjection)(nil), (*core.ClusterTrustBundleProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterTrustBundleProjection_To_core_ClusterTrustBundleProjection(a.(*corev1.ClusterTrustBundleProjection), b.(*core.ClusterTrustBundleProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ClusterTrustBundleProjection)(nil), (*corev1.ClusterTrustBundleProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ClusterTrustBundleProjection_To_v1_ClusterTrustBundleProjection(a.(*core.ClusterTrustBundleProjection), b.(*corev1.ClusterTrustBundleProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ComponentCondition)(nil), (*core.ComponentCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ComponentCondition_To_core_ComponentCondition(a.(*corev1.ComponentCondition), b.(*core.ComponentCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ComponentCondition)(nil), (*corev1.ComponentCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ComponentCondition_To_v1_ComponentCondition(a.(*core.ComponentCondition), b.(*corev1.ComponentCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ComponentStatus)(nil), (*core.ComponentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ComponentStatus_To_core_ComponentStatus(a.(*corev1.ComponentStatus), b.(*core.ComponentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ComponentStatus)(nil), (*corev1.ComponentStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ComponentStatus_To_v1_ComponentStatus(a.(*core.ComponentStatus), b.(*corev1.ComponentStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ComponentStatusList)(nil), (*core.ComponentStatusList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ComponentStatusList_To_core_ComponentStatusList(a.(*corev1.ComponentStatusList), b.(*core.ComponentStatusList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ComponentStatusList)(nil), (*corev1.ComponentStatusList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ComponentStatusList_To_v1_ComponentStatusList(a.(*core.ComponentStatusList), b.(*corev1.ComponentStatusList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ConfigMap)(nil), (*core.ConfigMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMap_To_core_ConfigMap(a.(*corev1.ConfigMap), b.(*core.ConfigMap), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ConfigMap)(nil), (*corev1.ConfigMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ConfigMap_To_v1_ConfigMap(a.(*core.ConfigMap), b.(*corev1.ConfigMap), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ConfigMapEnvSource)(nil), (*core.ConfigMapEnvSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapEnvSource_To_core_ConfigMapEnvSource(a.(*corev1.ConfigMapEnvSource), b.(*core.ConfigMapEnvSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ConfigMapEnvSource)(nil), (*corev1.ConfigMapEnvSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ConfigMapEnvSource_To_v1_ConfigMapEnvSource(a.(*core.ConfigMapEnvSource), b.(*corev1.ConfigMapEnvSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ConfigMapKeySelector)(nil), (*core.ConfigMapKeySelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapKeySelector_To_core_ConfigMapKeySelector(a.(*corev1.ConfigMapKeySelector), b.(*core.ConfigMapKeySelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ConfigMapKeySelector)(nil), (*corev1.ConfigMapKeySelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ConfigMapKeySelector_To_v1_ConfigMapKeySelector(a.(*core.ConfigMapKeySelector), b.(*corev1.ConfigMapKeySelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ConfigMapList)(nil), (*core.ConfigMapList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapList_To_core_ConfigMapList(a.(*corev1.ConfigMapList), b.(*core.ConfigMapList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ConfigMapList)(nil), (*corev1.ConfigMapList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ConfigMapList_To_v1_ConfigMapList(a.(*core.ConfigMapList), b.(*corev1.ConfigMapList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ConfigMapNodeConfigSource)(nil), (*core.ConfigMapNodeConfigSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapNodeConfigSource_To_core_ConfigMapNodeConfigSource(a.(*corev1.ConfigMapNodeConfigSource), b.(*core.ConfigMapNodeConfigSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ConfigMapNodeConfigSource)(nil), (*corev1.ConfigMapNodeConfigSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ConfigMapNodeConfigSource_To_v1_ConfigMapNodeConfigSource(a.(*core.ConfigMapNodeConfigSource), b.(*corev1.ConfigMapNodeConfigSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ConfigMapProjection)(nil), (*core.ConfigMapProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapProjection_To_core_ConfigMapProjection(a.(*corev1.ConfigMapProjection), b.(*core.ConfigMapProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ConfigMapProjection)(nil), (*corev1.ConfigMapProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ConfigMapProjection_To_v1_ConfigMapProjection(a.(*core.ConfigMapProjection), b.(*corev1.ConfigMapProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ConfigMapVolumeSource)(nil), (*core.ConfigMapVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapVolumeSource_To_core_ConfigMapVolumeSource(a.(*corev1.ConfigMapVolumeSource), b.(*core.ConfigMapVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ConfigMapVolumeSource)(nil), (*corev1.ConfigMapVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ConfigMapVolumeSource_To_v1_ConfigMapVolumeSource(a.(*core.ConfigMapVolumeSource), b.(*corev1.ConfigMapVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Container)(nil), (*core.Container)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Container_To_core_Container(a.(*corev1.Container), b.(*core.Container), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Container)(nil), (*corev1.Container)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Container_To_v1_Container(a.(*core.Container), b.(*corev1.Container), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerImage)(nil), (*core.ContainerImage)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerImage_To_core_ContainerImage(a.(*corev1.ContainerImage), b.(*core.ContainerImage), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerImage)(nil), (*corev1.ContainerImage)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerImage_To_v1_ContainerImage(a.(*core.ContainerImage), b.(*corev1.ContainerImage), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerPort)(nil), (*core.ContainerPort)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerPort_To_core_ContainerPort(a.(*corev1.ContainerPort), b.(*core.ContainerPort), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerPort)(nil), (*corev1.ContainerPort)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerPort_To_v1_ContainerPort(a.(*core.ContainerPort), b.(*corev1.ContainerPort), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerResizePolicy)(nil), (*core.ContainerResizePolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerResizePolicy_To_core_ContainerResizePolicy(a.(*corev1.ContainerResizePolicy), b.(*core.ContainerResizePolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerResizePolicy)(nil), (*corev1.ContainerResizePolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerResizePolicy_To_v1_ContainerResizePolicy(a.(*core.ContainerResizePolicy), b.(*corev1.ContainerResizePolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerState)(nil), (*core.ContainerState)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerState_To_core_ContainerState(a.(*corev1.ContainerState), b.(*core.ContainerState), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerState)(nil), (*corev1.ContainerState)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerState_To_v1_ContainerState(a.(*core.ContainerState), b.(*corev1.ContainerState), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerStateRunning)(nil), (*core.ContainerStateRunning)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerStateRunning_To_core_ContainerStateRunning(a.(*corev1.ContainerStateRunning), b.(*core.ContainerStateRunning), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerStateRunning)(nil), (*corev1.ContainerStateRunning)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerStateRunning_To_v1_ContainerStateRunning(a.(*core.ContainerStateRunning), b.(*corev1.ContainerStateRunning), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerStateTerminated)(nil), (*core.ContainerStateTerminated)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerStateTerminated_To_core_ContainerStateTerminated(a.(*corev1.ContainerStateTerminated), b.(*core.ContainerStateTerminated), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerStateTerminated)(nil), (*corev1.ContainerStateTerminated)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerStateTerminated_To_v1_ContainerStateTerminated(a.(*core.ContainerStateTerminated), b.(*corev1.ContainerStateTerminated), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerStateWaiting)(nil), (*core.ContainerStateWaiting)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerStateWaiting_To_core_ContainerStateWaiting(a.(*corev1.ContainerStateWaiting), b.(*core.ContainerStateWaiting), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerStateWaiting)(nil), (*corev1.ContainerStateWaiting)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerStateWaiting_To_v1_ContainerStateWaiting(a.(*core.ContainerStateWaiting), b.(*corev1.ContainerStateWaiting), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerStatus)(nil), (*core.ContainerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerStatus_To_core_ContainerStatus(a.(*corev1.ContainerStatus), b.(*core.ContainerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerStatus)(nil), (*corev1.ContainerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerStatus_To_v1_ContainerStatus(a.(*core.ContainerStatus), b.(*corev1.ContainerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ContainerUser)(nil), (*core.ContainerUser)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ContainerUser_To_core_ContainerUser(a.(*corev1.ContainerUser), b.(*core.ContainerUser), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ContainerUser)(nil), (*corev1.ContainerUser)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ContainerUser_To_v1_ContainerUser(a.(*core.ContainerUser), b.(*corev1.ContainerUser), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.DaemonEndpoint)(nil), (*core.DaemonEndpoint)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DaemonEndpoint_To_core_DaemonEndpoint(a.(*corev1.DaemonEndpoint), b.(*core.DaemonEndpoint), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.DaemonEndpoint)(nil), (*corev1.DaemonEndpoint)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_DaemonEndpoint_To_v1_DaemonEndpoint(a.(*core.DaemonEndpoint), b.(*corev1.DaemonEndpoint), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.DownwardAPIProjection)(nil), (*core.DownwardAPIProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DownwardAPIProjection_To_core_DownwardAPIProjection(a.(*corev1.DownwardAPIProjection), b.(*core.DownwardAPIProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.DownwardAPIProjection)(nil), (*corev1.DownwardAPIProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_DownwardAPIProjection_To_v1_DownwardAPIProjection(a.(*core.DownwardAPIProjection), b.(*corev1.DownwardAPIProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.DownwardAPIVolumeFile)(nil), (*core.DownwardAPIVolumeFile)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DownwardAPIVolumeFile_To_core_DownwardAPIVolumeFile(a.(*corev1.DownwardAPIVolumeFile), b.(*core.DownwardAPIVolumeFile), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.DownwardAPIVolumeFile)(nil), (*corev1.DownwardAPIVolumeFile)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_DownwardAPIVolumeFile_To_v1_DownwardAPIVolumeFile(a.(*core.DownwardAPIVolumeFile), b.(*corev1.DownwardAPIVolumeFile), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.DownwardAPIVolumeSource)(nil), (*core.DownwardAPIVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_DownwardAPIVolumeSource_To_core_DownwardAPIVolumeSource(a.(*corev1.DownwardAPIVolumeSource), b.(*core.DownwardAPIVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.DownwardAPIVolumeSource)(nil), (*corev1.DownwardAPIVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_DownwardAPIVolumeSource_To_v1_DownwardAPIVolumeSource(a.(*core.DownwardAPIVolumeSource), b.(*corev1.DownwardAPIVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EmptyDirVolumeSource)(nil), (*core.EmptyDirVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EmptyDirVolumeSource_To_core_EmptyDirVolumeSource(a.(*corev1.EmptyDirVolumeSource), b.(*core.EmptyDirVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EmptyDirVolumeSource)(nil), (*corev1.EmptyDirVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EmptyDirVolumeSource_To_v1_EmptyDirVolumeSource(a.(*core.EmptyDirVolumeSource), b.(*corev1.EmptyDirVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EndpointAddress)(nil), (*core.EndpointAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EndpointAddress_To_core_EndpointAddress(a.(*corev1.EndpointAddress), b.(*core.EndpointAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EndpointAddress)(nil), (*corev1.EndpointAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EndpointAddress_To_v1_EndpointAddress(a.(*core.EndpointAddress), b.(*corev1.EndpointAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EndpointPort)(nil), (*core.EndpointPort)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EndpointPort_To_core_EndpointPort(a.(*corev1.EndpointPort), b.(*core.EndpointPort), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EndpointPort)(nil), (*corev1.EndpointPort)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EndpointPort_To_v1_EndpointPort(a.(*core.EndpointPort), b.(*corev1.EndpointPort), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EndpointSubset)(nil), (*core.EndpointSubset)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EndpointSubset_To_core_EndpointSubset(a.(*corev1.EndpointSubset), b.(*core.EndpointSubset), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EndpointSubset)(nil), (*corev1.EndpointSubset)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EndpointSubset_To_v1_EndpointSubset(a.(*core.EndpointSubset), b.(*corev1.EndpointSubset), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Endpoints)(nil), (*core.Endpoints)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Endpoints_To_core_Endpoints(a.(*corev1.Endpoints), b.(*core.Endpoints), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Endpoints)(nil), (*corev1.Endpoints)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Endpoints_To_v1_Endpoints(a.(*core.Endpoints), b.(*corev1.Endpoints), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EndpointsList)(nil), (*core.EndpointsList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EndpointsList_To_core_EndpointsList(a.(*corev1.EndpointsList), b.(*core.EndpointsList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EndpointsList)(nil), (*corev1.EndpointsList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EndpointsList_To_v1_EndpointsList(a.(*core.EndpointsList), b.(*corev1.EndpointsList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EnvFromSource)(nil), (*core.EnvFromSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EnvFromSource_To_core_EnvFromSource(a.(*corev1.EnvFromSource), b.(*core.EnvFromSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EnvFromSource)(nil), (*corev1.EnvFromSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EnvFromSource_To_v1_EnvFromSource(a.(*core.EnvFromSource), b.(*corev1.EnvFromSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EnvVar)(nil), (*core.EnvVar)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EnvVar_To_core_EnvVar(a.(*corev1.EnvVar), b.(*core.EnvVar), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EnvVar)(nil), (*corev1.EnvVar)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EnvVar_To_v1_EnvVar(a.(*core.EnvVar), b.(*corev1.EnvVar), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EnvVarSource)(nil), (*core.EnvVarSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EnvVarSource_To_core_EnvVarSource(a.(*corev1.EnvVarSource), b.(*core.EnvVarSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EnvVarSource)(nil), (*corev1.EnvVarSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EnvVarSource_To_v1_EnvVarSource(a.(*core.EnvVarSource), b.(*corev1.EnvVarSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EphemeralContainer)(nil), (*core.EphemeralContainer)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EphemeralContainer_To_core_EphemeralContainer(a.(*corev1.EphemeralContainer), b.(*core.EphemeralContainer), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EphemeralContainer)(nil), (*corev1.EphemeralContainer)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EphemeralContainer_To_v1_EphemeralContainer(a.(*core.EphemeralContainer), b.(*corev1.EphemeralContainer), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EphemeralContainerCommon)(nil), (*core.EphemeralContainerCommon)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EphemeralContainerCommon_To_core_EphemeralContainerCommon(a.(*corev1.EphemeralContainerCommon), b.(*core.EphemeralContainerCommon), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EphemeralContainerCommon)(nil), (*corev1.EphemeralContainerCommon)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EphemeralContainerCommon_To_v1_EphemeralContainerCommon(a.(*core.EphemeralContainerCommon), b.(*corev1.EphemeralContainerCommon), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EphemeralVolumeSource)(nil), (*core.EphemeralVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EphemeralVolumeSource_To_core_EphemeralVolumeSource(a.(*corev1.EphemeralVolumeSource), b.(*core.EphemeralVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EphemeralVolumeSource)(nil), (*corev1.EphemeralVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EphemeralVolumeSource_To_v1_EphemeralVolumeSource(a.(*core.EphemeralVolumeSource), b.(*corev1.EphemeralVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Event)(nil), (*core.Event)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Event_To_core_Event(a.(*corev1.Event), b.(*core.Event), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Event)(nil), (*corev1.Event)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Event_To_v1_Event(a.(*core.Event), b.(*corev1.Event), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EventList)(nil), (*core.EventList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EventList_To_core_EventList(a.(*corev1.EventList), b.(*core.EventList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EventList)(nil), (*corev1.EventList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EventList_To_v1_EventList(a.(*core.EventList), b.(*corev1.EventList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EventSeries)(nil), (*core.EventSeries)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EventSeries_To_core_EventSeries(a.(*corev1.EventSeries), b.(*core.EventSeries), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EventSeries)(nil), (*corev1.EventSeries)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EventSeries_To_v1_EventSeries(a.(*core.EventSeries), b.(*corev1.EventSeries), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.EventSource)(nil), (*core.EventSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_EventSource_To_core_EventSource(a.(*corev1.EventSource), b.(*core.EventSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.EventSource)(nil), (*corev1.EventSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_EventSource_To_v1_EventSource(a.(*core.EventSource), b.(*corev1.EventSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ExecAction)(nil), (*core.ExecAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ExecAction_To_core_ExecAction(a.(*corev1.ExecAction), b.(*core.ExecAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ExecAction)(nil), (*corev1.ExecAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ExecAction_To_v1_ExecAction(a.(*core.ExecAction), b.(*corev1.ExecAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.FCVolumeSource)(nil), (*core.FCVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_FCVolumeSource_To_core_FCVolumeSource(a.(*corev1.FCVolumeSource), b.(*core.FCVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.FCVolumeSource)(nil), (*corev1.FCVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_FCVolumeSource_To_v1_FCVolumeSource(a.(*core.FCVolumeSource), b.(*corev1.FCVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.FileKeySelector)(nil), (*core.FileKeySelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_FileKeySelector_To_core_FileKeySelector(a.(*corev1.FileKeySelector), b.(*core.FileKeySelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.FileKeySelector)(nil), (*corev1.FileKeySelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_FileKeySelector_To_v1_FileKeySelector(a.(*core.FileKeySelector), b.(*corev1.FileKeySelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.FlexPersistentVolumeSource)(nil), (*core.FlexPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_FlexPersistentVolumeSource_To_core_FlexPersistentVolumeSource(a.(*corev1.FlexPersistentVolumeSource), b.(*core.FlexPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.FlexPersistentVolumeSource)(nil), (*corev1.FlexPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_FlexPersistentVolumeSource_To_v1_FlexPersistentVolumeSource(a.(*core.FlexPersistentVolumeSource), b.(*corev1.FlexPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.FlexVolumeSource)(nil), (*core.FlexVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_FlexVolumeSource_To_core_FlexVolumeSource(a.(*corev1.FlexVolumeSource), b.(*core.FlexVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.FlexVolumeSource)(nil), (*corev1.FlexVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_FlexVolumeSource_To_v1_FlexVolumeSource(a.(*core.FlexVolumeSource), b.(*corev1.FlexVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.FlockerVolumeSource)(nil), (*core.FlockerVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_FlockerVolumeSource_To_core_FlockerVolumeSource(a.(*corev1.FlockerVolumeSource), b.(*core.FlockerVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.FlockerVolumeSource)(nil), (*corev1.FlockerVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_FlockerVolumeSource_To_v1_FlockerVolumeSource(a.(*core.FlockerVolumeSource), b.(*corev1.FlockerVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.GCEPersistentDiskVolumeSource)(nil), (*core.GCEPersistentDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GCEPersistentDiskVolumeSource_To_core_GCEPersistentDiskVolumeSource(a.(*corev1.GCEPersistentDiskVolumeSource), b.(*core.GCEPersistentDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.GCEPersistentDiskVolumeSource)(nil), (*corev1.GCEPersistentDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_GCEPersistentDiskVolumeSource_To_v1_GCEPersistentDiskVolumeSource(a.(*core.GCEPersistentDiskVolumeSource), b.(*corev1.GCEPersistentDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.GRPCAction)(nil), (*core.GRPCAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GRPCAction_To_core_GRPCAction(a.(*corev1.GRPCAction), b.(*core.GRPCAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.GRPCAction)(nil), (*corev1.GRPCAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_GRPCAction_To_v1_GRPCAction(a.(*core.GRPCAction), b.(*corev1.GRPCAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.GitRepoVolumeSource)(nil), (*core.GitRepoVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GitRepoVolumeSource_To_core_GitRepoVolumeSource(a.(*corev1.GitRepoVolumeSource), b.(*core.GitRepoVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.GitRepoVolumeSource)(nil), (*corev1.GitRepoVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_GitRepoVolumeSource_To_v1_GitRepoVolumeSource(a.(*core.GitRepoVolumeSource), b.(*corev1.GitRepoVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.GlusterfsPersistentVolumeSource)(nil), (*core.GlusterfsPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GlusterfsPersistentVolumeSource_To_core_GlusterfsPersistentVolumeSource(a.(*corev1.GlusterfsPersistentVolumeSource), b.(*core.GlusterfsPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.GlusterfsPersistentVolumeSource)(nil), (*corev1.GlusterfsPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_GlusterfsPersistentVolumeSource_To_v1_GlusterfsPersistentVolumeSource(a.(*core.GlusterfsPersistentVolumeSource), b.(*corev1.GlusterfsPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.GlusterfsVolumeSource)(nil), (*core.GlusterfsVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_GlusterfsVolumeSource_To_core_GlusterfsVolumeSource(a.(*corev1.GlusterfsVolumeSource), b.(*core.GlusterfsVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.GlusterfsVolumeSource)(nil), (*corev1.GlusterfsVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_GlusterfsVolumeSource_To_v1_GlusterfsVolumeSource(a.(*core.GlusterfsVolumeSource), b.(*corev1.GlusterfsVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.HTTPGetAction)(nil), (*core.HTTPGetAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HTTPGetAction_To_core_HTTPGetAction(a.(*corev1.HTTPGetAction), b.(*core.HTTPGetAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.HTTPGetAction)(nil), (*corev1.HTTPGetAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_HTTPGetAction_To_v1_HTTPGetAction(a.(*core.HTTPGetAction), b.(*corev1.HTTPGetAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.HTTPHeader)(nil), (*core.HTTPHeader)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HTTPHeader_To_core_HTTPHeader(a.(*corev1.HTTPHeader), b.(*core.HTTPHeader), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.HTTPHeader)(nil), (*corev1.HTTPHeader)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_HTTPHeader_To_v1_HTTPHeader(a.(*core.HTTPHeader), b.(*corev1.HTTPHeader), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.HostAlias)(nil), (*core.HostAlias)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HostAlias_To_core_HostAlias(a.(*corev1.HostAlias), b.(*core.HostAlias), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.HostAlias)(nil), (*corev1.HostAlias)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_HostAlias_To_v1_HostAlias(a.(*core.HostAlias), b.(*corev1.HostAlias), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.HostIP)(nil), (*core.HostIP)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HostIP_To_core_HostIP(a.(*corev1.HostIP), b.(*core.HostIP), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.HostIP)(nil), (*corev1.HostIP)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_HostIP_To_v1_HostIP(a.(*core.HostIP), b.(*corev1.HostIP), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.HostPathVolumeSource)(nil), (*core.HostPathVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HostPathVolumeSource_To_core_HostPathVolumeSource(a.(*corev1.HostPathVolumeSource), b.(*core.HostPathVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.HostPathVolumeSource)(nil), (*corev1.HostPathVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_HostPathVolumeSource_To_v1_HostPathVolumeSource(a.(*core.HostPathVolumeSource), b.(*corev1.HostPathVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ISCSIPersistentVolumeSource)(nil), (*core.ISCSIPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ISCSIPersistentVolumeSource_To_core_ISCSIPersistentVolumeSource(a.(*corev1.ISCSIPersistentVolumeSource), b.(*core.ISCSIPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ISCSIPersistentVolumeSource)(nil), (*corev1.ISCSIPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ISCSIPersistentVolumeSource_To_v1_ISCSIPersistentVolumeSource(a.(*core.ISCSIPersistentVolumeSource), b.(*corev1.ISCSIPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ISCSIVolumeSource)(nil), (*core.ISCSIVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ISCSIVolumeSource_To_core_ISCSIVolumeSource(a.(*corev1.ISCSIVolumeSource), b.(*core.ISCSIVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ISCSIVolumeSource)(nil), (*corev1.ISCSIVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ISCSIVolumeSource_To_v1_ISCSIVolumeSource(a.(*core.ISCSIVolumeSource), b.(*corev1.ISCSIVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ImageVolumeSource)(nil), (*core.ImageVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ImageVolumeSource_To_core_ImageVolumeSource(a.(*corev1.ImageVolumeSource), b.(*core.ImageVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ImageVolumeSource)(nil), (*corev1.ImageVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ImageVolumeSource_To_v1_ImageVolumeSource(a.(*core.ImageVolumeSource), b.(*corev1.ImageVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.KeyToPath)(nil), (*core.KeyToPath)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_KeyToPath_To_core_KeyToPath(a.(*corev1.KeyToPath), b.(*core.KeyToPath), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.KeyToPath)(nil), (*corev1.KeyToPath)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_KeyToPath_To_v1_KeyToPath(a.(*core.KeyToPath), b.(*corev1.KeyToPath), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Lifecycle)(nil), (*core.Lifecycle)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Lifecycle_To_core_Lifecycle(a.(*corev1.Lifecycle), b.(*core.Lifecycle), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Lifecycle)(nil), (*corev1.Lifecycle)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Lifecycle_To_v1_Lifecycle(a.(*core.Lifecycle), b.(*corev1.Lifecycle), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LifecycleHandler)(nil), (*core.LifecycleHandler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LifecycleHandler_To_core_LifecycleHandler(a.(*corev1.LifecycleHandler), b.(*core.LifecycleHandler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LifecycleHandler)(nil), (*corev1.LifecycleHandler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LifecycleHandler_To_v1_LifecycleHandler(a.(*core.LifecycleHandler), b.(*corev1.LifecycleHandler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LimitRange)(nil), (*core.LimitRange)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LimitRange_To_core_LimitRange(a.(*corev1.LimitRange), b.(*core.LimitRange), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LimitRange)(nil), (*corev1.LimitRange)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LimitRange_To_v1_LimitRange(a.(*core.LimitRange), b.(*corev1.LimitRange), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LimitRangeItem)(nil), (*core.LimitRangeItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LimitRangeItem_To_core_LimitRangeItem(a.(*corev1.LimitRangeItem), b.(*core.LimitRangeItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LimitRangeItem)(nil), (*corev1.LimitRangeItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LimitRangeItem_To_v1_LimitRangeItem(a.(*core.LimitRangeItem), b.(*corev1.LimitRangeItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LimitRangeList)(nil), (*core.LimitRangeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LimitRangeList_To_core_LimitRangeList(a.(*corev1.LimitRangeList), b.(*core.LimitRangeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LimitRangeList)(nil), (*corev1.LimitRangeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LimitRangeList_To_v1_LimitRangeList(a.(*core.LimitRangeList), b.(*corev1.LimitRangeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LimitRangeSpec)(nil), (*core.LimitRangeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LimitRangeSpec_To_core_LimitRangeSpec(a.(*corev1.LimitRangeSpec), b.(*core.LimitRangeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LimitRangeSpec)(nil), (*corev1.LimitRangeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LimitRangeSpec_To_v1_LimitRangeSpec(a.(*core.LimitRangeSpec), b.(*corev1.LimitRangeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LinuxContainerUser)(nil), (*core.LinuxContainerUser)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LinuxContainerUser_To_core_LinuxContainerUser(a.(*corev1.LinuxContainerUser), b.(*core.LinuxContainerUser), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LinuxContainerUser)(nil), (*corev1.LinuxContainerUser)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LinuxContainerUser_To_v1_LinuxContainerUser(a.(*core.LinuxContainerUser), b.(*corev1.LinuxContainerUser), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.List)(nil), (*core.List)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_List_To_core_List(a.(*corev1.List), b.(*core.List), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.List)(nil), (*corev1.List)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_List_To_v1_List(a.(*core.List), b.(*corev1.List), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LoadBalancerIngress)(nil), (*core.LoadBalancerIngress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LoadBalancerIngress_To_core_LoadBalancerIngress(a.(*corev1.LoadBalancerIngress), b.(*core.LoadBalancerIngress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LoadBalancerIngress)(nil), (*corev1.LoadBalancerIngress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LoadBalancerIngress_To_v1_LoadBalancerIngress(a.(*core.LoadBalancerIngress), b.(*corev1.LoadBalancerIngress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LoadBalancerStatus)(nil), (*core.LoadBalancerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LoadBalancerStatus_To_core_LoadBalancerStatus(a.(*corev1.LoadBalancerStatus), b.(*core.LoadBalancerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LoadBalancerStatus)(nil), (*corev1.LoadBalancerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LoadBalancerStatus_To_v1_LoadBalancerStatus(a.(*core.LoadBalancerStatus), b.(*corev1.LoadBalancerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LocalObjectReference)(nil), (*core.LocalObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LocalObjectReference_To_core_LocalObjectReference(a.(*corev1.LocalObjectReference), b.(*core.LocalObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LocalObjectReference)(nil), (*corev1.LocalObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LocalObjectReference_To_v1_LocalObjectReference(a.(*core.LocalObjectReference), b.(*corev1.LocalObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.LocalVolumeSource)(nil), (*core.LocalVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LocalVolumeSource_To_core_LocalVolumeSource(a.(*corev1.LocalVolumeSource), b.(*core.LocalVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.LocalVolumeSource)(nil), (*corev1.LocalVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_LocalVolumeSource_To_v1_LocalVolumeSource(a.(*core.LocalVolumeSource), b.(*corev1.LocalVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ModifyVolumeStatus)(nil), (*core.ModifyVolumeStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ModifyVolumeStatus_To_core_ModifyVolumeStatus(a.(*corev1.ModifyVolumeStatus), b.(*core.ModifyVolumeStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ModifyVolumeStatus)(nil), (*corev1.ModifyVolumeStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ModifyVolumeStatus_To_v1_ModifyVolumeStatus(a.(*core.ModifyVolumeStatus), b.(*corev1.ModifyVolumeStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NFSVolumeSource)(nil), (*core.NFSVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NFSVolumeSource_To_core_NFSVolumeSource(a.(*corev1.NFSVolumeSource), b.(*core.NFSVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NFSVolumeSource)(nil), (*corev1.NFSVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NFSVolumeSource_To_v1_NFSVolumeSource(a.(*core.NFSVolumeSource), b.(*corev1.NFSVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Namespace)(nil), (*core.Namespace)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Namespace_To_core_Namespace(a.(*corev1.Namespace), b.(*core.Namespace), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Namespace)(nil), (*corev1.Namespace)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Namespace_To_v1_Namespace(a.(*core.Namespace), b.(*corev1.Namespace), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NamespaceCondition)(nil), (*core.NamespaceCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceCondition_To_core_NamespaceCondition(a.(*corev1.NamespaceCondition), b.(*core.NamespaceCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NamespaceCondition)(nil), (*corev1.NamespaceCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NamespaceCondition_To_v1_NamespaceCondition(a.(*core.NamespaceCondition), b.(*corev1.NamespaceCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NamespaceList)(nil), (*core.NamespaceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceList_To_core_NamespaceList(a.(*corev1.NamespaceList), b.(*core.NamespaceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NamespaceList)(nil), (*corev1.NamespaceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NamespaceList_To_v1_NamespaceList(a.(*core.NamespaceList), b.(*corev1.NamespaceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NamespaceSpec)(nil), (*core.NamespaceSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceSpec_To_core_NamespaceSpec(a.(*corev1.NamespaceSpec), b.(*core.NamespaceSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NamespaceSpec)(nil), (*corev1.NamespaceSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NamespaceSpec_To_v1_NamespaceSpec(a.(*core.NamespaceSpec), b.(*corev1.NamespaceSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NamespaceStatus)(nil), (*core.NamespaceStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NamespaceStatus_To_core_NamespaceStatus(a.(*corev1.NamespaceStatus), b.(*core.NamespaceStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NamespaceStatus)(nil), (*corev1.NamespaceStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NamespaceStatus_To_v1_NamespaceStatus(a.(*core.NamespaceStatus), b.(*corev1.NamespaceStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Node)(nil), (*core.Node)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Node_To_core_Node(a.(*corev1.Node), b.(*core.Node), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Node)(nil), (*corev1.Node)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Node_To_v1_Node(a.(*core.Node), b.(*corev1.Node), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeAddress)(nil), (*core.NodeAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeAddress_To_core_NodeAddress(a.(*corev1.NodeAddress), b.(*core.NodeAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeAddress)(nil), (*corev1.NodeAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeAddress_To_v1_NodeAddress(a.(*core.NodeAddress), b.(*corev1.NodeAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeAffinity)(nil), (*core.NodeAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeAffinity_To_core_NodeAffinity(a.(*corev1.NodeAffinity), b.(*core.NodeAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeAffinity)(nil), (*corev1.NodeAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeAffinity_To_v1_NodeAffinity(a.(*core.NodeAffinity), b.(*corev1.NodeAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeCondition)(nil), (*core.NodeCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeCondition_To_core_NodeCondition(a.(*corev1.NodeCondition), b.(*core.NodeCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeCondition)(nil), (*corev1.NodeCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeCondition_To_v1_NodeCondition(a.(*core.NodeCondition), b.(*corev1.NodeCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeConfigSource)(nil), (*core.NodeConfigSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeConfigSource_To_core_NodeConfigSource(a.(*corev1.NodeConfigSource), b.(*core.NodeConfigSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeConfigSource)(nil), (*corev1.NodeConfigSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeConfigSource_To_v1_NodeConfigSource(a.(*core.NodeConfigSource), b.(*corev1.NodeConfigSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeConfigStatus)(nil), (*core.NodeConfigStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeConfigStatus_To_core_NodeConfigStatus(a.(*corev1.NodeConfigStatus), b.(*core.NodeConfigStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeConfigStatus)(nil), (*corev1.NodeConfigStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeConfigStatus_To_v1_NodeConfigStatus(a.(*core.NodeConfigStatus), b.(*corev1.NodeConfigStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeDaemonEndpoints)(nil), (*core.NodeDaemonEndpoints)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeDaemonEndpoints_To_core_NodeDaemonEndpoints(a.(*corev1.NodeDaemonEndpoints), b.(*core.NodeDaemonEndpoints), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeDaemonEndpoints)(nil), (*corev1.NodeDaemonEndpoints)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeDaemonEndpoints_To_v1_NodeDaemonEndpoints(a.(*core.NodeDaemonEndpoints), b.(*corev1.NodeDaemonEndpoints), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeFeatures)(nil), (*core.NodeFeatures)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeFeatures_To_core_NodeFeatures(a.(*corev1.NodeFeatures), b.(*core.NodeFeatures), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeFeatures)(nil), (*corev1.NodeFeatures)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeFeatures_To_v1_NodeFeatures(a.(*core.NodeFeatures), b.(*corev1.NodeFeatures), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeList)(nil), (*core.NodeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeList_To_core_NodeList(a.(*corev1.NodeList), b.(*core.NodeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeList)(nil), (*corev1.NodeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeList_To_v1_NodeList(a.(*core.NodeList), b.(*corev1.NodeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeProxyOptions)(nil), (*core.NodeProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeProxyOptions_To_core_NodeProxyOptions(a.(*corev1.NodeProxyOptions), b.(*core.NodeProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeProxyOptions)(nil), (*corev1.NodeProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeProxyOptions_To_v1_NodeProxyOptions(a.(*core.NodeProxyOptions), b.(*corev1.NodeProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeRuntimeHandler)(nil), (*core.NodeRuntimeHandler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeRuntimeHandler_To_core_NodeRuntimeHandler(a.(*corev1.NodeRuntimeHandler), b.(*core.NodeRuntimeHandler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeRuntimeHandler)(nil), (*corev1.NodeRuntimeHandler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeRuntimeHandler_To_v1_NodeRuntimeHandler(a.(*core.NodeRuntimeHandler), b.(*corev1.NodeRuntimeHandler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeRuntimeHandlerFeatures)(nil), (*core.NodeRuntimeHandlerFeatures)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeRuntimeHandlerFeatures_To_core_NodeRuntimeHandlerFeatures(a.(*corev1.NodeRuntimeHandlerFeatures), b.(*core.NodeRuntimeHandlerFeatures), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeRuntimeHandlerFeatures)(nil), (*corev1.NodeRuntimeHandlerFeatures)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeRuntimeHandlerFeatures_To_v1_NodeRuntimeHandlerFeatures(a.(*core.NodeRuntimeHandlerFeatures), b.(*corev1.NodeRuntimeHandlerFeatures), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeSelector)(nil), (*core.NodeSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeSelector_To_core_NodeSelector(a.(*corev1.NodeSelector), b.(*core.NodeSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeSelector)(nil), (*corev1.NodeSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeSelector_To_v1_NodeSelector(a.(*core.NodeSelector), b.(*corev1.NodeSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeSelectorRequirement)(nil), (*core.NodeSelectorRequirement)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeSelectorRequirement_To_core_NodeSelectorRequirement(a.(*corev1.NodeSelectorRequirement), b.(*core.NodeSelectorRequirement), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeSelectorRequirement)(nil), (*corev1.NodeSelectorRequirement)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeSelectorRequirement_To_v1_NodeSelectorRequirement(a.(*core.NodeSelectorRequirement), b.(*corev1.NodeSelectorRequirement), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeSelectorTerm)(nil), (*core.NodeSelectorTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeSelectorTerm_To_core_NodeSelectorTerm(a.(*corev1.NodeSelectorTerm), b.(*core.NodeSelectorTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeSelectorTerm)(nil), (*corev1.NodeSelectorTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeSelectorTerm_To_v1_NodeSelectorTerm(a.(*core.NodeSelectorTerm), b.(*corev1.NodeSelectorTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeStatus)(nil), (*core.NodeStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeStatus_To_core_NodeStatus(a.(*corev1.NodeStatus), b.(*core.NodeStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeStatus)(nil), (*corev1.NodeStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeStatus_To_v1_NodeStatus(a.(*core.NodeStatus), b.(*corev1.NodeStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeSwapStatus)(nil), (*core.NodeSwapStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeSwapStatus_To_core_NodeSwapStatus(a.(*corev1.NodeSwapStatus), b.(*core.NodeSwapStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeSwapStatus)(nil), (*corev1.NodeSwapStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeSwapStatus_To_v1_NodeSwapStatus(a.(*core.NodeSwapStatus), b.(*corev1.NodeSwapStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.NodeSystemInfo)(nil), (*core.NodeSystemInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeSystemInfo_To_core_NodeSystemInfo(a.(*corev1.NodeSystemInfo), b.(*core.NodeSystemInfo), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.NodeSystemInfo)(nil), (*corev1.NodeSystemInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeSystemInfo_To_v1_NodeSystemInfo(a.(*core.NodeSystemInfo), b.(*corev1.NodeSystemInfo), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ObjectFieldSelector)(nil), (*core.ObjectFieldSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ObjectFieldSelector_To_core_ObjectFieldSelector(a.(*corev1.ObjectFieldSelector), b.(*core.ObjectFieldSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ObjectFieldSelector)(nil), (*corev1.ObjectFieldSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ObjectFieldSelector_To_v1_ObjectFieldSelector(a.(*core.ObjectFieldSelector), b.(*corev1.ObjectFieldSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ObjectReference)(nil), (*core.ObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ObjectReference_To_core_ObjectReference(a.(*corev1.ObjectReference), b.(*core.ObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ObjectReference)(nil), (*corev1.ObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ObjectReference_To_v1_ObjectReference(a.(*core.ObjectReference), b.(*corev1.ObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolume)(nil), (*core.PersistentVolume)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolume_To_core_PersistentVolume(a.(*corev1.PersistentVolume), b.(*core.PersistentVolume), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolume)(nil), (*corev1.PersistentVolume)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolume_To_v1_PersistentVolume(a.(*core.PersistentVolume), b.(*corev1.PersistentVolume), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeClaim)(nil), (*core.PersistentVolumeClaim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeClaim_To_core_PersistentVolumeClaim(a.(*corev1.PersistentVolumeClaim), b.(*core.PersistentVolumeClaim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeClaim)(nil), (*corev1.PersistentVolumeClaim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeClaim_To_v1_PersistentVolumeClaim(a.(*core.PersistentVolumeClaim), b.(*corev1.PersistentVolumeClaim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeClaimCondition)(nil), (*core.PersistentVolumeClaimCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeClaimCondition_To_core_PersistentVolumeClaimCondition(a.(*corev1.PersistentVolumeClaimCondition), b.(*core.PersistentVolumeClaimCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeClaimCondition)(nil), (*corev1.PersistentVolumeClaimCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeClaimCondition_To_v1_PersistentVolumeClaimCondition(a.(*core.PersistentVolumeClaimCondition), b.(*corev1.PersistentVolumeClaimCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeClaimList)(nil), (*core.PersistentVolumeClaimList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeClaimList_To_core_PersistentVolumeClaimList(a.(*corev1.PersistentVolumeClaimList), b.(*core.PersistentVolumeClaimList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeClaimList)(nil), (*corev1.PersistentVolumeClaimList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeClaimList_To_v1_PersistentVolumeClaimList(a.(*core.PersistentVolumeClaimList), b.(*corev1.PersistentVolumeClaimList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeClaimSpec)(nil), (*core.PersistentVolumeClaimSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeClaimSpec_To_core_PersistentVolumeClaimSpec(a.(*corev1.PersistentVolumeClaimSpec), b.(*core.PersistentVolumeClaimSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeClaimSpec)(nil), (*corev1.PersistentVolumeClaimSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeClaimSpec_To_v1_PersistentVolumeClaimSpec(a.(*core.PersistentVolumeClaimSpec), b.(*corev1.PersistentVolumeClaimSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeClaimStatus)(nil), (*core.PersistentVolumeClaimStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeClaimStatus_To_core_PersistentVolumeClaimStatus(a.(*corev1.PersistentVolumeClaimStatus), b.(*core.PersistentVolumeClaimStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeClaimStatus)(nil), (*corev1.PersistentVolumeClaimStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeClaimStatus_To_v1_PersistentVolumeClaimStatus(a.(*core.PersistentVolumeClaimStatus), b.(*corev1.PersistentVolumeClaimStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeClaimTemplate)(nil), (*core.PersistentVolumeClaimTemplate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeClaimTemplate_To_core_PersistentVolumeClaimTemplate(a.(*corev1.PersistentVolumeClaimTemplate), b.(*core.PersistentVolumeClaimTemplate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeClaimTemplate)(nil), (*corev1.PersistentVolumeClaimTemplate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeClaimTemplate_To_v1_PersistentVolumeClaimTemplate(a.(*core.PersistentVolumeClaimTemplate), b.(*corev1.PersistentVolumeClaimTemplate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeClaimVolumeSource)(nil), (*core.PersistentVolumeClaimVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeClaimVolumeSource_To_core_PersistentVolumeClaimVolumeSource(a.(*corev1.PersistentVolumeClaimVolumeSource), b.(*core.PersistentVolumeClaimVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeClaimVolumeSource)(nil), (*corev1.PersistentVolumeClaimVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeClaimVolumeSource_To_v1_PersistentVolumeClaimVolumeSource(a.(*core.PersistentVolumeClaimVolumeSource), b.(*corev1.PersistentVolumeClaimVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeList)(nil), (*core.PersistentVolumeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeList_To_core_PersistentVolumeList(a.(*corev1.PersistentVolumeList), b.(*core.PersistentVolumeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeList)(nil), (*corev1.PersistentVolumeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeList_To_v1_PersistentVolumeList(a.(*core.PersistentVolumeList), b.(*corev1.PersistentVolumeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeSource)(nil), (*core.PersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeSource_To_core_PersistentVolumeSource(a.(*corev1.PersistentVolumeSource), b.(*core.PersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeSource)(nil), (*corev1.PersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeSource_To_v1_PersistentVolumeSource(a.(*core.PersistentVolumeSource), b.(*corev1.PersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PersistentVolumeStatus)(nil), (*core.PersistentVolumeStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeStatus_To_core_PersistentVolumeStatus(a.(*corev1.PersistentVolumeStatus), b.(*core.PersistentVolumeStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PersistentVolumeStatus)(nil), (*corev1.PersistentVolumeStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeStatus_To_v1_PersistentVolumeStatus(a.(*core.PersistentVolumeStatus), b.(*corev1.PersistentVolumeStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PhotonPersistentDiskVolumeSource)(nil), (*core.PhotonPersistentDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PhotonPersistentDiskVolumeSource_To_core_PhotonPersistentDiskVolumeSource(a.(*corev1.PhotonPersistentDiskVolumeSource), b.(*core.PhotonPersistentDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PhotonPersistentDiskVolumeSource)(nil), (*corev1.PhotonPersistentDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PhotonPersistentDiskVolumeSource_To_v1_PhotonPersistentDiskVolumeSource(a.(*core.PhotonPersistentDiskVolumeSource), b.(*corev1.PhotonPersistentDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodAffinity)(nil), (*core.PodAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodAffinity_To_core_PodAffinity(a.(*corev1.PodAffinity), b.(*core.PodAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodAffinity)(nil), (*corev1.PodAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodAffinity_To_v1_PodAffinity(a.(*core.PodAffinity), b.(*corev1.PodAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodAffinityTerm)(nil), (*core.PodAffinityTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodAffinityTerm_To_core_PodAffinityTerm(a.(*corev1.PodAffinityTerm), b.(*core.PodAffinityTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodAffinityTerm)(nil), (*corev1.PodAffinityTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodAffinityTerm_To_v1_PodAffinityTerm(a.(*core.PodAffinityTerm), b.(*corev1.PodAffinityTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodAntiAffinity)(nil), (*core.PodAntiAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodAntiAffinity_To_core_PodAntiAffinity(a.(*corev1.PodAntiAffinity), b.(*core.PodAntiAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodAntiAffinity)(nil), (*corev1.PodAntiAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodAntiAffinity_To_v1_PodAntiAffinity(a.(*core.PodAntiAffinity), b.(*corev1.PodAntiAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodAttachOptions)(nil), (*core.PodAttachOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodAttachOptions_To_core_PodAttachOptions(a.(*corev1.PodAttachOptions), b.(*core.PodAttachOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodAttachOptions)(nil), (*corev1.PodAttachOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodAttachOptions_To_v1_PodAttachOptions(a.(*core.PodAttachOptions), b.(*corev1.PodAttachOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodCertificateProjection)(nil), (*core.PodCertificateProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodCertificateProjection_To_core_PodCertificateProjection(a.(*corev1.PodCertificateProjection), b.(*core.PodCertificateProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodCertificateProjection)(nil), (*corev1.PodCertificateProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodCertificateProjection_To_v1_PodCertificateProjection(a.(*core.PodCertificateProjection), b.(*corev1.PodCertificateProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodCondition)(nil), (*core.PodCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodCondition_To_core_PodCondition(a.(*corev1.PodCondition), b.(*core.PodCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodCondition)(nil), (*corev1.PodCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodCondition_To_v1_PodCondition(a.(*core.PodCondition), b.(*corev1.PodCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodDNSConfig)(nil), (*core.PodDNSConfig)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodDNSConfig_To_core_PodDNSConfig(a.(*corev1.PodDNSConfig), b.(*core.PodDNSConfig), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodDNSConfig)(nil), (*corev1.PodDNSConfig)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodDNSConfig_To_v1_PodDNSConfig(a.(*core.PodDNSConfig), b.(*corev1.PodDNSConfig), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodDNSConfigOption)(nil), (*core.PodDNSConfigOption)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodDNSConfigOption_To_core_PodDNSConfigOption(a.(*corev1.PodDNSConfigOption), b.(*core.PodDNSConfigOption), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodDNSConfigOption)(nil), (*corev1.PodDNSConfigOption)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodDNSConfigOption_To_v1_PodDNSConfigOption(a.(*core.PodDNSConfigOption), b.(*corev1.PodDNSConfigOption), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodExecOptions)(nil), (*core.PodExecOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodExecOptions_To_core_PodExecOptions(a.(*corev1.PodExecOptions), b.(*core.PodExecOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodExecOptions)(nil), (*corev1.PodExecOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodExecOptions_To_v1_PodExecOptions(a.(*core.PodExecOptions), b.(*corev1.PodExecOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodIP)(nil), (*core.PodIP)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodIP_To_core_PodIP(a.(*corev1.PodIP), b.(*core.PodIP), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodIP)(nil), (*corev1.PodIP)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodIP_To_v1_PodIP(a.(*core.PodIP), b.(*corev1.PodIP), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodList)(nil), (*core.PodList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodList_To_core_PodList(a.(*corev1.PodList), b.(*core.PodList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodList)(nil), (*corev1.PodList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodList_To_v1_PodList(a.(*core.PodList), b.(*corev1.PodList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodLogOptions)(nil), (*core.PodLogOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodLogOptions_To_core_PodLogOptions(a.(*corev1.PodLogOptions), b.(*core.PodLogOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodLogOptions)(nil), (*corev1.PodLogOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodLogOptions_To_v1_PodLogOptions(a.(*core.PodLogOptions), b.(*corev1.PodLogOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodOS)(nil), (*core.PodOS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodOS_To_core_PodOS(a.(*corev1.PodOS), b.(*core.PodOS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodOS)(nil), (*corev1.PodOS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodOS_To_v1_PodOS(a.(*core.PodOS), b.(*corev1.PodOS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodPortForwardOptions)(nil), (*core.PodPortForwardOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodPortForwardOptions_To_core_PodPortForwardOptions(a.(*corev1.PodPortForwardOptions), b.(*core.PodPortForwardOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodPortForwardOptions)(nil), (*corev1.PodPortForwardOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodPortForwardOptions_To_v1_PodPortForwardOptions(a.(*core.PodPortForwardOptions), b.(*corev1.PodPortForwardOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodProxyOptions)(nil), (*core.PodProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodProxyOptions_To_core_PodProxyOptions(a.(*corev1.PodProxyOptions), b.(*core.PodProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodProxyOptions)(nil), (*corev1.PodProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodProxyOptions_To_v1_PodProxyOptions(a.(*core.PodProxyOptions), b.(*corev1.PodProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodReadinessGate)(nil), (*core.PodReadinessGate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodReadinessGate_To_core_PodReadinessGate(a.(*corev1.PodReadinessGate), b.(*core.PodReadinessGate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodReadinessGate)(nil), (*corev1.PodReadinessGate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodReadinessGate_To_v1_PodReadinessGate(a.(*core.PodReadinessGate), b.(*corev1.PodReadinessGate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodResourceClaim)(nil), (*core.PodResourceClaim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodResourceClaim_To_core_PodResourceClaim(a.(*corev1.PodResourceClaim), b.(*core.PodResourceClaim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodResourceClaim)(nil), (*corev1.PodResourceClaim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodResourceClaim_To_v1_PodResourceClaim(a.(*core.PodResourceClaim), b.(*corev1.PodResourceClaim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodResourceClaimStatus)(nil), (*core.PodResourceClaimStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodResourceClaimStatus_To_core_PodResourceClaimStatus(a.(*corev1.PodResourceClaimStatus), b.(*core.PodResourceClaimStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodResourceClaimStatus)(nil), (*corev1.PodResourceClaimStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodResourceClaimStatus_To_v1_PodResourceClaimStatus(a.(*core.PodResourceClaimStatus), b.(*corev1.PodResourceClaimStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodSchedulingGate)(nil), (*core.PodSchedulingGate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodSchedulingGate_To_core_PodSchedulingGate(a.(*corev1.PodSchedulingGate), b.(*core.PodSchedulingGate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodSchedulingGate)(nil), (*corev1.PodSchedulingGate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodSchedulingGate_To_v1_PodSchedulingGate(a.(*core.PodSchedulingGate), b.(*corev1.PodSchedulingGate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodSecurityContext)(nil), (*core.PodSecurityContext)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodSecurityContext_To_core_PodSecurityContext(a.(*corev1.PodSecurityContext), b.(*core.PodSecurityContext), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodSecurityContext)(nil), (*corev1.PodSecurityContext)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodSecurityContext_To_v1_PodSecurityContext(a.(*core.PodSecurityContext), b.(*corev1.PodSecurityContext), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodSignature)(nil), (*core.PodSignature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodSignature_To_core_PodSignature(a.(*corev1.PodSignature), b.(*core.PodSignature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodSignature)(nil), (*corev1.PodSignature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodSignature_To_v1_PodSignature(a.(*core.PodSignature), b.(*corev1.PodSignature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodStatusResult)(nil), (*core.PodStatusResult)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodStatusResult_To_core_PodStatusResult(a.(*corev1.PodStatusResult), b.(*core.PodStatusResult), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodStatusResult)(nil), (*corev1.PodStatusResult)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodStatusResult_To_v1_PodStatusResult(a.(*core.PodStatusResult), b.(*corev1.PodStatusResult), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodTemplate)(nil), (*core.PodTemplate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodTemplate_To_core_PodTemplate(a.(*corev1.PodTemplate), b.(*core.PodTemplate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodTemplate)(nil), (*corev1.PodTemplate)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodTemplate_To_v1_PodTemplate(a.(*core.PodTemplate), b.(*corev1.PodTemplate), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PodTemplateList)(nil), (*core.PodTemplateList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodTemplateList_To_core_PodTemplateList(a.(*corev1.PodTemplateList), b.(*core.PodTemplateList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PodTemplateList)(nil), (*corev1.PodTemplateList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodTemplateList_To_v1_PodTemplateList(a.(*core.PodTemplateList), b.(*corev1.PodTemplateList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PortStatus)(nil), (*core.PortStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PortStatus_To_core_PortStatus(a.(*corev1.PortStatus), b.(*core.PortStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PortStatus)(nil), (*corev1.PortStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PortStatus_To_v1_PortStatus(a.(*core.PortStatus), b.(*corev1.PortStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PortworxVolumeSource)(nil), (*core.PortworxVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PortworxVolumeSource_To_core_PortworxVolumeSource(a.(*corev1.PortworxVolumeSource), b.(*core.PortworxVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PortworxVolumeSource)(nil), (*corev1.PortworxVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PortworxVolumeSource_To_v1_PortworxVolumeSource(a.(*core.PortworxVolumeSource), b.(*corev1.PortworxVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Preconditions)(nil), (*core.Preconditions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Preconditions_To_core_Preconditions(a.(*corev1.Preconditions), b.(*core.Preconditions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Preconditions)(nil), (*corev1.Preconditions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Preconditions_To_v1_Preconditions(a.(*core.Preconditions), b.(*corev1.Preconditions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PreferAvoidPodsEntry)(nil), (*core.PreferAvoidPodsEntry)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PreferAvoidPodsEntry_To_core_PreferAvoidPodsEntry(a.(*corev1.PreferAvoidPodsEntry), b.(*core.PreferAvoidPodsEntry), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PreferAvoidPodsEntry)(nil), (*corev1.PreferAvoidPodsEntry)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PreferAvoidPodsEntry_To_v1_PreferAvoidPodsEntry(a.(*core.PreferAvoidPodsEntry), b.(*corev1.PreferAvoidPodsEntry), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.PreferredSchedulingTerm)(nil), (*core.PreferredSchedulingTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PreferredSchedulingTerm_To_core_PreferredSchedulingTerm(a.(*corev1.PreferredSchedulingTerm), b.(*core.PreferredSchedulingTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.PreferredSchedulingTerm)(nil), (*corev1.PreferredSchedulingTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PreferredSchedulingTerm_To_v1_PreferredSchedulingTerm(a.(*core.PreferredSchedulingTerm), b.(*corev1.PreferredSchedulingTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Probe)(nil), (*core.Probe)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Probe_To_core_Probe(a.(*corev1.Probe), b.(*core.Probe), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Probe)(nil), (*corev1.Probe)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Probe_To_v1_Probe(a.(*core.Probe), b.(*corev1.Probe), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ProbeHandler)(nil), (*core.ProbeHandler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ProbeHandler_To_core_ProbeHandler(a.(*corev1.ProbeHandler), b.(*core.ProbeHandler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ProbeHandler)(nil), (*corev1.ProbeHandler)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ProbeHandler_To_v1_ProbeHandler(a.(*core.ProbeHandler), b.(*corev1.ProbeHandler), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ProjectedVolumeSource)(nil), (*core.ProjectedVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ProjectedVolumeSource_To_core_ProjectedVolumeSource(a.(*corev1.ProjectedVolumeSource), b.(*core.ProjectedVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ProjectedVolumeSource)(nil), (*corev1.ProjectedVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ProjectedVolumeSource_To_v1_ProjectedVolumeSource(a.(*core.ProjectedVolumeSource), b.(*corev1.ProjectedVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.QuobyteVolumeSource)(nil), (*core.QuobyteVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_QuobyteVolumeSource_To_core_QuobyteVolumeSource(a.(*corev1.QuobyteVolumeSource), b.(*core.QuobyteVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.QuobyteVolumeSource)(nil), (*corev1.QuobyteVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_QuobyteVolumeSource_To_v1_QuobyteVolumeSource(a.(*core.QuobyteVolumeSource), b.(*corev1.QuobyteVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.RBDPersistentVolumeSource)(nil), (*core.RBDPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RBDPersistentVolumeSource_To_core_RBDPersistentVolumeSource(a.(*corev1.RBDPersistentVolumeSource), b.(*core.RBDPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.RBDPersistentVolumeSource)(nil), (*corev1.RBDPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_RBDPersistentVolumeSource_To_v1_RBDPersistentVolumeSource(a.(*core.RBDPersistentVolumeSource), b.(*corev1.RBDPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.RBDVolumeSource)(nil), (*core.RBDVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RBDVolumeSource_To_core_RBDVolumeSource(a.(*corev1.RBDVolumeSource), b.(*core.RBDVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.RBDVolumeSource)(nil), (*corev1.RBDVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_RBDVolumeSource_To_v1_RBDVolumeSource(a.(*core.RBDVolumeSource), b.(*corev1.RBDVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.RangeAllocation)(nil), (*core.RangeAllocation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RangeAllocation_To_core_RangeAllocation(a.(*corev1.RangeAllocation), b.(*core.RangeAllocation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.RangeAllocation)(nil), (*corev1.RangeAllocation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_RangeAllocation_To_v1_RangeAllocation(a.(*core.RangeAllocation), b.(*corev1.RangeAllocation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ReplicationController)(nil), (*core.ReplicationController)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationController_To_core_ReplicationController(a.(*corev1.ReplicationController), b.(*core.ReplicationController), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ReplicationController)(nil), (*corev1.ReplicationController)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ReplicationController_To_v1_ReplicationController(a.(*core.ReplicationController), b.(*corev1.ReplicationController), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ReplicationControllerCondition)(nil), (*core.ReplicationControllerCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationControllerCondition_To_core_ReplicationControllerCondition(a.(*corev1.ReplicationControllerCondition), b.(*core.ReplicationControllerCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ReplicationControllerCondition)(nil), (*corev1.ReplicationControllerCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ReplicationControllerCondition_To_v1_ReplicationControllerCondition(a.(*core.ReplicationControllerCondition), b.(*corev1.ReplicationControllerCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ReplicationControllerList)(nil), (*core.ReplicationControllerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationControllerList_To_core_ReplicationControllerList(a.(*corev1.ReplicationControllerList), b.(*core.ReplicationControllerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ReplicationControllerList)(nil), (*corev1.ReplicationControllerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ReplicationControllerList_To_v1_ReplicationControllerList(a.(*core.ReplicationControllerList), b.(*corev1.ReplicationControllerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ReplicationControllerStatus)(nil), (*core.ReplicationControllerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationControllerStatus_To_core_ReplicationControllerStatus(a.(*corev1.ReplicationControllerStatus), b.(*core.ReplicationControllerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ReplicationControllerStatus)(nil), (*corev1.ReplicationControllerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ReplicationControllerStatus_To_v1_ReplicationControllerStatus(a.(*core.ReplicationControllerStatus), b.(*corev1.ReplicationControllerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceClaim)(nil), (*core.ResourceClaim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceClaim_To_core_ResourceClaim(a.(*corev1.ResourceClaim), b.(*core.ResourceClaim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceClaim)(nil), (*corev1.ResourceClaim)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceClaim_To_v1_ResourceClaim(a.(*core.ResourceClaim), b.(*corev1.ResourceClaim), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceFieldSelector)(nil), (*core.ResourceFieldSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceFieldSelector_To_core_ResourceFieldSelector(a.(*corev1.ResourceFieldSelector), b.(*core.ResourceFieldSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceFieldSelector)(nil), (*corev1.ResourceFieldSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceFieldSelector_To_v1_ResourceFieldSelector(a.(*core.ResourceFieldSelector), b.(*corev1.ResourceFieldSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceHealth)(nil), (*core.ResourceHealth)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceHealth_To_core_ResourceHealth(a.(*corev1.ResourceHealth), b.(*core.ResourceHealth), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceHealth)(nil), (*corev1.ResourceHealth)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceHealth_To_v1_ResourceHealth(a.(*core.ResourceHealth), b.(*corev1.ResourceHealth), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceQuota)(nil), (*core.ResourceQuota)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceQuota_To_core_ResourceQuota(a.(*corev1.ResourceQuota), b.(*core.ResourceQuota), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceQuota)(nil), (*corev1.ResourceQuota)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceQuota_To_v1_ResourceQuota(a.(*core.ResourceQuota), b.(*corev1.ResourceQuota), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceQuotaList)(nil), (*core.ResourceQuotaList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceQuotaList_To_core_ResourceQuotaList(a.(*corev1.ResourceQuotaList), b.(*core.ResourceQuotaList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceQuotaList)(nil), (*corev1.ResourceQuotaList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceQuotaList_To_v1_ResourceQuotaList(a.(*core.ResourceQuotaList), b.(*corev1.ResourceQuotaList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceQuotaSpec)(nil), (*core.ResourceQuotaSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceQuotaSpec_To_core_ResourceQuotaSpec(a.(*corev1.ResourceQuotaSpec), b.(*core.ResourceQuotaSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceQuotaSpec)(nil), (*corev1.ResourceQuotaSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceQuotaSpec_To_v1_ResourceQuotaSpec(a.(*core.ResourceQuotaSpec), b.(*corev1.ResourceQuotaSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceQuotaStatus)(nil), (*core.ResourceQuotaStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceQuotaStatus_To_core_ResourceQuotaStatus(a.(*corev1.ResourceQuotaStatus), b.(*core.ResourceQuotaStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceQuotaStatus)(nil), (*corev1.ResourceQuotaStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceQuotaStatus_To_v1_ResourceQuotaStatus(a.(*core.ResourceQuotaStatus), b.(*corev1.ResourceQuotaStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceRequirements)(nil), (*core.ResourceRequirements)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceRequirements_To_core_ResourceRequirements(a.(*corev1.ResourceRequirements), b.(*core.ResourceRequirements), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceRequirements)(nil), (*corev1.ResourceRequirements)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceRequirements_To_v1_ResourceRequirements(a.(*core.ResourceRequirements), b.(*corev1.ResourceRequirements), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ResourceStatus)(nil), (*core.ResourceStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceStatus_To_core_ResourceStatus(a.(*corev1.ResourceStatus), b.(*core.ResourceStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ResourceStatus)(nil), (*corev1.ResourceStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ResourceStatus_To_v1_ResourceStatus(a.(*core.ResourceStatus), b.(*corev1.ResourceStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SELinuxOptions)(nil), (*core.SELinuxOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SELinuxOptions_To_core_SELinuxOptions(a.(*corev1.SELinuxOptions), b.(*core.SELinuxOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SELinuxOptions)(nil), (*corev1.SELinuxOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SELinuxOptions_To_v1_SELinuxOptions(a.(*core.SELinuxOptions), b.(*corev1.SELinuxOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ScaleIOPersistentVolumeSource)(nil), (*core.ScaleIOPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ScaleIOPersistentVolumeSource_To_core_ScaleIOPersistentVolumeSource(a.(*corev1.ScaleIOPersistentVolumeSource), b.(*core.ScaleIOPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ScaleIOPersistentVolumeSource)(nil), (*corev1.ScaleIOPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ScaleIOPersistentVolumeSource_To_v1_ScaleIOPersistentVolumeSource(a.(*core.ScaleIOPersistentVolumeSource), b.(*corev1.ScaleIOPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ScaleIOVolumeSource)(nil), (*core.ScaleIOVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ScaleIOVolumeSource_To_core_ScaleIOVolumeSource(a.(*corev1.ScaleIOVolumeSource), b.(*core.ScaleIOVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ScaleIOVolumeSource)(nil), (*corev1.ScaleIOVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ScaleIOVolumeSource_To_v1_ScaleIOVolumeSource(a.(*core.ScaleIOVolumeSource), b.(*corev1.ScaleIOVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ScopeSelector)(nil), (*core.ScopeSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ScopeSelector_To_core_ScopeSelector(a.(*corev1.ScopeSelector), b.(*core.ScopeSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ScopeSelector)(nil), (*corev1.ScopeSelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ScopeSelector_To_v1_ScopeSelector(a.(*core.ScopeSelector), b.(*corev1.ScopeSelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ScopedResourceSelectorRequirement)(nil), (*core.ScopedResourceSelectorRequirement)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ScopedResourceSelectorRequirement_To_core_ScopedResourceSelectorRequirement(a.(*corev1.ScopedResourceSelectorRequirement), b.(*core.ScopedResourceSelectorRequirement), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ScopedResourceSelectorRequirement)(nil), (*corev1.ScopedResourceSelectorRequirement)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ScopedResourceSelectorRequirement_To_v1_ScopedResourceSelectorRequirement(a.(*core.ScopedResourceSelectorRequirement), b.(*corev1.ScopedResourceSelectorRequirement), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SeccompProfile)(nil), (*core.SeccompProfile)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SeccompProfile_To_core_SeccompProfile(a.(*corev1.SeccompProfile), b.(*core.SeccompProfile), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SeccompProfile)(nil), (*corev1.SeccompProfile)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SeccompProfile_To_v1_SeccompProfile(a.(*core.SeccompProfile), b.(*corev1.SeccompProfile), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Secret)(nil), (*corev1.Secret)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Secret_To_v1_Secret(a.(*core.Secret), b.(*corev1.Secret), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SecretEnvSource)(nil), (*core.SecretEnvSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SecretEnvSource_To_core_SecretEnvSource(a.(*corev1.SecretEnvSource), b.(*core.SecretEnvSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SecretEnvSource)(nil), (*corev1.SecretEnvSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SecretEnvSource_To_v1_SecretEnvSource(a.(*core.SecretEnvSource), b.(*corev1.SecretEnvSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SecretKeySelector)(nil), (*core.SecretKeySelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SecretKeySelector_To_core_SecretKeySelector(a.(*corev1.SecretKeySelector), b.(*core.SecretKeySelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SecretKeySelector)(nil), (*corev1.SecretKeySelector)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SecretKeySelector_To_v1_SecretKeySelector(a.(*core.SecretKeySelector), b.(*corev1.SecretKeySelector), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SecretList)(nil), (*core.SecretList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SecretList_To_core_SecretList(a.(*corev1.SecretList), b.(*core.SecretList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SecretList)(nil), (*corev1.SecretList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SecretList_To_v1_SecretList(a.(*core.SecretList), b.(*corev1.SecretList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SecretProjection)(nil), (*core.SecretProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SecretProjection_To_core_SecretProjection(a.(*corev1.SecretProjection), b.(*core.SecretProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SecretProjection)(nil), (*corev1.SecretProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SecretProjection_To_v1_SecretProjection(a.(*core.SecretProjection), b.(*corev1.SecretProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SecretReference)(nil), (*core.SecretReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SecretReference_To_core_SecretReference(a.(*corev1.SecretReference), b.(*core.SecretReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SecretReference)(nil), (*corev1.SecretReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SecretReference_To_v1_SecretReference(a.(*core.SecretReference), b.(*corev1.SecretReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SecretVolumeSource)(nil), (*core.SecretVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SecretVolumeSource_To_core_SecretVolumeSource(a.(*corev1.SecretVolumeSource), b.(*core.SecretVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SecretVolumeSource)(nil), (*corev1.SecretVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SecretVolumeSource_To_v1_SecretVolumeSource(a.(*core.SecretVolumeSource), b.(*corev1.SecretVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SecurityContext)(nil), (*core.SecurityContext)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SecurityContext_To_core_SecurityContext(a.(*corev1.SecurityContext), b.(*core.SecurityContext), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SecurityContext)(nil), (*corev1.SecurityContext)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SecurityContext_To_v1_SecurityContext(a.(*core.SecurityContext), b.(*corev1.SecurityContext), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SerializedReference)(nil), (*core.SerializedReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SerializedReference_To_core_SerializedReference(a.(*corev1.SerializedReference), b.(*core.SerializedReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SerializedReference)(nil), (*corev1.SerializedReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SerializedReference_To_v1_SerializedReference(a.(*core.SerializedReference), b.(*corev1.SerializedReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Service)(nil), (*core.Service)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Service_To_core_Service(a.(*corev1.Service), b.(*core.Service), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Service)(nil), (*corev1.Service)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Service_To_v1_Service(a.(*core.Service), b.(*corev1.Service), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServiceAccount)(nil), (*core.ServiceAccount)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServiceAccount_To_core_ServiceAccount(a.(*corev1.ServiceAccount), b.(*core.ServiceAccount), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServiceAccount)(nil), (*corev1.ServiceAccount)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServiceAccount_To_v1_ServiceAccount(a.(*core.ServiceAccount), b.(*corev1.ServiceAccount), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServiceAccountList)(nil), (*core.ServiceAccountList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServiceAccountList_To_core_ServiceAccountList(a.(*corev1.ServiceAccountList), b.(*core.ServiceAccountList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServiceAccountList)(nil), (*corev1.ServiceAccountList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServiceAccountList_To_v1_ServiceAccountList(a.(*core.ServiceAccountList), b.(*corev1.ServiceAccountList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServiceAccountTokenProjection)(nil), (*core.ServiceAccountTokenProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServiceAccountTokenProjection_To_core_ServiceAccountTokenProjection(a.(*corev1.ServiceAccountTokenProjection), b.(*core.ServiceAccountTokenProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServiceAccountTokenProjection)(nil), (*corev1.ServiceAccountTokenProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServiceAccountTokenProjection_To_v1_ServiceAccountTokenProjection(a.(*core.ServiceAccountTokenProjection), b.(*corev1.ServiceAccountTokenProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServiceList)(nil), (*core.ServiceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServiceList_To_core_ServiceList(a.(*corev1.ServiceList), b.(*core.ServiceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServiceList)(nil), (*corev1.ServiceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServiceList_To_v1_ServiceList(a.(*core.ServiceList), b.(*corev1.ServiceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServicePort)(nil), (*core.ServicePort)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServicePort_To_core_ServicePort(a.(*corev1.ServicePort), b.(*core.ServicePort), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServicePort)(nil), (*corev1.ServicePort)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServicePort_To_v1_ServicePort(a.(*core.ServicePort), b.(*corev1.ServicePort), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServiceProxyOptions)(nil), (*core.ServiceProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServiceProxyOptions_To_core_ServiceProxyOptions(a.(*corev1.ServiceProxyOptions), b.(*core.ServiceProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServiceProxyOptions)(nil), (*corev1.ServiceProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServiceProxyOptions_To_v1_ServiceProxyOptions(a.(*core.ServiceProxyOptions), b.(*corev1.ServiceProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServiceSpec)(nil), (*core.ServiceSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServiceSpec_To_core_ServiceSpec(a.(*corev1.ServiceSpec), b.(*core.ServiceSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServiceSpec)(nil), (*corev1.ServiceSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServiceSpec_To_v1_ServiceSpec(a.(*core.ServiceSpec), b.(*corev1.ServiceSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.ServiceStatus)(nil), (*core.ServiceStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ServiceStatus_To_core_ServiceStatus(a.(*corev1.ServiceStatus), b.(*core.ServiceStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.ServiceStatus)(nil), (*corev1.ServiceStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ServiceStatus_To_v1_ServiceStatus(a.(*core.ServiceStatus), b.(*corev1.ServiceStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SessionAffinityConfig)(nil), (*core.SessionAffinityConfig)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SessionAffinityConfig_To_core_SessionAffinityConfig(a.(*corev1.SessionAffinityConfig), b.(*core.SessionAffinityConfig), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SessionAffinityConfig)(nil), (*corev1.SessionAffinityConfig)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SessionAffinityConfig_To_v1_SessionAffinityConfig(a.(*core.SessionAffinityConfig), b.(*corev1.SessionAffinityConfig), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.SleepAction)(nil), (*core.SleepAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SleepAction_To_core_SleepAction(a.(*corev1.SleepAction), b.(*core.SleepAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.SleepAction)(nil), (*corev1.SleepAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_SleepAction_To_v1_SleepAction(a.(*core.SleepAction), b.(*corev1.SleepAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.StorageOSPersistentVolumeSource)(nil), (*core.StorageOSPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageOSPersistentVolumeSource_To_core_StorageOSPersistentVolumeSource(a.(*corev1.StorageOSPersistentVolumeSource), b.(*core.StorageOSPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.StorageOSPersistentVolumeSource)(nil), (*corev1.StorageOSPersistentVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_StorageOSPersistentVolumeSource_To_v1_StorageOSPersistentVolumeSource(a.(*core.StorageOSPersistentVolumeSource), b.(*corev1.StorageOSPersistentVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.StorageOSVolumeSource)(nil), (*core.StorageOSVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageOSVolumeSource_To_core_StorageOSVolumeSource(a.(*corev1.StorageOSVolumeSource), b.(*core.StorageOSVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.StorageOSVolumeSource)(nil), (*corev1.StorageOSVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_StorageOSVolumeSource_To_v1_StorageOSVolumeSource(a.(*core.StorageOSVolumeSource), b.(*corev1.StorageOSVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Sysctl)(nil), (*core.Sysctl)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Sysctl_To_core_Sysctl(a.(*corev1.Sysctl), b.(*core.Sysctl), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Sysctl)(nil), (*corev1.Sysctl)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Sysctl_To_v1_Sysctl(a.(*core.Sysctl), b.(*corev1.Sysctl), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.TCPSocketAction)(nil), (*core.TCPSocketAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TCPSocketAction_To_core_TCPSocketAction(a.(*corev1.TCPSocketAction), b.(*core.TCPSocketAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.TCPSocketAction)(nil), (*corev1.TCPSocketAction)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_TCPSocketAction_To_v1_TCPSocketAction(a.(*core.TCPSocketAction), b.(*corev1.TCPSocketAction), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Taint)(nil), (*core.Taint)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Taint_To_core_Taint(a.(*corev1.Taint), b.(*core.Taint), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Taint)(nil), (*corev1.Taint)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Taint_To_v1_Taint(a.(*core.Taint), b.(*corev1.Taint), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Toleration)(nil), (*core.Toleration)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Toleration_To_core_Toleration(a.(*corev1.Toleration), b.(*core.Toleration), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Toleration)(nil), (*corev1.Toleration)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Toleration_To_v1_Toleration(a.(*core.Toleration), b.(*corev1.Toleration), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.TopologySelectorLabelRequirement)(nil), (*core.TopologySelectorLabelRequirement)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TopologySelectorLabelRequirement_To_core_TopologySelectorLabelRequirement(a.(*corev1.TopologySelectorLabelRequirement), b.(*core.TopologySelectorLabelRequirement), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.TopologySelectorLabelRequirement)(nil), (*corev1.TopologySelectorLabelRequirement)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_TopologySelectorLabelRequirement_To_v1_TopologySelectorLabelRequirement(a.(*core.TopologySelectorLabelRequirement), b.(*corev1.TopologySelectorLabelRequirement), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.TopologySelectorTerm)(nil), (*core.TopologySelectorTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TopologySelectorTerm_To_core_TopologySelectorTerm(a.(*corev1.TopologySelectorTerm), b.(*core.TopologySelectorTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.TopologySelectorTerm)(nil), (*corev1.TopologySelectorTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_TopologySelectorTerm_To_v1_TopologySelectorTerm(a.(*core.TopologySelectorTerm), b.(*corev1.TopologySelectorTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.TopologySpreadConstraint)(nil), (*core.TopologySpreadConstraint)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TopologySpreadConstraint_To_core_TopologySpreadConstraint(a.(*corev1.TopologySpreadConstraint), b.(*core.TopologySpreadConstraint), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.TopologySpreadConstraint)(nil), (*corev1.TopologySpreadConstraint)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_TopologySpreadConstraint_To_v1_TopologySpreadConstraint(a.(*core.TopologySpreadConstraint), b.(*corev1.TopologySpreadConstraint), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.TypedLocalObjectReference)(nil), (*core.TypedLocalObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TypedLocalObjectReference_To_core_TypedLocalObjectReference(a.(*corev1.TypedLocalObjectReference), b.(*core.TypedLocalObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.TypedLocalObjectReference)(nil), (*corev1.TypedLocalObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_TypedLocalObjectReference_To_v1_TypedLocalObjectReference(a.(*core.TypedLocalObjectReference), b.(*corev1.TypedLocalObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.TypedObjectReference)(nil), (*core.TypedObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TypedObjectReference_To_core_TypedObjectReference(a.(*corev1.TypedObjectReference), b.(*core.TypedObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.TypedObjectReference)(nil), (*corev1.TypedObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_TypedObjectReference_To_v1_TypedObjectReference(a.(*core.TypedObjectReference), b.(*corev1.TypedObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.Volume)(nil), (*core.Volume)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Volume_To_core_Volume(a.(*corev1.Volume), b.(*core.Volume), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.Volume)(nil), (*corev1.Volume)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Volume_To_v1_Volume(a.(*core.Volume), b.(*corev1.Volume), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VolumeDevice)(nil), (*core.VolumeDevice)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeDevice_To_core_VolumeDevice(a.(*corev1.VolumeDevice), b.(*core.VolumeDevice), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VolumeDevice)(nil), (*corev1.VolumeDevice)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VolumeDevice_To_v1_VolumeDevice(a.(*core.VolumeDevice), b.(*corev1.VolumeDevice), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VolumeMount)(nil), (*core.VolumeMount)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeMount_To_core_VolumeMount(a.(*corev1.VolumeMount), b.(*core.VolumeMount), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VolumeMount)(nil), (*corev1.VolumeMount)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VolumeMount_To_v1_VolumeMount(a.(*core.VolumeMount), b.(*corev1.VolumeMount), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VolumeMountStatus)(nil), (*core.VolumeMountStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeMountStatus_To_core_VolumeMountStatus(a.(*corev1.VolumeMountStatus), b.(*core.VolumeMountStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VolumeMountStatus)(nil), (*corev1.VolumeMountStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VolumeMountStatus_To_v1_VolumeMountStatus(a.(*core.VolumeMountStatus), b.(*corev1.VolumeMountStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VolumeNodeAffinity)(nil), (*core.VolumeNodeAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeNodeAffinity_To_core_VolumeNodeAffinity(a.(*corev1.VolumeNodeAffinity), b.(*core.VolumeNodeAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VolumeNodeAffinity)(nil), (*corev1.VolumeNodeAffinity)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VolumeNodeAffinity_To_v1_VolumeNodeAffinity(a.(*core.VolumeNodeAffinity), b.(*corev1.VolumeNodeAffinity), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VolumeProjection)(nil), (*core.VolumeProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeProjection_To_core_VolumeProjection(a.(*corev1.VolumeProjection), b.(*core.VolumeProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VolumeProjection)(nil), (*corev1.VolumeProjection)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VolumeProjection_To_v1_VolumeProjection(a.(*core.VolumeProjection), b.(*corev1.VolumeProjection), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VolumeResourceRequirements)(nil), (*core.VolumeResourceRequirements)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeResourceRequirements_To_core_VolumeResourceRequirements(a.(*corev1.VolumeResourceRequirements), b.(*core.VolumeResourceRequirements), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VolumeResourceRequirements)(nil), (*corev1.VolumeResourceRequirements)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VolumeResourceRequirements_To_v1_VolumeResourceRequirements(a.(*core.VolumeResourceRequirements), b.(*corev1.VolumeResourceRequirements), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VolumeSource)(nil), (*core.VolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VolumeSource_To_core_VolumeSource(a.(*corev1.VolumeSource), b.(*core.VolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VolumeSource)(nil), (*corev1.VolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VolumeSource_To_v1_VolumeSource(a.(*core.VolumeSource), b.(*corev1.VolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.VsphereVirtualDiskVolumeSource)(nil), (*core.VsphereVirtualDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_VsphereVirtualDiskVolumeSource_To_core_VsphereVirtualDiskVolumeSource(a.(*corev1.VsphereVirtualDiskVolumeSource), b.(*core.VsphereVirtualDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.VsphereVirtualDiskVolumeSource)(nil), (*corev1.VsphereVirtualDiskVolumeSource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_VsphereVirtualDiskVolumeSource_To_v1_VsphereVirtualDiskVolumeSource(a.(*core.VsphereVirtualDiskVolumeSource), b.(*corev1.VsphereVirtualDiskVolumeSource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.WeightedPodAffinityTerm)(nil), (*core.WeightedPodAffinityTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_WeightedPodAffinityTerm_To_core_WeightedPodAffinityTerm(a.(*corev1.WeightedPodAffinityTerm), b.(*core.WeightedPodAffinityTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.WeightedPodAffinityTerm)(nil), (*corev1.WeightedPodAffinityTerm)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_WeightedPodAffinityTerm_To_v1_WeightedPodAffinityTerm(a.(*core.WeightedPodAffinityTerm), b.(*corev1.WeightedPodAffinityTerm), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*corev1.WindowsSecurityContextOptions)(nil), (*core.WindowsSecurityContextOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_WindowsSecurityContextOptions_To_core_WindowsSecurityContextOptions(a.(*corev1.WindowsSecurityContextOptions), b.(*core.WindowsSecurityContextOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*core.WindowsSecurityContextOptions)(nil), (*corev1.WindowsSecurityContextOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_WindowsSecurityContextOptions_To_v1_WindowsSecurityContextOptions(a.(*core.WindowsSecurityContextOptions), b.(*corev1.WindowsSecurityContextOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*corev1.NodeProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_NodeProxyOptions(a.(*url.Values), b.(*corev1.NodeProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*corev1.PodAttachOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_PodAttachOptions(a.(*url.Values), b.(*corev1.PodAttachOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*corev1.PodExecOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_PodExecOptions(a.(*url.Values), b.(*corev1.PodExecOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*corev1.PodLogOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_PodLogOptions(a.(*url.Values), b.(*corev1.PodLogOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*corev1.PodPortForwardOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_PodPortForwardOptions(a.(*url.Values), b.(*corev1.PodPortForwardOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*corev1.PodProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_PodProxyOptions(a.(*url.Values), b.(*corev1.PodProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*corev1.ServiceProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_ServiceProxyOptions(a.(*url.Values), b.(*corev1.ServiceProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*[]string)(nil), (**string)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_Slice_string_To_Pointer_string(a.(*[]string), b.(**string), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*apps.ReplicaSetSpec)(nil), (*corev1.ReplicationControllerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_apps_ReplicaSetSpec_To_v1_ReplicationControllerSpec(a.(*apps.ReplicaSetSpec), b.(*corev1.ReplicationControllerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*apps.ReplicaSetStatus)(nil), (*corev1.ReplicationControllerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_apps_ReplicaSetStatus_To_v1_ReplicationControllerStatus(a.(*apps.ReplicaSetStatus), b.(*corev1.ReplicationControllerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*apps.ReplicaSet)(nil), (*corev1.ReplicationController)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_apps_ReplicaSet_To_v1_ReplicationController(a.(*apps.ReplicaSet), b.(*corev1.ReplicationController), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*core.NodeSpec)(nil), (*corev1.NodeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_NodeSpec_To_v1_NodeSpec(a.(*core.NodeSpec), b.(*corev1.NodeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*core.PersistentVolumeSpec)(nil), (*corev1.PersistentVolumeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PersistentVolumeSpec_To_v1_PersistentVolumeSpec(a.(*core.PersistentVolumeSpec), b.(*corev1.PersistentVolumeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*core.PodSpec)(nil), (*corev1.PodSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodSpec_To_v1_PodSpec(a.(*core.PodSpec), b.(*corev1.PodSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*core.PodStatus)(nil), (*corev1.PodStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodStatus_To_v1_PodStatus(a.(*core.PodStatus), b.(*corev1.PodStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*core.PodTemplateSpec)(nil), (*corev1.PodTemplateSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_PodTemplateSpec_To_v1_PodTemplateSpec(a.(*core.PodTemplateSpec), b.(*corev1.PodTemplateSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*core.Pod)(nil), (*corev1.Pod)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_Pod_To_v1_Pod(a.(*core.Pod), b.(*corev1.Pod), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*core.ReplicationControllerSpec)(nil), (*corev1.ReplicationControllerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_core_ReplicationControllerSpec_To_v1_ReplicationControllerSpec(a.(*core.ReplicationControllerSpec), b.(*corev1.ReplicationControllerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.NodeSpec)(nil), (*core.NodeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_NodeSpec_To_core_NodeSpec(a.(*corev1.NodeSpec), b.(*core.NodeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.PersistentVolumeSpec)(nil), (*core.PersistentVolumeSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentVolumeSpec_To_core_PersistentVolumeSpec(a.(*corev1.PersistentVolumeSpec), b.(*core.PersistentVolumeSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.PodSpec)(nil), (*core.PodSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodSpec_To_core_PodSpec(a.(*corev1.PodSpec), b.(*core.PodSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.PodStatus)(nil), (*core.PodStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodStatus_To_core_PodStatus(a.(*corev1.PodStatus), b.(*core.PodStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.PodTemplateSpec)(nil), (*core.PodTemplateSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PodTemplateSpec_To_core_PodTemplateSpec(a.(*corev1.PodTemplateSpec), b.(*core.PodTemplateSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.Pod)(nil), (*core.Pod)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Pod_To_core_Pod(a.(*corev1.Pod), b.(*core.Pod), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.ReplicationControllerSpec)(nil), (*apps.ReplicaSetSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationControllerSpec_To_apps_ReplicaSetSpec(a.(*corev1.ReplicationControllerSpec), b.(*apps.ReplicaSetSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.ReplicationControllerSpec)(nil), (*core.ReplicationControllerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationControllerSpec_To_core_ReplicationControllerSpec(a.(*corev1.ReplicationControllerSpec), b.(*core.ReplicationControllerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.ReplicationControllerStatus)(nil), (*apps.ReplicaSetStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationControllerStatus_To_apps_ReplicaSetStatus(a.(*corev1.ReplicationControllerStatus), b.(*apps.ReplicaSetStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.ReplicationController)(nil), (*apps.ReplicaSet)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ReplicationController_To_apps_ReplicaSet(a.(*corev1.ReplicationController), b.(*apps.ReplicaSet), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.ResourceList)(nil), (*core.ResourceList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceList_To_core_ResourceList(a.(*corev1.ResourceList), b.(*core.ResourceList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*corev1.Secret)(nil), (*core.Secret)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Secret_To_core_Secret(a.(*corev1.Secret), b.(*core.Secret), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1_AWSElasticBlockStoreVolumeSource_To_core_AWSElasticBlockStoreVolumeSource(in *corev1.AWSElasticBlockStoreVolumeSource, out *core.AWSElasticBlockStoreVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.Partition = in.Partition
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_AWSElasticBlockStoreVolumeSource_To_core_AWSElasticBlockStoreVolumeSource is an autogenerated conversion function.
func Convert_v1_AWSElasticBlockStoreVolumeSource_To_core_AWSElasticBlockStoreVolumeSource(in *corev1.AWSElasticBlockStoreVolumeSource, out *core.AWSElasticBlockStoreVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_AWSElasticBlockStoreVolumeSource_To_core_AWSElasticBlockStoreVolumeSource(in, out, s)
}

func autoConvert_core_AWSElasticBlockStoreVolumeSource_To_v1_AWSElasticBlockStoreVolumeSource(in *core.AWSElasticBlockStoreVolumeSource, out *corev1.AWSElasticBlockStoreVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.Partition = in.Partition
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_AWSElasticBlockStoreVolumeSource_To_v1_AWSElasticBlockStoreVolumeSource is an autogenerated conversion function.
func Convert_core_AWSElasticBlockStoreVolumeSource_To_v1_AWSElasticBlockStoreVolumeSource(in *core.AWSElasticBlockStoreVolumeSource, out *corev1.AWSElasticBlockStoreVolumeSource, s conversion.Scope) error {
	return autoConvert_core_AWSElasticBlockStoreVolumeSource_To_v1_AWSElasticBlockStoreVolumeSource(in, out, s)
}

func autoConvert_v1_Affinity_To_core_Affinity(in *corev1.Affinity, out *core.Affinity, s conversion.Scope) error {
	out.NodeAffinity = (*core.NodeAffinity)(unsafe.Pointer(in.NodeAffinity))
	out.PodAffinity = (*core.PodAffinity)(unsafe.Pointer(in.PodAffinity))
	out.PodAntiAffinity = (*core.PodAntiAffinity)(unsafe.Pointer(in.PodAntiAffinity))
	return nil
}

// Convert_v1_Affinity_To_core_Affinity is an autogenerated conversion function.
func Convert_v1_Affinity_To_core_Affinity(in *corev1.Affinity, out *core.Affinity, s conversion.Scope) error {
	return autoConvert_v1_Affinity_To_core_Affinity(in, out, s)
}

func autoConvert_core_Affinity_To_v1_Affinity(in *core.Affinity, out *corev1.Affinity, s conversion.Scope) error {
	out.NodeAffinity = (*corev1.NodeAffinity)(unsafe.Pointer(in.NodeAffinity))
	out.PodAffinity = (*corev1.PodAffinity)(unsafe.Pointer(in.PodAffinity))
	out.PodAntiAffinity = (*corev1.PodAntiAffinity)(unsafe.Pointer(in.PodAntiAffinity))
	return nil
}

// Convert_core_Affinity_To_v1_Affinity is an autogenerated conversion function.
func Convert_core_Affinity_To_v1_Affinity(in *core.Affinity, out *corev1.Affinity, s conversion.Scope) error {
	return autoConvert_core_Affinity_To_v1_Affinity(in, out, s)
}

func autoConvert_v1_AppArmorProfile_To_core_AppArmorProfile(in *corev1.AppArmorProfile, out *core.AppArmorProfile, s conversion.Scope) error {
	out.Type = core.AppArmorProfileType(in.Type)
	out.LocalhostProfile = (*string)(unsafe.Pointer(in.LocalhostProfile))
	return nil
}

// Convert_v1_AppArmorProfile_To_core_AppArmorProfile is an autogenerated conversion function.
func Convert_v1_AppArmorProfile_To_core_AppArmorProfile(in *corev1.AppArmorProfile, out *core.AppArmorProfile, s conversion.Scope) error {
	return autoConvert_v1_AppArmorProfile_To_core_AppArmorProfile(in, out, s)
}

func autoConvert_core_AppArmorProfile_To_v1_AppArmorProfile(in *core.AppArmorProfile, out *corev1.AppArmorProfile, s conversion.Scope) error {
	out.Type = corev1.AppArmorProfileType(in.Type)
	out.LocalhostProfile = (*string)(unsafe.Pointer(in.LocalhostProfile))
	return nil
}

// Convert_core_AppArmorProfile_To_v1_AppArmorProfile is an autogenerated conversion function.
func Convert_core_AppArmorProfile_To_v1_AppArmorProfile(in *core.AppArmorProfile, out *corev1.AppArmorProfile, s conversion.Scope) error {
	return autoConvert_core_AppArmorProfile_To_v1_AppArmorProfile(in, out, s)
}

func autoConvert_v1_AttachedVolume_To_core_AttachedVolume(in *corev1.AttachedVolume, out *core.AttachedVolume, s conversion.Scope) error {
	out.Name = core.UniqueVolumeName(in.Name)
	out.DevicePath = in.DevicePath
	return nil
}

// Convert_v1_AttachedVolume_To_core_AttachedVolume is an autogenerated conversion function.
func Convert_v1_AttachedVolume_To_core_AttachedVolume(in *corev1.AttachedVolume, out *core.AttachedVolume, s conversion.Scope) error {
	return autoConvert_v1_AttachedVolume_To_core_AttachedVolume(in, out, s)
}

func autoConvert_core_AttachedVolume_To_v1_AttachedVolume(in *core.AttachedVolume, out *corev1.AttachedVolume, s conversion.Scope) error {
	out.Name = corev1.UniqueVolumeName(in.Name)
	out.DevicePath = in.DevicePath
	return nil
}

// Convert_core_AttachedVolume_To_v1_AttachedVolume is an autogenerated conversion function.
func Convert_core_AttachedVolume_To_v1_AttachedVolume(in *core.AttachedVolume, out *corev1.AttachedVolume, s conversion.Scope) error {
	return autoConvert_core_AttachedVolume_To_v1_AttachedVolume(in, out, s)
}

func autoConvert_v1_AvoidPods_To_core_AvoidPods(in *corev1.AvoidPods, out *core.AvoidPods, s conversion.Scope) error {
	out.PreferAvoidPods = *(*[]core.PreferAvoidPodsEntry)(unsafe.Pointer(&in.PreferAvoidPods))
	return nil
}

// Convert_v1_AvoidPods_To_core_AvoidPods is an autogenerated conversion function.
func Convert_v1_AvoidPods_To_core_AvoidPods(in *corev1.AvoidPods, out *core.AvoidPods, s conversion.Scope) error {
	return autoConvert_v1_AvoidPods_To_core_AvoidPods(in, out, s)
}

func autoConvert_core_AvoidPods_To_v1_AvoidPods(in *core.AvoidPods, out *corev1.AvoidPods, s conversion.Scope) error {
	out.PreferAvoidPods = *(*[]corev1.PreferAvoidPodsEntry)(unsafe.Pointer(&in.PreferAvoidPods))
	return nil
}

// Convert_core_AvoidPods_To_v1_AvoidPods is an autogenerated conversion function.
func Convert_core_AvoidPods_To_v1_AvoidPods(in *core.AvoidPods, out *corev1.AvoidPods, s conversion.Scope) error {
	return autoConvert_core_AvoidPods_To_v1_AvoidPods(in, out, s)
}

func autoConvert_v1_AzureDiskVolumeSource_To_core_AzureDiskVolumeSource(in *corev1.AzureDiskVolumeSource, out *core.AzureDiskVolumeSource, s conversion.Scope) error {
	out.DiskName = in.DiskName
	out.DataDiskURI = in.DataDiskURI
	out.CachingMode = (*core.AzureDataDiskCachingMode)(unsafe.Pointer(in.CachingMode))
	out.FSType = (*string)(unsafe.Pointer(in.FSType))
	out.ReadOnly = (*bool)(unsafe.Pointer(in.ReadOnly))
	out.Kind = (*core.AzureDataDiskKind)(unsafe.Pointer(in.Kind))
	return nil
}

// Convert_v1_AzureDiskVolumeSource_To_core_AzureDiskVolumeSource is an autogenerated conversion function.
func Convert_v1_AzureDiskVolumeSource_To_core_AzureDiskVolumeSource(in *corev1.AzureDiskVolumeSource, out *core.AzureDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_AzureDiskVolumeSource_To_core_AzureDiskVolumeSource(in, out, s)
}

func autoConvert_core_AzureDiskVolumeSource_To_v1_AzureDiskVolumeSource(in *core.AzureDiskVolumeSource, out *corev1.AzureDiskVolumeSource, s conversion.Scope) error {
	out.DiskName = in.DiskName
	out.DataDiskURI = in.DataDiskURI
	out.CachingMode = (*corev1.AzureDataDiskCachingMode)(unsafe.Pointer(in.CachingMode))
	out.FSType = (*string)(unsafe.Pointer(in.FSType))
	out.ReadOnly = (*bool)(unsafe.Pointer(in.ReadOnly))
	out.Kind = (*corev1.AzureDataDiskKind)(unsafe.Pointer(in.Kind))
	return nil
}

// Convert_core_AzureDiskVolumeSource_To_v1_AzureDiskVolumeSource is an autogenerated conversion function.
func Convert_core_AzureDiskVolumeSource_To_v1_AzureDiskVolumeSource(in *core.AzureDiskVolumeSource, out *corev1.AzureDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_core_AzureDiskVolumeSource_To_v1_AzureDiskVolumeSource(in, out, s)
}

func autoConvert_v1_AzureFilePersistentVolumeSource_To_core_AzureFilePersistentVolumeSource(in *corev1.AzureFilePersistentVolumeSource, out *core.AzureFilePersistentVolumeSource, s conversion.Scope) error {
	out.SecretName = in.SecretName
	out.ShareName = in.ShareName
	out.ReadOnly = in.ReadOnly
	out.SecretNamespace = (*string)(unsafe.Pointer(in.SecretNamespace))
	return nil
}

// Convert_v1_AzureFilePersistentVolumeSource_To_core_AzureFilePersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_AzureFilePersistentVolumeSource_To_core_AzureFilePersistentVolumeSource(in *corev1.AzureFilePersistentVolumeSource, out *core.AzureFilePersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_AzureFilePersistentVolumeSource_To_core_AzureFilePersistentVolumeSource(in, out, s)
}

func autoConvert_core_AzureFilePersistentVolumeSource_To_v1_AzureFilePersistentVolumeSource(in *core.AzureFilePersistentVolumeSource, out *corev1.AzureFilePersistentVolumeSource, s conversion.Scope) error {
	out.SecretName = in.SecretName
	out.ShareName = in.ShareName
	out.ReadOnly = in.ReadOnly
	out.SecretNamespace = (*string)(unsafe.Pointer(in.SecretNamespace))
	return nil
}

// Convert_core_AzureFilePersistentVolumeSource_To_v1_AzureFilePersistentVolumeSource is an autogenerated conversion function.
func Convert_core_AzureFilePersistentVolumeSource_To_v1_AzureFilePersistentVolumeSource(in *core.AzureFilePersistentVolumeSource, out *corev1.AzureFilePersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_AzureFilePersistentVolumeSource_To_v1_AzureFilePersistentVolumeSource(in, out, s)
}

func autoConvert_v1_AzureFileVolumeSource_To_core_AzureFileVolumeSource(in *corev1.AzureFileVolumeSource, out *core.AzureFileVolumeSource, s conversion.Scope) error {
	out.SecretName = in.SecretName
	out.ShareName = in.ShareName
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_AzureFileVolumeSource_To_core_AzureFileVolumeSource is an autogenerated conversion function.
func Convert_v1_AzureFileVolumeSource_To_core_AzureFileVolumeSource(in *corev1.AzureFileVolumeSource, out *core.AzureFileVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_AzureFileVolumeSource_To_core_AzureFileVolumeSource(in, out, s)
}

func autoConvert_core_AzureFileVolumeSource_To_v1_AzureFileVolumeSource(in *core.AzureFileVolumeSource, out *corev1.AzureFileVolumeSource, s conversion.Scope) error {
	out.SecretName = in.SecretName
	out.ShareName = in.ShareName
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_AzureFileVolumeSource_To_v1_AzureFileVolumeSource is an autogenerated conversion function.
func Convert_core_AzureFileVolumeSource_To_v1_AzureFileVolumeSource(in *core.AzureFileVolumeSource, out *corev1.AzureFileVolumeSource, s conversion.Scope) error {
	return autoConvert_core_AzureFileVolumeSource_To_v1_AzureFileVolumeSource(in, out, s)
}

func autoConvert_v1_Binding_To_core_Binding(in *corev1.Binding, out *core.Binding, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ObjectReference_To_core_ObjectReference(&in.Target, &out.Target, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Binding_To_core_Binding is an autogenerated conversion function.
func Convert_v1_Binding_To_core_Binding(in *corev1.Binding, out *core.Binding, s conversion.Scope) error {
	return autoConvert_v1_Binding_To_core_Binding(in, out, s)
}

func autoConvert_core_Binding_To_v1_Binding(in *core.Binding, out *corev1.Binding, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_ObjectReference_To_v1_ObjectReference(&in.Target, &out.Target, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_Binding_To_v1_Binding is an autogenerated conversion function.
func Convert_core_Binding_To_v1_Binding(in *core.Binding, out *corev1.Binding, s conversion.Scope) error {
	return autoConvert_core_Binding_To_v1_Binding(in, out, s)
}

func autoConvert_v1_CSIPersistentVolumeSource_To_core_CSIPersistentVolumeSource(in *corev1.CSIPersistentVolumeSource, out *core.CSIPersistentVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.VolumeHandle = in.VolumeHandle
	out.ReadOnly = in.ReadOnly
	out.FSType = in.FSType
	out.VolumeAttributes = *(*map[string]string)(unsafe.Pointer(&in.VolumeAttributes))
	out.ControllerPublishSecretRef = (*core.SecretReference)(unsafe.Pointer(in.ControllerPublishSecretRef))
	out.NodeStageSecretRef = (*core.SecretReference)(unsafe.Pointer(in.NodeStageSecretRef))
	out.NodePublishSecretRef = (*core.SecretReference)(unsafe.Pointer(in.NodePublishSecretRef))
	out.ControllerExpandSecretRef = (*core.SecretReference)(unsafe.Pointer(in.ControllerExpandSecretRef))
	out.NodeExpandSecretRef = (*core.SecretReference)(unsafe.Pointer(in.NodeExpandSecretRef))
	return nil
}

// Convert_v1_CSIPersistentVolumeSource_To_core_CSIPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_CSIPersistentVolumeSource_To_core_CSIPersistentVolumeSource(in *corev1.CSIPersistentVolumeSource, out *core.CSIPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_CSIPersistentVolumeSource_To_core_CSIPersistentVolumeSource(in, out, s)
}

func autoConvert_core_CSIPersistentVolumeSource_To_v1_CSIPersistentVolumeSource(in *core.CSIPersistentVolumeSource, out *corev1.CSIPersistentVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.VolumeHandle = in.VolumeHandle
	out.ReadOnly = in.ReadOnly
	out.FSType = in.FSType
	out.VolumeAttributes = *(*map[string]string)(unsafe.Pointer(&in.VolumeAttributes))
	out.ControllerPublishSecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.ControllerPublishSecretRef))
	out.NodeStageSecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.NodeStageSecretRef))
	out.NodePublishSecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.NodePublishSecretRef))
	out.ControllerExpandSecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.ControllerExpandSecretRef))
	out.NodeExpandSecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.NodeExpandSecretRef))
	return nil
}

// Convert_core_CSIPersistentVolumeSource_To_v1_CSIPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_CSIPersistentVolumeSource_To_v1_CSIPersistentVolumeSource(in *core.CSIPersistentVolumeSource, out *corev1.CSIPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_CSIPersistentVolumeSource_To_v1_CSIPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_CSIVolumeSource_To_core_CSIVolumeSource(in *corev1.CSIVolumeSource, out *core.CSIVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.ReadOnly = (*bool)(unsafe.Pointer(in.ReadOnly))
	out.FSType = (*string)(unsafe.Pointer(in.FSType))
	out.VolumeAttributes = *(*map[string]string)(unsafe.Pointer(&in.VolumeAttributes))
	out.NodePublishSecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.NodePublishSecretRef))
	return nil
}

// Convert_v1_CSIVolumeSource_To_core_CSIVolumeSource is an autogenerated conversion function.
func Convert_v1_CSIVolumeSource_To_core_CSIVolumeSource(in *corev1.CSIVolumeSource, out *core.CSIVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_CSIVolumeSource_To_core_CSIVolumeSource(in, out, s)
}

func autoConvert_core_CSIVolumeSource_To_v1_CSIVolumeSource(in *core.CSIVolumeSource, out *corev1.CSIVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.ReadOnly = (*bool)(unsafe.Pointer(in.ReadOnly))
	out.FSType = (*string)(unsafe.Pointer(in.FSType))
	out.VolumeAttributes = *(*map[string]string)(unsafe.Pointer(&in.VolumeAttributes))
	out.NodePublishSecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.NodePublishSecretRef))
	return nil
}

// Convert_core_CSIVolumeSource_To_v1_CSIVolumeSource is an autogenerated conversion function.
func Convert_core_CSIVolumeSource_To_v1_CSIVolumeSource(in *core.CSIVolumeSource, out *corev1.CSIVolumeSource, s conversion.Scope) error {
	return autoConvert_core_CSIVolumeSource_To_v1_CSIVolumeSource(in, out, s)
}

func autoConvert_v1_Capabilities_To_core_Capabilities(in *corev1.Capabilities, out *core.Capabilities, s conversion.Scope) error {
	out.Add = *(*[]core.Capability)(unsafe.Pointer(&in.Add))
	out.Drop = *(*[]core.Capability)(unsafe.Pointer(&in.Drop))
	return nil
}

// Convert_v1_Capabilities_To_core_Capabilities is an autogenerated conversion function.
func Convert_v1_Capabilities_To_core_Capabilities(in *corev1.Capabilities, out *core.Capabilities, s conversion.Scope) error {
	return autoConvert_v1_Capabilities_To_core_Capabilities(in, out, s)
}

func autoConvert_core_Capabilities_To_v1_Capabilities(in *core.Capabilities, out *corev1.Capabilities, s conversion.Scope) error {
	out.Add = *(*[]corev1.Capability)(unsafe.Pointer(&in.Add))
	out.Drop = *(*[]corev1.Capability)(unsafe.Pointer(&in.Drop))
	return nil
}

// Convert_core_Capabilities_To_v1_Capabilities is an autogenerated conversion function.
func Convert_core_Capabilities_To_v1_Capabilities(in *core.Capabilities, out *corev1.Capabilities, s conversion.Scope) error {
	return autoConvert_core_Capabilities_To_v1_Capabilities(in, out, s)
}

func autoConvert_v1_CephFSPersistentVolumeSource_To_core_CephFSPersistentVolumeSource(in *corev1.CephFSPersistentVolumeSource, out *core.CephFSPersistentVolumeSource, s conversion.Scope) error {
	out.Monitors = *(*[]string)(unsafe.Pointer(&in.Monitors))
	out.Path = in.Path
	out.User = in.User
	out.SecretFile = in.SecretFile
	out.SecretRef = (*core.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_CephFSPersistentVolumeSource_To_core_CephFSPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_CephFSPersistentVolumeSource_To_core_CephFSPersistentVolumeSource(in *corev1.CephFSPersistentVolumeSource, out *core.CephFSPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_CephFSPersistentVolumeSource_To_core_CephFSPersistentVolumeSource(in, out, s)
}

func autoConvert_core_CephFSPersistentVolumeSource_To_v1_CephFSPersistentVolumeSource(in *core.CephFSPersistentVolumeSource, out *corev1.CephFSPersistentVolumeSource, s conversion.Scope) error {
	out.Monitors = *(*[]string)(unsafe.Pointer(&in.Monitors))
	out.Path = in.Path
	out.User = in.User
	out.SecretFile = in.SecretFile
	out.SecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_CephFSPersistentVolumeSource_To_v1_CephFSPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_CephFSPersistentVolumeSource_To_v1_CephFSPersistentVolumeSource(in *core.CephFSPersistentVolumeSource, out *corev1.CephFSPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_CephFSPersistentVolumeSource_To_v1_CephFSPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_CephFSVolumeSource_To_core_CephFSVolumeSource(in *corev1.CephFSVolumeSource, out *core.CephFSVolumeSource, s conversion.Scope) error {
	out.Monitors = *(*[]string)(unsafe.Pointer(&in.Monitors))
	out.Path = in.Path
	out.User = in.User
	out.SecretFile = in.SecretFile
	out.SecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_CephFSVolumeSource_To_core_CephFSVolumeSource is an autogenerated conversion function.
func Convert_v1_CephFSVolumeSource_To_core_CephFSVolumeSource(in *corev1.CephFSVolumeSource, out *core.CephFSVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_CephFSVolumeSource_To_core_CephFSVolumeSource(in, out, s)
}

func autoConvert_core_CephFSVolumeSource_To_v1_CephFSVolumeSource(in *core.CephFSVolumeSource, out *corev1.CephFSVolumeSource, s conversion.Scope) error {
	out.Monitors = *(*[]string)(unsafe.Pointer(&in.Monitors))
	out.Path = in.Path
	out.User = in.User
	out.SecretFile = in.SecretFile
	out.SecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_CephFSVolumeSource_To_v1_CephFSVolumeSource is an autogenerated conversion function.
func Convert_core_CephFSVolumeSource_To_v1_CephFSVolumeSource(in *core.CephFSVolumeSource, out *corev1.CephFSVolumeSource, s conversion.Scope) error {
	return autoConvert_core_CephFSVolumeSource_To_v1_CephFSVolumeSource(in, out, s)
}

func autoConvert_v1_CinderPersistentVolumeSource_To_core_CinderPersistentVolumeSource(in *corev1.CinderPersistentVolumeSource, out *core.CinderPersistentVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*core.SecretReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_v1_CinderPersistentVolumeSource_To_core_CinderPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_CinderPersistentVolumeSource_To_core_CinderPersistentVolumeSource(in *corev1.CinderPersistentVolumeSource, out *core.CinderPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_CinderPersistentVolumeSource_To_core_CinderPersistentVolumeSource(in, out, s)
}

func autoConvert_core_CinderPersistentVolumeSource_To_v1_CinderPersistentVolumeSource(in *core.CinderPersistentVolumeSource, out *corev1.CinderPersistentVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_core_CinderPersistentVolumeSource_To_v1_CinderPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_CinderPersistentVolumeSource_To_v1_CinderPersistentVolumeSource(in *core.CinderPersistentVolumeSource, out *corev1.CinderPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_CinderPersistentVolumeSource_To_v1_CinderPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_CinderVolumeSource_To_core_CinderVolumeSource(in *corev1.CinderVolumeSource, out *core.CinderVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_v1_CinderVolumeSource_To_core_CinderVolumeSource is an autogenerated conversion function.
func Convert_v1_CinderVolumeSource_To_core_CinderVolumeSource(in *corev1.CinderVolumeSource, out *core.CinderVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_CinderVolumeSource_To_core_CinderVolumeSource(in, out, s)
}

func autoConvert_core_CinderVolumeSource_To_v1_CinderVolumeSource(in *core.CinderVolumeSource, out *corev1.CinderVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_core_CinderVolumeSource_To_v1_CinderVolumeSource is an autogenerated conversion function.
func Convert_core_CinderVolumeSource_To_v1_CinderVolumeSource(in *core.CinderVolumeSource, out *corev1.CinderVolumeSource, s conversion.Scope) error {
	return autoConvert_core_CinderVolumeSource_To_v1_CinderVolumeSource(in, out, s)
}

func autoConvert_v1_ClientIPConfig_To_core_ClientIPConfig(in *corev1.ClientIPConfig, out *core.ClientIPConfig, s conversion.Scope) error {
	out.TimeoutSeconds = (*int32)(unsafe.Pointer(in.TimeoutSeconds))
	return nil
}

// Convert_v1_ClientIPConfig_To_core_ClientIPConfig is an autogenerated conversion function.
func Convert_v1_ClientIPConfig_To_core_ClientIPConfig(in *corev1.ClientIPConfig, out *core.ClientIPConfig, s conversion.Scope) error {
	return autoConvert_v1_ClientIPConfig_To_core_ClientIPConfig(in, out, s)
}

func autoConvert_core_ClientIPConfig_To_v1_ClientIPConfig(in *core.ClientIPConfig, out *corev1.ClientIPConfig, s conversion.Scope) error {
	out.TimeoutSeconds = (*int32)(unsafe.Pointer(in.TimeoutSeconds))
	return nil
}

// Convert_core_ClientIPConfig_To_v1_ClientIPConfig is an autogenerated conversion function.
func Convert_core_ClientIPConfig_To_v1_ClientIPConfig(in *core.ClientIPConfig, out *corev1.ClientIPConfig, s conversion.Scope) error {
	return autoConvert_core_ClientIPConfig_To_v1_ClientIPConfig(in, out, s)
}

func autoConvert_v1_ClusterTrustBundleProjection_To_core_ClusterTrustBundleProjection(in *corev1.ClusterTrustBundleProjection, out *core.ClusterTrustBundleProjection, s conversion.Scope) error {
	out.Name = (*string)(unsafe.Pointer(in.Name))
	out.SignerName = (*string)(unsafe.Pointer(in.SignerName))
	out.LabelSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.LabelSelector))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	out.Path = in.Path
	return nil
}

// Convert_v1_ClusterTrustBundleProjection_To_core_ClusterTrustBundleProjection is an autogenerated conversion function.
func Convert_v1_ClusterTrustBundleProjection_To_core_ClusterTrustBundleProjection(in *corev1.ClusterTrustBundleProjection, out *core.ClusterTrustBundleProjection, s conversion.Scope) error {
	return autoConvert_v1_ClusterTrustBundleProjection_To_core_ClusterTrustBundleProjection(in, out, s)
}

func autoConvert_core_ClusterTrustBundleProjection_To_v1_ClusterTrustBundleProjection(in *core.ClusterTrustBundleProjection, out *corev1.ClusterTrustBundleProjection, s conversion.Scope) error {
	out.Name = (*string)(unsafe.Pointer(in.Name))
	out.SignerName = (*string)(unsafe.Pointer(in.SignerName))
	out.LabelSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.LabelSelector))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	out.Path = in.Path
	return nil
}

// Convert_core_ClusterTrustBundleProjection_To_v1_ClusterTrustBundleProjection is an autogenerated conversion function.
func Convert_core_ClusterTrustBundleProjection_To_v1_ClusterTrustBundleProjection(in *core.ClusterTrustBundleProjection, out *corev1.ClusterTrustBundleProjection, s conversion.Scope) error {
	return autoConvert_core_ClusterTrustBundleProjection_To_v1_ClusterTrustBundleProjection(in, out, s)
}

func autoConvert_v1_ComponentCondition_To_core_ComponentCondition(in *corev1.ComponentCondition, out *core.ComponentCondition, s conversion.Scope) error {
	out.Type = core.ComponentConditionType(in.Type)
	out.Status = core.ConditionStatus(in.Status)
	out.Message = in.Message
	out.Error = in.Error
	return nil
}

// Convert_v1_ComponentCondition_To_core_ComponentCondition is an autogenerated conversion function.
func Convert_v1_ComponentCondition_To_core_ComponentCondition(in *corev1.ComponentCondition, out *core.ComponentCondition, s conversion.Scope) error {
	return autoConvert_v1_ComponentCondition_To_core_ComponentCondition(in, out, s)
}

func autoConvert_core_ComponentCondition_To_v1_ComponentCondition(in *core.ComponentCondition, out *corev1.ComponentCondition, s conversion.Scope) error {
	out.Type = corev1.ComponentConditionType(in.Type)
	out.Status = corev1.ConditionStatus(in.Status)
	out.Message = in.Message
	out.Error = in.Error
	return nil
}

// Convert_core_ComponentCondition_To_v1_ComponentCondition is an autogenerated conversion function.
func Convert_core_ComponentCondition_To_v1_ComponentCondition(in *core.ComponentCondition, out *corev1.ComponentCondition, s conversion.Scope) error {
	return autoConvert_core_ComponentCondition_To_v1_ComponentCondition(in, out, s)
}

func autoConvert_v1_ComponentStatus_To_core_ComponentStatus(in *corev1.ComponentStatus, out *core.ComponentStatus, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Conditions = *(*[]core.ComponentCondition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_v1_ComponentStatus_To_core_ComponentStatus is an autogenerated conversion function.
func Convert_v1_ComponentStatus_To_core_ComponentStatus(in *corev1.ComponentStatus, out *core.ComponentStatus, s conversion.Scope) error {
	return autoConvert_v1_ComponentStatus_To_core_ComponentStatus(in, out, s)
}

func autoConvert_core_ComponentStatus_To_v1_ComponentStatus(in *core.ComponentStatus, out *corev1.ComponentStatus, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Conditions = *(*[]corev1.ComponentCondition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_core_ComponentStatus_To_v1_ComponentStatus is an autogenerated conversion function.
func Convert_core_ComponentStatus_To_v1_ComponentStatus(in *core.ComponentStatus, out *corev1.ComponentStatus, s conversion.Scope) error {
	return autoConvert_core_ComponentStatus_To_v1_ComponentStatus(in, out, s)
}

func autoConvert_v1_ComponentStatusList_To_core_ComponentStatusList(in *corev1.ComponentStatusList, out *core.ComponentStatusList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.ComponentStatus)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ComponentStatusList_To_core_ComponentStatusList is an autogenerated conversion function.
func Convert_v1_ComponentStatusList_To_core_ComponentStatusList(in *corev1.ComponentStatusList, out *core.ComponentStatusList, s conversion.Scope) error {
	return autoConvert_v1_ComponentStatusList_To_core_ComponentStatusList(in, out, s)
}

func autoConvert_core_ComponentStatusList_To_v1_ComponentStatusList(in *core.ComponentStatusList, out *corev1.ComponentStatusList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.ComponentStatus)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_ComponentStatusList_To_v1_ComponentStatusList is an autogenerated conversion function.
func Convert_core_ComponentStatusList_To_v1_ComponentStatusList(in *core.ComponentStatusList, out *corev1.ComponentStatusList, s conversion.Scope) error {
	return autoConvert_core_ComponentStatusList_To_v1_ComponentStatusList(in, out, s)
}

func autoConvert_v1_ConfigMap_To_core_ConfigMap(in *corev1.ConfigMap, out *core.ConfigMap, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Immutable = (*bool)(unsafe.Pointer(in.Immutable))
	out.Data = *(*map[string]string)(unsafe.Pointer(&in.Data))
	out.BinaryData = *(*map[string][]byte)(unsafe.Pointer(&in.BinaryData))
	return nil
}

// Convert_v1_ConfigMap_To_core_ConfigMap is an autogenerated conversion function.
func Convert_v1_ConfigMap_To_core_ConfigMap(in *corev1.ConfigMap, out *core.ConfigMap, s conversion.Scope) error {
	return autoConvert_v1_ConfigMap_To_core_ConfigMap(in, out, s)
}

func autoConvert_core_ConfigMap_To_v1_ConfigMap(in *core.ConfigMap, out *corev1.ConfigMap, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Immutable = (*bool)(unsafe.Pointer(in.Immutable))
	out.Data = *(*map[string]string)(unsafe.Pointer(&in.Data))
	out.BinaryData = *(*map[string][]byte)(unsafe.Pointer(&in.BinaryData))
	return nil
}

// Convert_core_ConfigMap_To_v1_ConfigMap is an autogenerated conversion function.
func Convert_core_ConfigMap_To_v1_ConfigMap(in *core.ConfigMap, out *corev1.ConfigMap, s conversion.Scope) error {
	return autoConvert_core_ConfigMap_To_v1_ConfigMap(in, out, s)
}

func autoConvert_v1_ConfigMapEnvSource_To_core_ConfigMapEnvSource(in *corev1.ConfigMapEnvSource, out *core.ConfigMapEnvSource, s conversion.Scope) error {
	if err := Convert_v1_LocalObjectReference_To_core_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_ConfigMapEnvSource_To_core_ConfigMapEnvSource is an autogenerated conversion function.
func Convert_v1_ConfigMapEnvSource_To_core_ConfigMapEnvSource(in *corev1.ConfigMapEnvSource, out *core.ConfigMapEnvSource, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapEnvSource_To_core_ConfigMapEnvSource(in, out, s)
}

func autoConvert_core_ConfigMapEnvSource_To_v1_ConfigMapEnvSource(in *core.ConfigMapEnvSource, out *corev1.ConfigMapEnvSource, s conversion.Scope) error {
	if err := Convert_core_LocalObjectReference_To_v1_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_ConfigMapEnvSource_To_v1_ConfigMapEnvSource is an autogenerated conversion function.
func Convert_core_ConfigMapEnvSource_To_v1_ConfigMapEnvSource(in *core.ConfigMapEnvSource, out *corev1.ConfigMapEnvSource, s conversion.Scope) error {
	return autoConvert_core_ConfigMapEnvSource_To_v1_ConfigMapEnvSource(in, out, s)
}

func autoConvert_v1_ConfigMapKeySelector_To_core_ConfigMapKeySelector(in *corev1.ConfigMapKeySelector, out *core.ConfigMapKeySelector, s conversion.Scope) error {
	if err := Convert_v1_LocalObjectReference_To_core_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Key = in.Key
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_ConfigMapKeySelector_To_core_ConfigMapKeySelector is an autogenerated conversion function.
func Convert_v1_ConfigMapKeySelector_To_core_ConfigMapKeySelector(in *corev1.ConfigMapKeySelector, out *core.ConfigMapKeySelector, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapKeySelector_To_core_ConfigMapKeySelector(in, out, s)
}

func autoConvert_core_ConfigMapKeySelector_To_v1_ConfigMapKeySelector(in *core.ConfigMapKeySelector, out *corev1.ConfigMapKeySelector, s conversion.Scope) error {
	if err := Convert_core_LocalObjectReference_To_v1_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Key = in.Key
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_ConfigMapKeySelector_To_v1_ConfigMapKeySelector is an autogenerated conversion function.
func Convert_core_ConfigMapKeySelector_To_v1_ConfigMapKeySelector(in *core.ConfigMapKeySelector, out *corev1.ConfigMapKeySelector, s conversion.Scope) error {
	return autoConvert_core_ConfigMapKeySelector_To_v1_ConfigMapKeySelector(in, out, s)
}

func autoConvert_v1_ConfigMapList_To_core_ConfigMapList(in *corev1.ConfigMapList, out *core.ConfigMapList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.ConfigMap)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ConfigMapList_To_core_ConfigMapList is an autogenerated conversion function.
func Convert_v1_ConfigMapList_To_core_ConfigMapList(in *corev1.ConfigMapList, out *core.ConfigMapList, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapList_To_core_ConfigMapList(in, out, s)
}

func autoConvert_core_ConfigMapList_To_v1_ConfigMapList(in *core.ConfigMapList, out *corev1.ConfigMapList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.ConfigMap)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_ConfigMapList_To_v1_ConfigMapList is an autogenerated conversion function.
func Convert_core_ConfigMapList_To_v1_ConfigMapList(in *core.ConfigMapList, out *corev1.ConfigMapList, s conversion.Scope) error {
	return autoConvert_core_ConfigMapList_To_v1_ConfigMapList(in, out, s)
}

func autoConvert_v1_ConfigMapNodeConfigSource_To_core_ConfigMapNodeConfigSource(in *corev1.ConfigMapNodeConfigSource, out *core.ConfigMapNodeConfigSource, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.UID = types.UID(in.UID)
	out.ResourceVersion = in.ResourceVersion
	out.KubeletConfigKey = in.KubeletConfigKey
	return nil
}

// Convert_v1_ConfigMapNodeConfigSource_To_core_ConfigMapNodeConfigSource is an autogenerated conversion function.
func Convert_v1_ConfigMapNodeConfigSource_To_core_ConfigMapNodeConfigSource(in *corev1.ConfigMapNodeConfigSource, out *core.ConfigMapNodeConfigSource, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapNodeConfigSource_To_core_ConfigMapNodeConfigSource(in, out, s)
}

func autoConvert_core_ConfigMapNodeConfigSource_To_v1_ConfigMapNodeConfigSource(in *core.ConfigMapNodeConfigSource, out *corev1.ConfigMapNodeConfigSource, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.UID = types.UID(in.UID)
	out.ResourceVersion = in.ResourceVersion
	out.KubeletConfigKey = in.KubeletConfigKey
	return nil
}

// Convert_core_ConfigMapNodeConfigSource_To_v1_ConfigMapNodeConfigSource is an autogenerated conversion function.
func Convert_core_ConfigMapNodeConfigSource_To_v1_ConfigMapNodeConfigSource(in *core.ConfigMapNodeConfigSource, out *corev1.ConfigMapNodeConfigSource, s conversion.Scope) error {
	return autoConvert_core_ConfigMapNodeConfigSource_To_v1_ConfigMapNodeConfigSource(in, out, s)
}

func autoConvert_v1_ConfigMapProjection_To_core_ConfigMapProjection(in *corev1.ConfigMapProjection, out *core.ConfigMapProjection, s conversion.Scope) error {
	if err := Convert_v1_LocalObjectReference_To_core_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Items = *(*[]core.KeyToPath)(unsafe.Pointer(&in.Items))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_ConfigMapProjection_To_core_ConfigMapProjection is an autogenerated conversion function.
func Convert_v1_ConfigMapProjection_To_core_ConfigMapProjection(in *corev1.ConfigMapProjection, out *core.ConfigMapProjection, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapProjection_To_core_ConfigMapProjection(in, out, s)
}

func autoConvert_core_ConfigMapProjection_To_v1_ConfigMapProjection(in *core.ConfigMapProjection, out *corev1.ConfigMapProjection, s conversion.Scope) error {
	if err := Convert_core_LocalObjectReference_To_v1_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Items = *(*[]corev1.KeyToPath)(unsafe.Pointer(&in.Items))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_ConfigMapProjection_To_v1_ConfigMapProjection is an autogenerated conversion function.
func Convert_core_ConfigMapProjection_To_v1_ConfigMapProjection(in *core.ConfigMapProjection, out *corev1.ConfigMapProjection, s conversion.Scope) error {
	return autoConvert_core_ConfigMapProjection_To_v1_ConfigMapProjection(in, out, s)
}

func autoConvert_v1_ConfigMapVolumeSource_To_core_ConfigMapVolumeSource(in *corev1.ConfigMapVolumeSource, out *core.ConfigMapVolumeSource, s conversion.Scope) error {
	if err := Convert_v1_LocalObjectReference_To_core_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Items = *(*[]core.KeyToPath)(unsafe.Pointer(&in.Items))
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_ConfigMapVolumeSource_To_core_ConfigMapVolumeSource is an autogenerated conversion function.
func Convert_v1_ConfigMapVolumeSource_To_core_ConfigMapVolumeSource(in *corev1.ConfigMapVolumeSource, out *core.ConfigMapVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapVolumeSource_To_core_ConfigMapVolumeSource(in, out, s)
}

func autoConvert_core_ConfigMapVolumeSource_To_v1_ConfigMapVolumeSource(in *core.ConfigMapVolumeSource, out *corev1.ConfigMapVolumeSource, s conversion.Scope) error {
	if err := Convert_core_LocalObjectReference_To_v1_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Items = *(*[]corev1.KeyToPath)(unsafe.Pointer(&in.Items))
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_ConfigMapVolumeSource_To_v1_ConfigMapVolumeSource is an autogenerated conversion function.
func Convert_core_ConfigMapVolumeSource_To_v1_ConfigMapVolumeSource(in *core.ConfigMapVolumeSource, out *corev1.ConfigMapVolumeSource, s conversion.Scope) error {
	return autoConvert_core_ConfigMapVolumeSource_To_v1_ConfigMapVolumeSource(in, out, s)
}

func autoConvert_v1_Container_To_core_Container(in *corev1.Container, out *core.Container, s conversion.Scope) error {
	out.Name = in.Name
	out.Image = in.Image
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	out.Args = *(*[]string)(unsafe.Pointer(&in.Args))
	out.WorkingDir = in.WorkingDir
	out.Ports = *(*[]core.ContainerPort)(unsafe.Pointer(&in.Ports))
	out.EnvFrom = *(*[]core.EnvFromSource)(unsafe.Pointer(&in.EnvFrom))
	out.Env = *(*[]core.EnvVar)(unsafe.Pointer(&in.Env))
	if err := Convert_v1_ResourceRequirements_To_core_ResourceRequirements(&in.Resources, &out.Resources, s); err != nil {
		return err
	}
	out.ResizePolicy = *(*[]core.ContainerResizePolicy)(unsafe.Pointer(&in.ResizePolicy))
	out.RestartPolicy = (*core.ContainerRestartPolicy)(unsafe.Pointer(in.RestartPolicy))
	out.VolumeMounts = *(*[]core.VolumeMount)(unsafe.Pointer(&in.VolumeMounts))
	out.VolumeDevices = *(*[]core.VolumeDevice)(unsafe.Pointer(&in.VolumeDevices))
	out.LivenessProbe = (*core.Probe)(unsafe.Pointer(in.LivenessProbe))
	out.ReadinessProbe = (*core.Probe)(unsafe.Pointer(in.ReadinessProbe))
	out.StartupProbe = (*core.Probe)(unsafe.Pointer(in.StartupProbe))
	out.Lifecycle = (*core.Lifecycle)(unsafe.Pointer(in.Lifecycle))
	out.TerminationMessagePath = in.TerminationMessagePath
	out.TerminationMessagePolicy = core.TerminationMessagePolicy(in.TerminationMessagePolicy)
	out.ImagePullPolicy = core.PullPolicy(in.ImagePullPolicy)
	out.SecurityContext = (*core.SecurityContext)(unsafe.Pointer(in.SecurityContext))
	out.Stdin = in.Stdin
	out.StdinOnce = in.StdinOnce
	out.TTY = in.TTY
	return nil
}

// Convert_v1_Container_To_core_Container is an autogenerated conversion function.
func Convert_v1_Container_To_core_Container(in *corev1.Container, out *core.Container, s conversion.Scope) error {
	return autoConvert_v1_Container_To_core_Container(in, out, s)
}

func autoConvert_core_Container_To_v1_Container(in *core.Container, out *corev1.Container, s conversion.Scope) error {
	out.Name = in.Name
	out.Image = in.Image
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	out.Args = *(*[]string)(unsafe.Pointer(&in.Args))
	out.WorkingDir = in.WorkingDir
	out.Ports = *(*[]corev1.ContainerPort)(unsafe.Pointer(&in.Ports))
	out.EnvFrom = *(*[]corev1.EnvFromSource)(unsafe.Pointer(&in.EnvFrom))
	out.Env = *(*[]corev1.EnvVar)(unsafe.Pointer(&in.Env))
	if err := Convert_core_ResourceRequirements_To_v1_ResourceRequirements(&in.Resources, &out.Resources, s); err != nil {
		return err
	}
	out.ResizePolicy = *(*[]corev1.ContainerResizePolicy)(unsafe.Pointer(&in.ResizePolicy))
	out.RestartPolicy = (*corev1.ContainerRestartPolicy)(unsafe.Pointer(in.RestartPolicy))
	out.VolumeMounts = *(*[]corev1.VolumeMount)(unsafe.Pointer(&in.VolumeMounts))
	out.VolumeDevices = *(*[]corev1.VolumeDevice)(unsafe.Pointer(&in.VolumeDevices))
	out.LivenessProbe = (*corev1.Probe)(unsafe.Pointer(in.LivenessProbe))
	out.ReadinessProbe = (*corev1.Probe)(unsafe.Pointer(in.ReadinessProbe))
	out.StartupProbe = (*corev1.Probe)(unsafe.Pointer(in.StartupProbe))
	out.Lifecycle = (*corev1.Lifecycle)(unsafe.Pointer(in.Lifecycle))
	out.TerminationMessagePath = in.TerminationMessagePath
	out.TerminationMessagePolicy = corev1.TerminationMessagePolicy(in.TerminationMessagePolicy)
	out.ImagePullPolicy = corev1.PullPolicy(in.ImagePullPolicy)
	out.SecurityContext = (*corev1.SecurityContext)(unsafe.Pointer(in.SecurityContext))
	out.Stdin = in.Stdin
	out.StdinOnce = in.StdinOnce
	out.TTY = in.TTY
	return nil
}

// Convert_core_Container_To_v1_Container is an autogenerated conversion function.
func Convert_core_Container_To_v1_Container(in *core.Container, out *corev1.Container, s conversion.Scope) error {
	return autoConvert_core_Container_To_v1_Container(in, out, s)
}

func autoConvert_v1_ContainerImage_To_core_ContainerImage(in *corev1.ContainerImage, out *core.ContainerImage, s conversion.Scope) error {
	out.Names = *(*[]string)(unsafe.Pointer(&in.Names))
	out.SizeBytes = in.SizeBytes
	return nil
}

// Convert_v1_ContainerImage_To_core_ContainerImage is an autogenerated conversion function.
func Convert_v1_ContainerImage_To_core_ContainerImage(in *corev1.ContainerImage, out *core.ContainerImage, s conversion.Scope) error {
	return autoConvert_v1_ContainerImage_To_core_ContainerImage(in, out, s)
}

func autoConvert_core_ContainerImage_To_v1_ContainerImage(in *core.ContainerImage, out *corev1.ContainerImage, s conversion.Scope) error {
	out.Names = *(*[]string)(unsafe.Pointer(&in.Names))
	out.SizeBytes = in.SizeBytes
	return nil
}

// Convert_core_ContainerImage_To_v1_ContainerImage is an autogenerated conversion function.
func Convert_core_ContainerImage_To_v1_ContainerImage(in *core.ContainerImage, out *corev1.ContainerImage, s conversion.Scope) error {
	return autoConvert_core_ContainerImage_To_v1_ContainerImage(in, out, s)
}

func autoConvert_v1_ContainerPort_To_core_ContainerPort(in *corev1.ContainerPort, out *core.ContainerPort, s conversion.Scope) error {
	out.Name = in.Name
	out.HostPort = in.HostPort
	out.ContainerPort = in.ContainerPort
	out.Protocol = core.Protocol(in.Protocol)
	out.HostIP = in.HostIP
	return nil
}

// Convert_v1_ContainerPort_To_core_ContainerPort is an autogenerated conversion function.
func Convert_v1_ContainerPort_To_core_ContainerPort(in *corev1.ContainerPort, out *core.ContainerPort, s conversion.Scope) error {
	return autoConvert_v1_ContainerPort_To_core_ContainerPort(in, out, s)
}

func autoConvert_core_ContainerPort_To_v1_ContainerPort(in *core.ContainerPort, out *corev1.ContainerPort, s conversion.Scope) error {
	out.Name = in.Name
	out.HostPort = in.HostPort
	out.ContainerPort = in.ContainerPort
	out.Protocol = corev1.Protocol(in.Protocol)
	out.HostIP = in.HostIP
	return nil
}

// Convert_core_ContainerPort_To_v1_ContainerPort is an autogenerated conversion function.
func Convert_core_ContainerPort_To_v1_ContainerPort(in *core.ContainerPort, out *corev1.ContainerPort, s conversion.Scope) error {
	return autoConvert_core_ContainerPort_To_v1_ContainerPort(in, out, s)
}

func autoConvert_v1_ContainerResizePolicy_To_core_ContainerResizePolicy(in *corev1.ContainerResizePolicy, out *core.ContainerResizePolicy, s conversion.Scope) error {
	out.ResourceName = core.ResourceName(in.ResourceName)
	out.RestartPolicy = core.ResourceResizeRestartPolicy(in.RestartPolicy)
	return nil
}

// Convert_v1_ContainerResizePolicy_To_core_ContainerResizePolicy is an autogenerated conversion function.
func Convert_v1_ContainerResizePolicy_To_core_ContainerResizePolicy(in *corev1.ContainerResizePolicy, out *core.ContainerResizePolicy, s conversion.Scope) error {
	return autoConvert_v1_ContainerResizePolicy_To_core_ContainerResizePolicy(in, out, s)
}

func autoConvert_core_ContainerResizePolicy_To_v1_ContainerResizePolicy(in *core.ContainerResizePolicy, out *corev1.ContainerResizePolicy, s conversion.Scope) error {
	out.ResourceName = corev1.ResourceName(in.ResourceName)
	out.RestartPolicy = corev1.ResourceResizeRestartPolicy(in.RestartPolicy)
	return nil
}

// Convert_core_ContainerResizePolicy_To_v1_ContainerResizePolicy is an autogenerated conversion function.
func Convert_core_ContainerResizePolicy_To_v1_ContainerResizePolicy(in *core.ContainerResizePolicy, out *corev1.ContainerResizePolicy, s conversion.Scope) error {
	return autoConvert_core_ContainerResizePolicy_To_v1_ContainerResizePolicy(in, out, s)
}

func autoConvert_v1_ContainerState_To_core_ContainerState(in *corev1.ContainerState, out *core.ContainerState, s conversion.Scope) error {
	out.Waiting = (*core.ContainerStateWaiting)(unsafe.Pointer(in.Waiting))
	out.Running = (*core.ContainerStateRunning)(unsafe.Pointer(in.Running))
	out.Terminated = (*core.ContainerStateTerminated)(unsafe.Pointer(in.Terminated))
	return nil
}

// Convert_v1_ContainerState_To_core_ContainerState is an autogenerated conversion function.
func Convert_v1_ContainerState_To_core_ContainerState(in *corev1.ContainerState, out *core.ContainerState, s conversion.Scope) error {
	return autoConvert_v1_ContainerState_To_core_ContainerState(in, out, s)
}

func autoConvert_core_ContainerState_To_v1_ContainerState(in *core.ContainerState, out *corev1.ContainerState, s conversion.Scope) error {
	out.Waiting = (*corev1.ContainerStateWaiting)(unsafe.Pointer(in.Waiting))
	out.Running = (*corev1.ContainerStateRunning)(unsafe.Pointer(in.Running))
	out.Terminated = (*corev1.ContainerStateTerminated)(unsafe.Pointer(in.Terminated))
	return nil
}

// Convert_core_ContainerState_To_v1_ContainerState is an autogenerated conversion function.
func Convert_core_ContainerState_To_v1_ContainerState(in *core.ContainerState, out *corev1.ContainerState, s conversion.Scope) error {
	return autoConvert_core_ContainerState_To_v1_ContainerState(in, out, s)
}

func autoConvert_v1_ContainerStateRunning_To_core_ContainerStateRunning(in *corev1.ContainerStateRunning, out *core.ContainerStateRunning, s conversion.Scope) error {
	out.StartedAt = in.StartedAt
	return nil
}

// Convert_v1_ContainerStateRunning_To_core_ContainerStateRunning is an autogenerated conversion function.
func Convert_v1_ContainerStateRunning_To_core_ContainerStateRunning(in *corev1.ContainerStateRunning, out *core.ContainerStateRunning, s conversion.Scope) error {
	return autoConvert_v1_ContainerStateRunning_To_core_ContainerStateRunning(in, out, s)
}

func autoConvert_core_ContainerStateRunning_To_v1_ContainerStateRunning(in *core.ContainerStateRunning, out *corev1.ContainerStateRunning, s conversion.Scope) error {
	out.StartedAt = in.StartedAt
	return nil
}

// Convert_core_ContainerStateRunning_To_v1_ContainerStateRunning is an autogenerated conversion function.
func Convert_core_ContainerStateRunning_To_v1_ContainerStateRunning(in *core.ContainerStateRunning, out *corev1.ContainerStateRunning, s conversion.Scope) error {
	return autoConvert_core_ContainerStateRunning_To_v1_ContainerStateRunning(in, out, s)
}

func autoConvert_v1_ContainerStateTerminated_To_core_ContainerStateTerminated(in *corev1.ContainerStateTerminated, out *core.ContainerStateTerminated, s conversion.Scope) error {
	out.ExitCode = in.ExitCode
	out.Signal = in.Signal
	out.Reason = in.Reason
	out.Message = in.Message
	out.StartedAt = in.StartedAt
	out.FinishedAt = in.FinishedAt
	out.ContainerID = in.ContainerID
	return nil
}

// Convert_v1_ContainerStateTerminated_To_core_ContainerStateTerminated is an autogenerated conversion function.
func Convert_v1_ContainerStateTerminated_To_core_ContainerStateTerminated(in *corev1.ContainerStateTerminated, out *core.ContainerStateTerminated, s conversion.Scope) error {
	return autoConvert_v1_ContainerStateTerminated_To_core_ContainerStateTerminated(in, out, s)
}

func autoConvert_core_ContainerStateTerminated_To_v1_ContainerStateTerminated(in *core.ContainerStateTerminated, out *corev1.ContainerStateTerminated, s conversion.Scope) error {
	out.ExitCode = in.ExitCode
	out.Signal = in.Signal
	out.Reason = in.Reason
	out.Message = in.Message
	out.StartedAt = in.StartedAt
	out.FinishedAt = in.FinishedAt
	out.ContainerID = in.ContainerID
	return nil
}

// Convert_core_ContainerStateTerminated_To_v1_ContainerStateTerminated is an autogenerated conversion function.
func Convert_core_ContainerStateTerminated_To_v1_ContainerStateTerminated(in *core.ContainerStateTerminated, out *corev1.ContainerStateTerminated, s conversion.Scope) error {
	return autoConvert_core_ContainerStateTerminated_To_v1_ContainerStateTerminated(in, out, s)
}

func autoConvert_v1_ContainerStateWaiting_To_core_ContainerStateWaiting(in *corev1.ContainerStateWaiting, out *core.ContainerStateWaiting, s conversion.Scope) error {
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_ContainerStateWaiting_To_core_ContainerStateWaiting is an autogenerated conversion function.
func Convert_v1_ContainerStateWaiting_To_core_ContainerStateWaiting(in *corev1.ContainerStateWaiting, out *core.ContainerStateWaiting, s conversion.Scope) error {
	return autoConvert_v1_ContainerStateWaiting_To_core_ContainerStateWaiting(in, out, s)
}

func autoConvert_core_ContainerStateWaiting_To_v1_ContainerStateWaiting(in *core.ContainerStateWaiting, out *corev1.ContainerStateWaiting, s conversion.Scope) error {
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_core_ContainerStateWaiting_To_v1_ContainerStateWaiting is an autogenerated conversion function.
func Convert_core_ContainerStateWaiting_To_v1_ContainerStateWaiting(in *core.ContainerStateWaiting, out *corev1.ContainerStateWaiting, s conversion.Scope) error {
	return autoConvert_core_ContainerStateWaiting_To_v1_ContainerStateWaiting(in, out, s)
}

func autoConvert_v1_ContainerStatus_To_core_ContainerStatus(in *corev1.ContainerStatus, out *core.ContainerStatus, s conversion.Scope) error {
	out.Name = in.Name
	if err := Convert_v1_ContainerState_To_core_ContainerState(&in.State, &out.State, s); err != nil {
		return err
	}
	if err := Convert_v1_ContainerState_To_core_ContainerState(&in.LastTerminationState, &out.LastTerminationState, s); err != nil {
		return err
	}
	out.Ready = in.Ready
	out.RestartCount = in.RestartCount
	out.Image = in.Image
	out.ImageID = in.ImageID
	out.ContainerID = in.ContainerID
	out.Started = (*bool)(unsafe.Pointer(in.Started))
	out.AllocatedResources = *(*core.ResourceList)(unsafe.Pointer(&in.AllocatedResources))
	out.Resources = (*core.ResourceRequirements)(unsafe.Pointer(in.Resources))
	out.VolumeMounts = *(*[]core.VolumeMountStatus)(unsafe.Pointer(&in.VolumeMounts))
	out.User = (*core.ContainerUser)(unsafe.Pointer(in.User))
	out.AllocatedResourcesStatus = *(*[]core.ResourceStatus)(unsafe.Pointer(&in.AllocatedResourcesStatus))
	out.StopSignal = (*core.Signal)(unsafe.Pointer(in.StopSignal))
	return nil
}

// Convert_v1_ContainerStatus_To_core_ContainerStatus is an autogenerated conversion function.
func Convert_v1_ContainerStatus_To_core_ContainerStatus(in *corev1.ContainerStatus, out *core.ContainerStatus, s conversion.Scope) error {
	return autoConvert_v1_ContainerStatus_To_core_ContainerStatus(in, out, s)
}

func autoConvert_core_ContainerStatus_To_v1_ContainerStatus(in *core.ContainerStatus, out *corev1.ContainerStatus, s conversion.Scope) error {
	out.Name = in.Name
	if err := Convert_core_ContainerState_To_v1_ContainerState(&in.State, &out.State, s); err != nil {
		return err
	}
	if err := Convert_core_ContainerState_To_v1_ContainerState(&in.LastTerminationState, &out.LastTerminationState, s); err != nil {
		return err
	}
	out.Ready = in.Ready
	out.RestartCount = in.RestartCount
	out.Image = in.Image
	out.ImageID = in.ImageID
	out.ContainerID = in.ContainerID
	out.Started = (*bool)(unsafe.Pointer(in.Started))
	out.AllocatedResources = *(*corev1.ResourceList)(unsafe.Pointer(&in.AllocatedResources))
	out.Resources = (*corev1.ResourceRequirements)(unsafe.Pointer(in.Resources))
	out.VolumeMounts = *(*[]corev1.VolumeMountStatus)(unsafe.Pointer(&in.VolumeMounts))
	out.User = (*corev1.ContainerUser)(unsafe.Pointer(in.User))
	out.AllocatedResourcesStatus = *(*[]corev1.ResourceStatus)(unsafe.Pointer(&in.AllocatedResourcesStatus))
	out.StopSignal = (*corev1.Signal)(unsafe.Pointer(in.StopSignal))
	return nil
}

// Convert_core_ContainerStatus_To_v1_ContainerStatus is an autogenerated conversion function.
func Convert_core_ContainerStatus_To_v1_ContainerStatus(in *core.ContainerStatus, out *corev1.ContainerStatus, s conversion.Scope) error {
	return autoConvert_core_ContainerStatus_To_v1_ContainerStatus(in, out, s)
}

func autoConvert_v1_ContainerUser_To_core_ContainerUser(in *corev1.ContainerUser, out *core.ContainerUser, s conversion.Scope) error {
	out.Linux = (*core.LinuxContainerUser)(unsafe.Pointer(in.Linux))
	return nil
}

// Convert_v1_ContainerUser_To_core_ContainerUser is an autogenerated conversion function.
func Convert_v1_ContainerUser_To_core_ContainerUser(in *corev1.ContainerUser, out *core.ContainerUser, s conversion.Scope) error {
	return autoConvert_v1_ContainerUser_To_core_ContainerUser(in, out, s)
}

func autoConvert_core_ContainerUser_To_v1_ContainerUser(in *core.ContainerUser, out *corev1.ContainerUser, s conversion.Scope) error {
	out.Linux = (*corev1.LinuxContainerUser)(unsafe.Pointer(in.Linux))
	return nil
}

// Convert_core_ContainerUser_To_v1_ContainerUser is an autogenerated conversion function.
func Convert_core_ContainerUser_To_v1_ContainerUser(in *core.ContainerUser, out *corev1.ContainerUser, s conversion.Scope) error {
	return autoConvert_core_ContainerUser_To_v1_ContainerUser(in, out, s)
}

func autoConvert_v1_DaemonEndpoint_To_core_DaemonEndpoint(in *corev1.DaemonEndpoint, out *core.DaemonEndpoint, s conversion.Scope) error {
	out.Port = in.Port
	return nil
}

// Convert_v1_DaemonEndpoint_To_core_DaemonEndpoint is an autogenerated conversion function.
func Convert_v1_DaemonEndpoint_To_core_DaemonEndpoint(in *corev1.DaemonEndpoint, out *core.DaemonEndpoint, s conversion.Scope) error {
	return autoConvert_v1_DaemonEndpoint_To_core_DaemonEndpoint(in, out, s)
}

func autoConvert_core_DaemonEndpoint_To_v1_DaemonEndpoint(in *core.DaemonEndpoint, out *corev1.DaemonEndpoint, s conversion.Scope) error {
	out.Port = in.Port
	return nil
}

// Convert_core_DaemonEndpoint_To_v1_DaemonEndpoint is an autogenerated conversion function.
func Convert_core_DaemonEndpoint_To_v1_DaemonEndpoint(in *core.DaemonEndpoint, out *corev1.DaemonEndpoint, s conversion.Scope) error {
	return autoConvert_core_DaemonEndpoint_To_v1_DaemonEndpoint(in, out, s)
}

func autoConvert_v1_DownwardAPIProjection_To_core_DownwardAPIProjection(in *corev1.DownwardAPIProjection, out *core.DownwardAPIProjection, s conversion.Scope) error {
	out.Items = *(*[]core.DownwardAPIVolumeFile)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_DownwardAPIProjection_To_core_DownwardAPIProjection is an autogenerated conversion function.
func Convert_v1_DownwardAPIProjection_To_core_DownwardAPIProjection(in *corev1.DownwardAPIProjection, out *core.DownwardAPIProjection, s conversion.Scope) error {
	return autoConvert_v1_DownwardAPIProjection_To_core_DownwardAPIProjection(in, out, s)
}

func autoConvert_core_DownwardAPIProjection_To_v1_DownwardAPIProjection(in *core.DownwardAPIProjection, out *corev1.DownwardAPIProjection, s conversion.Scope) error {
	out.Items = *(*[]corev1.DownwardAPIVolumeFile)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_DownwardAPIProjection_To_v1_DownwardAPIProjection is an autogenerated conversion function.
func Convert_core_DownwardAPIProjection_To_v1_DownwardAPIProjection(in *core.DownwardAPIProjection, out *corev1.DownwardAPIProjection, s conversion.Scope) error {
	return autoConvert_core_DownwardAPIProjection_To_v1_DownwardAPIProjection(in, out, s)
}

func autoConvert_v1_DownwardAPIVolumeFile_To_core_DownwardAPIVolumeFile(in *corev1.DownwardAPIVolumeFile, out *core.DownwardAPIVolumeFile, s conversion.Scope) error {
	out.Path = in.Path
	out.FieldRef = (*core.ObjectFieldSelector)(unsafe.Pointer(in.FieldRef))
	out.ResourceFieldRef = (*core.ResourceFieldSelector)(unsafe.Pointer(in.ResourceFieldRef))
	out.Mode = (*int32)(unsafe.Pointer(in.Mode))
	return nil
}

// Convert_v1_DownwardAPIVolumeFile_To_core_DownwardAPIVolumeFile is an autogenerated conversion function.
func Convert_v1_DownwardAPIVolumeFile_To_core_DownwardAPIVolumeFile(in *corev1.DownwardAPIVolumeFile, out *core.DownwardAPIVolumeFile, s conversion.Scope) error {
	return autoConvert_v1_DownwardAPIVolumeFile_To_core_DownwardAPIVolumeFile(in, out, s)
}

func autoConvert_core_DownwardAPIVolumeFile_To_v1_DownwardAPIVolumeFile(in *core.DownwardAPIVolumeFile, out *corev1.DownwardAPIVolumeFile, s conversion.Scope) error {
	out.Path = in.Path
	out.FieldRef = (*corev1.ObjectFieldSelector)(unsafe.Pointer(in.FieldRef))
	out.ResourceFieldRef = (*corev1.ResourceFieldSelector)(unsafe.Pointer(in.ResourceFieldRef))
	out.Mode = (*int32)(unsafe.Pointer(in.Mode))
	return nil
}

// Convert_core_DownwardAPIVolumeFile_To_v1_DownwardAPIVolumeFile is an autogenerated conversion function.
func Convert_core_DownwardAPIVolumeFile_To_v1_DownwardAPIVolumeFile(in *core.DownwardAPIVolumeFile, out *corev1.DownwardAPIVolumeFile, s conversion.Scope) error {
	return autoConvert_core_DownwardAPIVolumeFile_To_v1_DownwardAPIVolumeFile(in, out, s)
}

func autoConvert_v1_DownwardAPIVolumeSource_To_core_DownwardAPIVolumeSource(in *corev1.DownwardAPIVolumeSource, out *core.DownwardAPIVolumeSource, s conversion.Scope) error {
	out.Items = *(*[]core.DownwardAPIVolumeFile)(unsafe.Pointer(&in.Items))
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	return nil
}

// Convert_v1_DownwardAPIVolumeSource_To_core_DownwardAPIVolumeSource is an autogenerated conversion function.
func Convert_v1_DownwardAPIVolumeSource_To_core_DownwardAPIVolumeSource(in *corev1.DownwardAPIVolumeSource, out *core.DownwardAPIVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_DownwardAPIVolumeSource_To_core_DownwardAPIVolumeSource(in, out, s)
}

func autoConvert_core_DownwardAPIVolumeSource_To_v1_DownwardAPIVolumeSource(in *core.DownwardAPIVolumeSource, out *corev1.DownwardAPIVolumeSource, s conversion.Scope) error {
	out.Items = *(*[]corev1.DownwardAPIVolumeFile)(unsafe.Pointer(&in.Items))
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	return nil
}

// Convert_core_DownwardAPIVolumeSource_To_v1_DownwardAPIVolumeSource is an autogenerated conversion function.
func Convert_core_DownwardAPIVolumeSource_To_v1_DownwardAPIVolumeSource(in *core.DownwardAPIVolumeSource, out *corev1.DownwardAPIVolumeSource, s conversion.Scope) error {
	return autoConvert_core_DownwardAPIVolumeSource_To_v1_DownwardAPIVolumeSource(in, out, s)
}

func autoConvert_v1_EmptyDirVolumeSource_To_core_EmptyDirVolumeSource(in *corev1.EmptyDirVolumeSource, out *core.EmptyDirVolumeSource, s conversion.Scope) error {
	out.Medium = core.StorageMedium(in.Medium)
	out.SizeLimit = (*resource.Quantity)(unsafe.Pointer(in.SizeLimit))
	return nil
}

// Convert_v1_EmptyDirVolumeSource_To_core_EmptyDirVolumeSource is an autogenerated conversion function.
func Convert_v1_EmptyDirVolumeSource_To_core_EmptyDirVolumeSource(in *corev1.EmptyDirVolumeSource, out *core.EmptyDirVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_EmptyDirVolumeSource_To_core_EmptyDirVolumeSource(in, out, s)
}

func autoConvert_core_EmptyDirVolumeSource_To_v1_EmptyDirVolumeSource(in *core.EmptyDirVolumeSource, out *corev1.EmptyDirVolumeSource, s conversion.Scope) error {
	out.Medium = corev1.StorageMedium(in.Medium)
	out.SizeLimit = (*resource.Quantity)(unsafe.Pointer(in.SizeLimit))
	return nil
}

// Convert_core_EmptyDirVolumeSource_To_v1_EmptyDirVolumeSource is an autogenerated conversion function.
func Convert_core_EmptyDirVolumeSource_To_v1_EmptyDirVolumeSource(in *core.EmptyDirVolumeSource, out *corev1.EmptyDirVolumeSource, s conversion.Scope) error {
	return autoConvert_core_EmptyDirVolumeSource_To_v1_EmptyDirVolumeSource(in, out, s)
}

func autoConvert_v1_EndpointAddress_To_core_EndpointAddress(in *corev1.EndpointAddress, out *core.EndpointAddress, s conversion.Scope) error {
	out.IP = in.IP
	out.Hostname = in.Hostname
	out.NodeName = (*string)(unsafe.Pointer(in.NodeName))
	out.TargetRef = (*core.ObjectReference)(unsafe.Pointer(in.TargetRef))
	return nil
}

// Convert_v1_EndpointAddress_To_core_EndpointAddress is an autogenerated conversion function.
func Convert_v1_EndpointAddress_To_core_EndpointAddress(in *corev1.EndpointAddress, out *core.EndpointAddress, s conversion.Scope) error {
	return autoConvert_v1_EndpointAddress_To_core_EndpointAddress(in, out, s)
}

func autoConvert_core_EndpointAddress_To_v1_EndpointAddress(in *core.EndpointAddress, out *corev1.EndpointAddress, s conversion.Scope) error {
	out.IP = in.IP
	out.Hostname = in.Hostname
	out.NodeName = (*string)(unsafe.Pointer(in.NodeName))
	out.TargetRef = (*corev1.ObjectReference)(unsafe.Pointer(in.TargetRef))
	return nil
}

// Convert_core_EndpointAddress_To_v1_EndpointAddress is an autogenerated conversion function.
func Convert_core_EndpointAddress_To_v1_EndpointAddress(in *core.EndpointAddress, out *corev1.EndpointAddress, s conversion.Scope) error {
	return autoConvert_core_EndpointAddress_To_v1_EndpointAddress(in, out, s)
}

func autoConvert_v1_EndpointPort_To_core_EndpointPort(in *corev1.EndpointPort, out *core.EndpointPort, s conversion.Scope) error {
	out.Name = in.Name
	out.Port = in.Port
	out.Protocol = core.Protocol(in.Protocol)
	out.AppProtocol = (*string)(unsafe.Pointer(in.AppProtocol))
	return nil
}

// Convert_v1_EndpointPort_To_core_EndpointPort is an autogenerated conversion function.
func Convert_v1_EndpointPort_To_core_EndpointPort(in *corev1.EndpointPort, out *core.EndpointPort, s conversion.Scope) error {
	return autoConvert_v1_EndpointPort_To_core_EndpointPort(in, out, s)
}

func autoConvert_core_EndpointPort_To_v1_EndpointPort(in *core.EndpointPort, out *corev1.EndpointPort, s conversion.Scope) error {
	out.Name = in.Name
	out.Port = in.Port
	out.Protocol = corev1.Protocol(in.Protocol)
	out.AppProtocol = (*string)(unsafe.Pointer(in.AppProtocol))
	return nil
}

// Convert_core_EndpointPort_To_v1_EndpointPort is an autogenerated conversion function.
func Convert_core_EndpointPort_To_v1_EndpointPort(in *core.EndpointPort, out *corev1.EndpointPort, s conversion.Scope) error {
	return autoConvert_core_EndpointPort_To_v1_EndpointPort(in, out, s)
}

func autoConvert_v1_EndpointSubset_To_core_EndpointSubset(in *corev1.EndpointSubset, out *core.EndpointSubset, s conversion.Scope) error {
	out.Addresses = *(*[]core.EndpointAddress)(unsafe.Pointer(&in.Addresses))
	out.NotReadyAddresses = *(*[]core.EndpointAddress)(unsafe.Pointer(&in.NotReadyAddresses))
	out.Ports = *(*[]core.EndpointPort)(unsafe.Pointer(&in.Ports))
	return nil
}

// Convert_v1_EndpointSubset_To_core_EndpointSubset is an autogenerated conversion function.
func Convert_v1_EndpointSubset_To_core_EndpointSubset(in *corev1.EndpointSubset, out *core.EndpointSubset, s conversion.Scope) error {
	return autoConvert_v1_EndpointSubset_To_core_EndpointSubset(in, out, s)
}

func autoConvert_core_EndpointSubset_To_v1_EndpointSubset(in *core.EndpointSubset, out *corev1.EndpointSubset, s conversion.Scope) error {
	out.Addresses = *(*[]corev1.EndpointAddress)(unsafe.Pointer(&in.Addresses))
	out.NotReadyAddresses = *(*[]corev1.EndpointAddress)(unsafe.Pointer(&in.NotReadyAddresses))
	out.Ports = *(*[]corev1.EndpointPort)(unsafe.Pointer(&in.Ports))
	return nil
}

// Convert_core_EndpointSubset_To_v1_EndpointSubset is an autogenerated conversion function.
func Convert_core_EndpointSubset_To_v1_EndpointSubset(in *core.EndpointSubset, out *corev1.EndpointSubset, s conversion.Scope) error {
	return autoConvert_core_EndpointSubset_To_v1_EndpointSubset(in, out, s)
}

func autoConvert_v1_Endpoints_To_core_Endpoints(in *corev1.Endpoints, out *core.Endpoints, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Subsets = *(*[]core.EndpointSubset)(unsafe.Pointer(&in.Subsets))
	return nil
}

// Convert_v1_Endpoints_To_core_Endpoints is an autogenerated conversion function.
func Convert_v1_Endpoints_To_core_Endpoints(in *corev1.Endpoints, out *core.Endpoints, s conversion.Scope) error {
	return autoConvert_v1_Endpoints_To_core_Endpoints(in, out, s)
}

func autoConvert_core_Endpoints_To_v1_Endpoints(in *core.Endpoints, out *corev1.Endpoints, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Subsets = *(*[]corev1.EndpointSubset)(unsafe.Pointer(&in.Subsets))
	return nil
}

// Convert_core_Endpoints_To_v1_Endpoints is an autogenerated conversion function.
func Convert_core_Endpoints_To_v1_Endpoints(in *core.Endpoints, out *corev1.Endpoints, s conversion.Scope) error {
	return autoConvert_core_Endpoints_To_v1_Endpoints(in, out, s)
}

func autoConvert_v1_EndpointsList_To_core_EndpointsList(in *corev1.EndpointsList, out *core.EndpointsList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.Endpoints)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_EndpointsList_To_core_EndpointsList is an autogenerated conversion function.
func Convert_v1_EndpointsList_To_core_EndpointsList(in *corev1.EndpointsList, out *core.EndpointsList, s conversion.Scope) error {
	return autoConvert_v1_EndpointsList_To_core_EndpointsList(in, out, s)
}

func autoConvert_core_EndpointsList_To_v1_EndpointsList(in *core.EndpointsList, out *corev1.EndpointsList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.Endpoints)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_EndpointsList_To_v1_EndpointsList is an autogenerated conversion function.
func Convert_core_EndpointsList_To_v1_EndpointsList(in *core.EndpointsList, out *corev1.EndpointsList, s conversion.Scope) error {
	return autoConvert_core_EndpointsList_To_v1_EndpointsList(in, out, s)
}

func autoConvert_v1_EnvFromSource_To_core_EnvFromSource(in *corev1.EnvFromSource, out *core.EnvFromSource, s conversion.Scope) error {
	out.Prefix = in.Prefix
	out.ConfigMapRef = (*core.ConfigMapEnvSource)(unsafe.Pointer(in.ConfigMapRef))
	out.SecretRef = (*core.SecretEnvSource)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_v1_EnvFromSource_To_core_EnvFromSource is an autogenerated conversion function.
func Convert_v1_EnvFromSource_To_core_EnvFromSource(in *corev1.EnvFromSource, out *core.EnvFromSource, s conversion.Scope) error {
	return autoConvert_v1_EnvFromSource_To_core_EnvFromSource(in, out, s)
}

func autoConvert_core_EnvFromSource_To_v1_EnvFromSource(in *core.EnvFromSource, out *corev1.EnvFromSource, s conversion.Scope) error {
	out.Prefix = in.Prefix
	out.ConfigMapRef = (*corev1.ConfigMapEnvSource)(unsafe.Pointer(in.ConfigMapRef))
	out.SecretRef = (*corev1.SecretEnvSource)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_core_EnvFromSource_To_v1_EnvFromSource is an autogenerated conversion function.
func Convert_core_EnvFromSource_To_v1_EnvFromSource(in *core.EnvFromSource, out *corev1.EnvFromSource, s conversion.Scope) error {
	return autoConvert_core_EnvFromSource_To_v1_EnvFromSource(in, out, s)
}

func autoConvert_v1_EnvVar_To_core_EnvVar(in *corev1.EnvVar, out *core.EnvVar, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = in.Value
	out.ValueFrom = (*core.EnvVarSource)(unsafe.Pointer(in.ValueFrom))
	return nil
}

// Convert_v1_EnvVar_To_core_EnvVar is an autogenerated conversion function.
func Convert_v1_EnvVar_To_core_EnvVar(in *corev1.EnvVar, out *core.EnvVar, s conversion.Scope) error {
	return autoConvert_v1_EnvVar_To_core_EnvVar(in, out, s)
}

func autoConvert_core_EnvVar_To_v1_EnvVar(in *core.EnvVar, out *corev1.EnvVar, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = in.Value
	out.ValueFrom = (*corev1.EnvVarSource)(unsafe.Pointer(in.ValueFrom))
	return nil
}

// Convert_core_EnvVar_To_v1_EnvVar is an autogenerated conversion function.
func Convert_core_EnvVar_To_v1_EnvVar(in *core.EnvVar, out *corev1.EnvVar, s conversion.Scope) error {
	return autoConvert_core_EnvVar_To_v1_EnvVar(in, out, s)
}

func autoConvert_v1_EnvVarSource_To_core_EnvVarSource(in *corev1.EnvVarSource, out *core.EnvVarSource, s conversion.Scope) error {
	out.FieldRef = (*core.ObjectFieldSelector)(unsafe.Pointer(in.FieldRef))
	out.ResourceFieldRef = (*core.ResourceFieldSelector)(unsafe.Pointer(in.ResourceFieldRef))
	out.ConfigMapKeyRef = (*core.ConfigMapKeySelector)(unsafe.Pointer(in.ConfigMapKeyRef))
	out.SecretKeyRef = (*core.SecretKeySelector)(unsafe.Pointer(in.SecretKeyRef))
	out.FileKeyRef = (*core.FileKeySelector)(unsafe.Pointer(in.FileKeyRef))
	return nil
}

// Convert_v1_EnvVarSource_To_core_EnvVarSource is an autogenerated conversion function.
func Convert_v1_EnvVarSource_To_core_EnvVarSource(in *corev1.EnvVarSource, out *core.EnvVarSource, s conversion.Scope) error {
	return autoConvert_v1_EnvVarSource_To_core_EnvVarSource(in, out, s)
}

func autoConvert_core_EnvVarSource_To_v1_EnvVarSource(in *core.EnvVarSource, out *corev1.EnvVarSource, s conversion.Scope) error {
	out.FieldRef = (*corev1.ObjectFieldSelector)(unsafe.Pointer(in.FieldRef))
	out.ResourceFieldRef = (*corev1.ResourceFieldSelector)(unsafe.Pointer(in.ResourceFieldRef))
	out.ConfigMapKeyRef = (*corev1.ConfigMapKeySelector)(unsafe.Pointer(in.ConfigMapKeyRef))
	out.SecretKeyRef = (*corev1.SecretKeySelector)(unsafe.Pointer(in.SecretKeyRef))
	out.FileKeyRef = (*corev1.FileKeySelector)(unsafe.Pointer(in.FileKeyRef))
	return nil
}

// Convert_core_EnvVarSource_To_v1_EnvVarSource is an autogenerated conversion function.
func Convert_core_EnvVarSource_To_v1_EnvVarSource(in *core.EnvVarSource, out *corev1.EnvVarSource, s conversion.Scope) error {
	return autoConvert_core_EnvVarSource_To_v1_EnvVarSource(in, out, s)
}

func autoConvert_v1_EphemeralContainer_To_core_EphemeralContainer(in *corev1.EphemeralContainer, out *core.EphemeralContainer, s conversion.Scope) error {
	if err := Convert_v1_EphemeralContainerCommon_To_core_EphemeralContainerCommon(&in.EphemeralContainerCommon, &out.EphemeralContainerCommon, s); err != nil {
		return err
	}
	out.TargetContainerName = in.TargetContainerName
	return nil
}

// Convert_v1_EphemeralContainer_To_core_EphemeralContainer is an autogenerated conversion function.
func Convert_v1_EphemeralContainer_To_core_EphemeralContainer(in *corev1.EphemeralContainer, out *core.EphemeralContainer, s conversion.Scope) error {
	return autoConvert_v1_EphemeralContainer_To_core_EphemeralContainer(in, out, s)
}

func autoConvert_core_EphemeralContainer_To_v1_EphemeralContainer(in *core.EphemeralContainer, out *corev1.EphemeralContainer, s conversion.Scope) error {
	if err := Convert_core_EphemeralContainerCommon_To_v1_EphemeralContainerCommon(&in.EphemeralContainerCommon, &out.EphemeralContainerCommon, s); err != nil {
		return err
	}
	out.TargetContainerName = in.TargetContainerName
	return nil
}

// Convert_core_EphemeralContainer_To_v1_EphemeralContainer is an autogenerated conversion function.
func Convert_core_EphemeralContainer_To_v1_EphemeralContainer(in *core.EphemeralContainer, out *corev1.EphemeralContainer, s conversion.Scope) error {
	return autoConvert_core_EphemeralContainer_To_v1_EphemeralContainer(in, out, s)
}

func autoConvert_v1_EphemeralContainerCommon_To_core_EphemeralContainerCommon(in *corev1.EphemeralContainerCommon, out *core.EphemeralContainerCommon, s conversion.Scope) error {
	out.Name = in.Name
	out.Image = in.Image
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	out.Args = *(*[]string)(unsafe.Pointer(&in.Args))
	out.WorkingDir = in.WorkingDir
	out.Ports = *(*[]core.ContainerPort)(unsafe.Pointer(&in.Ports))
	out.EnvFrom = *(*[]core.EnvFromSource)(unsafe.Pointer(&in.EnvFrom))
	out.Env = *(*[]core.EnvVar)(unsafe.Pointer(&in.Env))
	if err := Convert_v1_ResourceRequirements_To_core_ResourceRequirements(&in.Resources, &out.Resources, s); err != nil {
		return err
	}
	out.ResizePolicy = *(*[]core.ContainerResizePolicy)(unsafe.Pointer(&in.ResizePolicy))
	out.RestartPolicy = (*core.ContainerRestartPolicy)(unsafe.Pointer(in.RestartPolicy))
	out.VolumeMounts = *(*[]core.VolumeMount)(unsafe.Pointer(&in.VolumeMounts))
	out.VolumeDevices = *(*[]core.VolumeDevice)(unsafe.Pointer(&in.VolumeDevices))
	out.LivenessProbe = (*core.Probe)(unsafe.Pointer(in.LivenessProbe))
	out.ReadinessProbe = (*core.Probe)(unsafe.Pointer(in.ReadinessProbe))
	out.StartupProbe = (*core.Probe)(unsafe.Pointer(in.StartupProbe))
	out.Lifecycle = (*core.Lifecycle)(unsafe.Pointer(in.Lifecycle))
	out.TerminationMessagePath = in.TerminationMessagePath
	out.TerminationMessagePolicy = core.TerminationMessagePolicy(in.TerminationMessagePolicy)
	out.ImagePullPolicy = core.PullPolicy(in.ImagePullPolicy)
	out.SecurityContext = (*core.SecurityContext)(unsafe.Pointer(in.SecurityContext))
	out.Stdin = in.Stdin
	out.StdinOnce = in.StdinOnce
	out.TTY = in.TTY
	return nil
}

// Convert_v1_EphemeralContainerCommon_To_core_EphemeralContainerCommon is an autogenerated conversion function.
func Convert_v1_EphemeralContainerCommon_To_core_EphemeralContainerCommon(in *corev1.EphemeralContainerCommon, out *core.EphemeralContainerCommon, s conversion.Scope) error {
	return autoConvert_v1_EphemeralContainerCommon_To_core_EphemeralContainerCommon(in, out, s)
}

func autoConvert_core_EphemeralContainerCommon_To_v1_EphemeralContainerCommon(in *core.EphemeralContainerCommon, out *corev1.EphemeralContainerCommon, s conversion.Scope) error {
	out.Name = in.Name
	out.Image = in.Image
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	out.Args = *(*[]string)(unsafe.Pointer(&in.Args))
	out.WorkingDir = in.WorkingDir
	out.Ports = *(*[]corev1.ContainerPort)(unsafe.Pointer(&in.Ports))
	out.EnvFrom = *(*[]corev1.EnvFromSource)(unsafe.Pointer(&in.EnvFrom))
	out.Env = *(*[]corev1.EnvVar)(unsafe.Pointer(&in.Env))
	if err := Convert_core_ResourceRequirements_To_v1_ResourceRequirements(&in.Resources, &out.Resources, s); err != nil {
		return err
	}
	out.ResizePolicy = *(*[]corev1.ContainerResizePolicy)(unsafe.Pointer(&in.ResizePolicy))
	out.RestartPolicy = (*corev1.ContainerRestartPolicy)(unsafe.Pointer(in.RestartPolicy))
	out.VolumeMounts = *(*[]corev1.VolumeMount)(unsafe.Pointer(&in.VolumeMounts))
	out.VolumeDevices = *(*[]corev1.VolumeDevice)(unsafe.Pointer(&in.VolumeDevices))
	out.LivenessProbe = (*corev1.Probe)(unsafe.Pointer(in.LivenessProbe))
	out.ReadinessProbe = (*corev1.Probe)(unsafe.Pointer(in.ReadinessProbe))
	out.StartupProbe = (*corev1.Probe)(unsafe.Pointer(in.StartupProbe))
	out.Lifecycle = (*corev1.Lifecycle)(unsafe.Pointer(in.Lifecycle))
	out.TerminationMessagePath = in.TerminationMessagePath
	out.TerminationMessagePolicy = corev1.TerminationMessagePolicy(in.TerminationMessagePolicy)
	out.ImagePullPolicy = corev1.PullPolicy(in.ImagePullPolicy)
	out.SecurityContext = (*corev1.SecurityContext)(unsafe.Pointer(in.SecurityContext))
	out.Stdin = in.Stdin
	out.StdinOnce = in.StdinOnce
	out.TTY = in.TTY
	return nil
}

// Convert_core_EphemeralContainerCommon_To_v1_EphemeralContainerCommon is an autogenerated conversion function.
func Convert_core_EphemeralContainerCommon_To_v1_EphemeralContainerCommon(in *core.EphemeralContainerCommon, out *corev1.EphemeralContainerCommon, s conversion.Scope) error {
	return autoConvert_core_EphemeralContainerCommon_To_v1_EphemeralContainerCommon(in, out, s)
}

func autoConvert_v1_EphemeralVolumeSource_To_core_EphemeralVolumeSource(in *corev1.EphemeralVolumeSource, out *core.EphemeralVolumeSource, s conversion.Scope) error {
	out.VolumeClaimTemplate = (*core.PersistentVolumeClaimTemplate)(unsafe.Pointer(in.VolumeClaimTemplate))
	return nil
}

// Convert_v1_EphemeralVolumeSource_To_core_EphemeralVolumeSource is an autogenerated conversion function.
func Convert_v1_EphemeralVolumeSource_To_core_EphemeralVolumeSource(in *corev1.EphemeralVolumeSource, out *core.EphemeralVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_EphemeralVolumeSource_To_core_EphemeralVolumeSource(in, out, s)
}

func autoConvert_core_EphemeralVolumeSource_To_v1_EphemeralVolumeSource(in *core.EphemeralVolumeSource, out *corev1.EphemeralVolumeSource, s conversion.Scope) error {
	out.VolumeClaimTemplate = (*corev1.PersistentVolumeClaimTemplate)(unsafe.Pointer(in.VolumeClaimTemplate))
	return nil
}

// Convert_core_EphemeralVolumeSource_To_v1_EphemeralVolumeSource is an autogenerated conversion function.
func Convert_core_EphemeralVolumeSource_To_v1_EphemeralVolumeSource(in *core.EphemeralVolumeSource, out *corev1.EphemeralVolumeSource, s conversion.Scope) error {
	return autoConvert_core_EphemeralVolumeSource_To_v1_EphemeralVolumeSource(in, out, s)
}

func autoConvert_v1_Event_To_core_Event(in *corev1.Event, out *core.Event, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ObjectReference_To_core_ObjectReference(&in.InvolvedObject, &out.InvolvedObject, s); err != nil {
		return err
	}
	out.Reason = in.Reason
	out.Message = in.Message
	if err := Convert_v1_EventSource_To_core_EventSource(&in.Source, &out.Source, s); err != nil {
		return err
	}
	out.FirstTimestamp = in.FirstTimestamp
	out.LastTimestamp = in.LastTimestamp
	out.Count = in.Count
	out.Type = in.Type
	out.EventTime = in.EventTime
	out.Series = (*core.EventSeries)(unsafe.Pointer(in.Series))
	out.Action = in.Action
	out.Related = (*core.ObjectReference)(unsafe.Pointer(in.Related))
	out.ReportingController = in.ReportingController
	out.ReportingInstance = in.ReportingInstance
	return nil
}

// Convert_v1_Event_To_core_Event is an autogenerated conversion function.
func Convert_v1_Event_To_core_Event(in *corev1.Event, out *core.Event, s conversion.Scope) error {
	return autoConvert_v1_Event_To_core_Event(in, out, s)
}

func autoConvert_core_Event_To_v1_Event(in *core.Event, out *corev1.Event, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_ObjectReference_To_v1_ObjectReference(&in.InvolvedObject, &out.InvolvedObject, s); err != nil {
		return err
	}
	out.Reason = in.Reason
	out.Message = in.Message
	if err := Convert_core_EventSource_To_v1_EventSource(&in.Source, &out.Source, s); err != nil {
		return err
	}
	out.FirstTimestamp = in.FirstTimestamp
	out.LastTimestamp = in.LastTimestamp
	out.Count = in.Count
	out.Type = in.Type
	out.EventTime = in.EventTime
	out.Series = (*corev1.EventSeries)(unsafe.Pointer(in.Series))
	out.Action = in.Action
	out.Related = (*corev1.ObjectReference)(unsafe.Pointer(in.Related))
	out.ReportingController = in.ReportingController
	out.ReportingInstance = in.ReportingInstance
	return nil
}

// Convert_core_Event_To_v1_Event is an autogenerated conversion function.
func Convert_core_Event_To_v1_Event(in *core.Event, out *corev1.Event, s conversion.Scope) error {
	return autoConvert_core_Event_To_v1_Event(in, out, s)
}

func autoConvert_v1_EventList_To_core_EventList(in *corev1.EventList, out *core.EventList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.Event)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_EventList_To_core_EventList is an autogenerated conversion function.
func Convert_v1_EventList_To_core_EventList(in *corev1.EventList, out *core.EventList, s conversion.Scope) error {
	return autoConvert_v1_EventList_To_core_EventList(in, out, s)
}

func autoConvert_core_EventList_To_v1_EventList(in *core.EventList, out *corev1.EventList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.Event)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_EventList_To_v1_EventList is an autogenerated conversion function.
func Convert_core_EventList_To_v1_EventList(in *core.EventList, out *corev1.EventList, s conversion.Scope) error {
	return autoConvert_core_EventList_To_v1_EventList(in, out, s)
}

func autoConvert_v1_EventSeries_To_core_EventSeries(in *corev1.EventSeries, out *core.EventSeries, s conversion.Scope) error {
	out.Count = in.Count
	out.LastObservedTime = in.LastObservedTime
	return nil
}

// Convert_v1_EventSeries_To_core_EventSeries is an autogenerated conversion function.
func Convert_v1_EventSeries_To_core_EventSeries(in *corev1.EventSeries, out *core.EventSeries, s conversion.Scope) error {
	return autoConvert_v1_EventSeries_To_core_EventSeries(in, out, s)
}

func autoConvert_core_EventSeries_To_v1_EventSeries(in *core.EventSeries, out *corev1.EventSeries, s conversion.Scope) error {
	out.Count = in.Count
	out.LastObservedTime = in.LastObservedTime
	return nil
}

// Convert_core_EventSeries_To_v1_EventSeries is an autogenerated conversion function.
func Convert_core_EventSeries_To_v1_EventSeries(in *core.EventSeries, out *corev1.EventSeries, s conversion.Scope) error {
	return autoConvert_core_EventSeries_To_v1_EventSeries(in, out, s)
}

func autoConvert_v1_EventSource_To_core_EventSource(in *corev1.EventSource, out *core.EventSource, s conversion.Scope) error {
	out.Component = in.Component
	out.Host = in.Host
	return nil
}

// Convert_v1_EventSource_To_core_EventSource is an autogenerated conversion function.
func Convert_v1_EventSource_To_core_EventSource(in *corev1.EventSource, out *core.EventSource, s conversion.Scope) error {
	return autoConvert_v1_EventSource_To_core_EventSource(in, out, s)
}

func autoConvert_core_EventSource_To_v1_EventSource(in *core.EventSource, out *corev1.EventSource, s conversion.Scope) error {
	out.Component = in.Component
	out.Host = in.Host
	return nil
}

// Convert_core_EventSource_To_v1_EventSource is an autogenerated conversion function.
func Convert_core_EventSource_To_v1_EventSource(in *core.EventSource, out *corev1.EventSource, s conversion.Scope) error {
	return autoConvert_core_EventSource_To_v1_EventSource(in, out, s)
}

func autoConvert_v1_ExecAction_To_core_ExecAction(in *corev1.ExecAction, out *core.ExecAction, s conversion.Scope) error {
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	return nil
}

// Convert_v1_ExecAction_To_core_ExecAction is an autogenerated conversion function.
func Convert_v1_ExecAction_To_core_ExecAction(in *corev1.ExecAction, out *core.ExecAction, s conversion.Scope) error {
	return autoConvert_v1_ExecAction_To_core_ExecAction(in, out, s)
}

func autoConvert_core_ExecAction_To_v1_ExecAction(in *core.ExecAction, out *corev1.ExecAction, s conversion.Scope) error {
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	return nil
}

// Convert_core_ExecAction_To_v1_ExecAction is an autogenerated conversion function.
func Convert_core_ExecAction_To_v1_ExecAction(in *core.ExecAction, out *corev1.ExecAction, s conversion.Scope) error {
	return autoConvert_core_ExecAction_To_v1_ExecAction(in, out, s)
}

func autoConvert_v1_FCVolumeSource_To_core_FCVolumeSource(in *corev1.FCVolumeSource, out *core.FCVolumeSource, s conversion.Scope) error {
	out.TargetWWNs = *(*[]string)(unsafe.Pointer(&in.TargetWWNs))
	out.Lun = (*int32)(unsafe.Pointer(in.Lun))
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.WWIDs = *(*[]string)(unsafe.Pointer(&in.WWIDs))
	return nil
}

// Convert_v1_FCVolumeSource_To_core_FCVolumeSource is an autogenerated conversion function.
func Convert_v1_FCVolumeSource_To_core_FCVolumeSource(in *corev1.FCVolumeSource, out *core.FCVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_FCVolumeSource_To_core_FCVolumeSource(in, out, s)
}

func autoConvert_core_FCVolumeSource_To_v1_FCVolumeSource(in *core.FCVolumeSource, out *corev1.FCVolumeSource, s conversion.Scope) error {
	out.TargetWWNs = *(*[]string)(unsafe.Pointer(&in.TargetWWNs))
	out.Lun = (*int32)(unsafe.Pointer(in.Lun))
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.WWIDs = *(*[]string)(unsafe.Pointer(&in.WWIDs))
	return nil
}

// Convert_core_FCVolumeSource_To_v1_FCVolumeSource is an autogenerated conversion function.
func Convert_core_FCVolumeSource_To_v1_FCVolumeSource(in *core.FCVolumeSource, out *corev1.FCVolumeSource, s conversion.Scope) error {
	return autoConvert_core_FCVolumeSource_To_v1_FCVolumeSource(in, out, s)
}

func autoConvert_v1_FileKeySelector_To_core_FileKeySelector(in *corev1.FileKeySelector, out *core.FileKeySelector, s conversion.Scope) error {
	out.VolumeName = in.VolumeName
	out.Path = in.Path
	out.Key = in.Key
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_FileKeySelector_To_core_FileKeySelector is an autogenerated conversion function.
func Convert_v1_FileKeySelector_To_core_FileKeySelector(in *corev1.FileKeySelector, out *core.FileKeySelector, s conversion.Scope) error {
	return autoConvert_v1_FileKeySelector_To_core_FileKeySelector(in, out, s)
}

func autoConvert_core_FileKeySelector_To_v1_FileKeySelector(in *core.FileKeySelector, out *corev1.FileKeySelector, s conversion.Scope) error {
	out.VolumeName = in.VolumeName
	out.Path = in.Path
	out.Key = in.Key
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_FileKeySelector_To_v1_FileKeySelector is an autogenerated conversion function.
func Convert_core_FileKeySelector_To_v1_FileKeySelector(in *core.FileKeySelector, out *corev1.FileKeySelector, s conversion.Scope) error {
	return autoConvert_core_FileKeySelector_To_v1_FileKeySelector(in, out, s)
}

func autoConvert_v1_FlexPersistentVolumeSource_To_core_FlexPersistentVolumeSource(in *corev1.FlexPersistentVolumeSource, out *core.FlexPersistentVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.FSType = in.FSType
	out.SecretRef = (*core.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	out.Options = *(*map[string]string)(unsafe.Pointer(&in.Options))
	return nil
}

// Convert_v1_FlexPersistentVolumeSource_To_core_FlexPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_FlexPersistentVolumeSource_To_core_FlexPersistentVolumeSource(in *corev1.FlexPersistentVolumeSource, out *core.FlexPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_FlexPersistentVolumeSource_To_core_FlexPersistentVolumeSource(in, out, s)
}

func autoConvert_core_FlexPersistentVolumeSource_To_v1_FlexPersistentVolumeSource(in *core.FlexPersistentVolumeSource, out *corev1.FlexPersistentVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.FSType = in.FSType
	out.SecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	out.Options = *(*map[string]string)(unsafe.Pointer(&in.Options))
	return nil
}

// Convert_core_FlexPersistentVolumeSource_To_v1_FlexPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_FlexPersistentVolumeSource_To_v1_FlexPersistentVolumeSource(in *core.FlexPersistentVolumeSource, out *corev1.FlexPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_FlexPersistentVolumeSource_To_v1_FlexPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_FlexVolumeSource_To_core_FlexVolumeSource(in *corev1.FlexVolumeSource, out *core.FlexVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.FSType = in.FSType
	out.SecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	out.Options = *(*map[string]string)(unsafe.Pointer(&in.Options))
	return nil
}

// Convert_v1_FlexVolumeSource_To_core_FlexVolumeSource is an autogenerated conversion function.
func Convert_v1_FlexVolumeSource_To_core_FlexVolumeSource(in *corev1.FlexVolumeSource, out *core.FlexVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_FlexVolumeSource_To_core_FlexVolumeSource(in, out, s)
}

func autoConvert_core_FlexVolumeSource_To_v1_FlexVolumeSource(in *core.FlexVolumeSource, out *corev1.FlexVolumeSource, s conversion.Scope) error {
	out.Driver = in.Driver
	out.FSType = in.FSType
	out.SecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	out.Options = *(*map[string]string)(unsafe.Pointer(&in.Options))
	return nil
}

// Convert_core_FlexVolumeSource_To_v1_FlexVolumeSource is an autogenerated conversion function.
func Convert_core_FlexVolumeSource_To_v1_FlexVolumeSource(in *core.FlexVolumeSource, out *corev1.FlexVolumeSource, s conversion.Scope) error {
	return autoConvert_core_FlexVolumeSource_To_v1_FlexVolumeSource(in, out, s)
}

func autoConvert_v1_FlockerVolumeSource_To_core_FlockerVolumeSource(in *corev1.FlockerVolumeSource, out *core.FlockerVolumeSource, s conversion.Scope) error {
	out.DatasetName = in.DatasetName
	out.DatasetUUID = in.DatasetUUID
	return nil
}

// Convert_v1_FlockerVolumeSource_To_core_FlockerVolumeSource is an autogenerated conversion function.
func Convert_v1_FlockerVolumeSource_To_core_FlockerVolumeSource(in *corev1.FlockerVolumeSource, out *core.FlockerVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_FlockerVolumeSource_To_core_FlockerVolumeSource(in, out, s)
}

func autoConvert_core_FlockerVolumeSource_To_v1_FlockerVolumeSource(in *core.FlockerVolumeSource, out *corev1.FlockerVolumeSource, s conversion.Scope) error {
	out.DatasetName = in.DatasetName
	out.DatasetUUID = in.DatasetUUID
	return nil
}

// Convert_core_FlockerVolumeSource_To_v1_FlockerVolumeSource is an autogenerated conversion function.
func Convert_core_FlockerVolumeSource_To_v1_FlockerVolumeSource(in *core.FlockerVolumeSource, out *corev1.FlockerVolumeSource, s conversion.Scope) error {
	return autoConvert_core_FlockerVolumeSource_To_v1_FlockerVolumeSource(in, out, s)
}

func autoConvert_v1_GCEPersistentDiskVolumeSource_To_core_GCEPersistentDiskVolumeSource(in *corev1.GCEPersistentDiskVolumeSource, out *core.GCEPersistentDiskVolumeSource, s conversion.Scope) error {
	out.PDName = in.PDName
	out.FSType = in.FSType
	out.Partition = in.Partition
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_GCEPersistentDiskVolumeSource_To_core_GCEPersistentDiskVolumeSource is an autogenerated conversion function.
func Convert_v1_GCEPersistentDiskVolumeSource_To_core_GCEPersistentDiskVolumeSource(in *corev1.GCEPersistentDiskVolumeSource, out *core.GCEPersistentDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_GCEPersistentDiskVolumeSource_To_core_GCEPersistentDiskVolumeSource(in, out, s)
}

func autoConvert_core_GCEPersistentDiskVolumeSource_To_v1_GCEPersistentDiskVolumeSource(in *core.GCEPersistentDiskVolumeSource, out *corev1.GCEPersistentDiskVolumeSource, s conversion.Scope) error {
	out.PDName = in.PDName
	out.FSType = in.FSType
	out.Partition = in.Partition
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_GCEPersistentDiskVolumeSource_To_v1_GCEPersistentDiskVolumeSource is an autogenerated conversion function.
func Convert_core_GCEPersistentDiskVolumeSource_To_v1_GCEPersistentDiskVolumeSource(in *core.GCEPersistentDiskVolumeSource, out *corev1.GCEPersistentDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_core_GCEPersistentDiskVolumeSource_To_v1_GCEPersistentDiskVolumeSource(in, out, s)
}

func autoConvert_v1_GRPCAction_To_core_GRPCAction(in *corev1.GRPCAction, out *core.GRPCAction, s conversion.Scope) error {
	out.Port = in.Port
	out.Service = (*string)(unsafe.Pointer(in.Service))
	return nil
}

// Convert_v1_GRPCAction_To_core_GRPCAction is an autogenerated conversion function.
func Convert_v1_GRPCAction_To_core_GRPCAction(in *corev1.GRPCAction, out *core.GRPCAction, s conversion.Scope) error {
	return autoConvert_v1_GRPCAction_To_core_GRPCAction(in, out, s)
}

func autoConvert_core_GRPCAction_To_v1_GRPCAction(in *core.GRPCAction, out *corev1.GRPCAction, s conversion.Scope) error {
	out.Port = in.Port
	out.Service = (*string)(unsafe.Pointer(in.Service))
	return nil
}

// Convert_core_GRPCAction_To_v1_GRPCAction is an autogenerated conversion function.
func Convert_core_GRPCAction_To_v1_GRPCAction(in *core.GRPCAction, out *corev1.GRPCAction, s conversion.Scope) error {
	return autoConvert_core_GRPCAction_To_v1_GRPCAction(in, out, s)
}

func autoConvert_v1_GitRepoVolumeSource_To_core_GitRepoVolumeSource(in *corev1.GitRepoVolumeSource, out *core.GitRepoVolumeSource, s conversion.Scope) error {
	out.Repository = in.Repository
	out.Revision = in.Revision
	out.Directory = in.Directory
	return nil
}

// Convert_v1_GitRepoVolumeSource_To_core_GitRepoVolumeSource is an autogenerated conversion function.
func Convert_v1_GitRepoVolumeSource_To_core_GitRepoVolumeSource(in *corev1.GitRepoVolumeSource, out *core.GitRepoVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_GitRepoVolumeSource_To_core_GitRepoVolumeSource(in, out, s)
}

func autoConvert_core_GitRepoVolumeSource_To_v1_GitRepoVolumeSource(in *core.GitRepoVolumeSource, out *corev1.GitRepoVolumeSource, s conversion.Scope) error {
	out.Repository = in.Repository
	out.Revision = in.Revision
	out.Directory = in.Directory
	return nil
}

// Convert_core_GitRepoVolumeSource_To_v1_GitRepoVolumeSource is an autogenerated conversion function.
func Convert_core_GitRepoVolumeSource_To_v1_GitRepoVolumeSource(in *core.GitRepoVolumeSource, out *corev1.GitRepoVolumeSource, s conversion.Scope) error {
	return autoConvert_core_GitRepoVolumeSource_To_v1_GitRepoVolumeSource(in, out, s)
}

func autoConvert_v1_GlusterfsPersistentVolumeSource_To_core_GlusterfsPersistentVolumeSource(in *corev1.GlusterfsPersistentVolumeSource, out *core.GlusterfsPersistentVolumeSource, s conversion.Scope) error {
	out.EndpointsName = in.EndpointsName
	out.Path = in.Path
	out.ReadOnly = in.ReadOnly
	out.EndpointsNamespace = (*string)(unsafe.Pointer(in.EndpointsNamespace))
	return nil
}

// Convert_v1_GlusterfsPersistentVolumeSource_To_core_GlusterfsPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_GlusterfsPersistentVolumeSource_To_core_GlusterfsPersistentVolumeSource(in *corev1.GlusterfsPersistentVolumeSource, out *core.GlusterfsPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_GlusterfsPersistentVolumeSource_To_core_GlusterfsPersistentVolumeSource(in, out, s)
}

func autoConvert_core_GlusterfsPersistentVolumeSource_To_v1_GlusterfsPersistentVolumeSource(in *core.GlusterfsPersistentVolumeSource, out *corev1.GlusterfsPersistentVolumeSource, s conversion.Scope) error {
	out.EndpointsName = in.EndpointsName
	out.Path = in.Path
	out.ReadOnly = in.ReadOnly
	out.EndpointsNamespace = (*string)(unsafe.Pointer(in.EndpointsNamespace))
	return nil
}

// Convert_core_GlusterfsPersistentVolumeSource_To_v1_GlusterfsPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_GlusterfsPersistentVolumeSource_To_v1_GlusterfsPersistentVolumeSource(in *core.GlusterfsPersistentVolumeSource, out *corev1.GlusterfsPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_GlusterfsPersistentVolumeSource_To_v1_GlusterfsPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_GlusterfsVolumeSource_To_core_GlusterfsVolumeSource(in *corev1.GlusterfsVolumeSource, out *core.GlusterfsVolumeSource, s conversion.Scope) error {
	out.EndpointsName = in.EndpointsName
	out.Path = in.Path
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_GlusterfsVolumeSource_To_core_GlusterfsVolumeSource is an autogenerated conversion function.
func Convert_v1_GlusterfsVolumeSource_To_core_GlusterfsVolumeSource(in *corev1.GlusterfsVolumeSource, out *core.GlusterfsVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_GlusterfsVolumeSource_To_core_GlusterfsVolumeSource(in, out, s)
}

func autoConvert_core_GlusterfsVolumeSource_To_v1_GlusterfsVolumeSource(in *core.GlusterfsVolumeSource, out *corev1.GlusterfsVolumeSource, s conversion.Scope) error {
	out.EndpointsName = in.EndpointsName
	out.Path = in.Path
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_GlusterfsVolumeSource_To_v1_GlusterfsVolumeSource is an autogenerated conversion function.
func Convert_core_GlusterfsVolumeSource_To_v1_GlusterfsVolumeSource(in *core.GlusterfsVolumeSource, out *corev1.GlusterfsVolumeSource, s conversion.Scope) error {
	return autoConvert_core_GlusterfsVolumeSource_To_v1_GlusterfsVolumeSource(in, out, s)
}

func autoConvert_v1_HTTPGetAction_To_core_HTTPGetAction(in *corev1.HTTPGetAction, out *core.HTTPGetAction, s conversion.Scope) error {
	out.Path = in.Path
	out.Port = in.Port
	out.Host = in.Host
	out.Scheme = core.URIScheme(in.Scheme)
	out.HTTPHeaders = *(*[]core.HTTPHeader)(unsafe.Pointer(&in.HTTPHeaders))
	return nil
}

// Convert_v1_HTTPGetAction_To_core_HTTPGetAction is an autogenerated conversion function.
func Convert_v1_HTTPGetAction_To_core_HTTPGetAction(in *corev1.HTTPGetAction, out *core.HTTPGetAction, s conversion.Scope) error {
	return autoConvert_v1_HTTPGetAction_To_core_HTTPGetAction(in, out, s)
}

func autoConvert_core_HTTPGetAction_To_v1_HTTPGetAction(in *core.HTTPGetAction, out *corev1.HTTPGetAction, s conversion.Scope) error {
	out.Path = in.Path
	out.Port = in.Port
	out.Host = in.Host
	out.Scheme = corev1.URIScheme(in.Scheme)
	out.HTTPHeaders = *(*[]corev1.HTTPHeader)(unsafe.Pointer(&in.HTTPHeaders))
	return nil
}

// Convert_core_HTTPGetAction_To_v1_HTTPGetAction is an autogenerated conversion function.
func Convert_core_HTTPGetAction_To_v1_HTTPGetAction(in *core.HTTPGetAction, out *corev1.HTTPGetAction, s conversion.Scope) error {
	return autoConvert_core_HTTPGetAction_To_v1_HTTPGetAction(in, out, s)
}

func autoConvert_v1_HTTPHeader_To_core_HTTPHeader(in *corev1.HTTPHeader, out *core.HTTPHeader, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = in.Value
	return nil
}

// Convert_v1_HTTPHeader_To_core_HTTPHeader is an autogenerated conversion function.
func Convert_v1_HTTPHeader_To_core_HTTPHeader(in *corev1.HTTPHeader, out *core.HTTPHeader, s conversion.Scope) error {
	return autoConvert_v1_HTTPHeader_To_core_HTTPHeader(in, out, s)
}

func autoConvert_core_HTTPHeader_To_v1_HTTPHeader(in *core.HTTPHeader, out *corev1.HTTPHeader, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = in.Value
	return nil
}

// Convert_core_HTTPHeader_To_v1_HTTPHeader is an autogenerated conversion function.
func Convert_core_HTTPHeader_To_v1_HTTPHeader(in *core.HTTPHeader, out *corev1.HTTPHeader, s conversion.Scope) error {
	return autoConvert_core_HTTPHeader_To_v1_HTTPHeader(in, out, s)
}

func autoConvert_v1_HostAlias_To_core_HostAlias(in *corev1.HostAlias, out *core.HostAlias, s conversion.Scope) error {
	out.IP = in.IP
	out.Hostnames = *(*[]string)(unsafe.Pointer(&in.Hostnames))
	return nil
}

// Convert_v1_HostAlias_To_core_HostAlias is an autogenerated conversion function.
func Convert_v1_HostAlias_To_core_HostAlias(in *corev1.HostAlias, out *core.HostAlias, s conversion.Scope) error {
	return autoConvert_v1_HostAlias_To_core_HostAlias(in, out, s)
}

func autoConvert_core_HostAlias_To_v1_HostAlias(in *core.HostAlias, out *corev1.HostAlias, s conversion.Scope) error {
	out.IP = in.IP
	out.Hostnames = *(*[]string)(unsafe.Pointer(&in.Hostnames))
	return nil
}

// Convert_core_HostAlias_To_v1_HostAlias is an autogenerated conversion function.
func Convert_core_HostAlias_To_v1_HostAlias(in *core.HostAlias, out *corev1.HostAlias, s conversion.Scope) error {
	return autoConvert_core_HostAlias_To_v1_HostAlias(in, out, s)
}

func autoConvert_v1_HostIP_To_core_HostIP(in *corev1.HostIP, out *core.HostIP, s conversion.Scope) error {
	out.IP = in.IP
	return nil
}

// Convert_v1_HostIP_To_core_HostIP is an autogenerated conversion function.
func Convert_v1_HostIP_To_core_HostIP(in *corev1.HostIP, out *core.HostIP, s conversion.Scope) error {
	return autoConvert_v1_HostIP_To_core_HostIP(in, out, s)
}

func autoConvert_core_HostIP_To_v1_HostIP(in *core.HostIP, out *corev1.HostIP, s conversion.Scope) error {
	out.IP = in.IP
	return nil
}

// Convert_core_HostIP_To_v1_HostIP is an autogenerated conversion function.
func Convert_core_HostIP_To_v1_HostIP(in *core.HostIP, out *corev1.HostIP, s conversion.Scope) error {
	return autoConvert_core_HostIP_To_v1_HostIP(in, out, s)
}

func autoConvert_v1_HostPathVolumeSource_To_core_HostPathVolumeSource(in *corev1.HostPathVolumeSource, out *core.HostPathVolumeSource, s conversion.Scope) error {
	out.Path = in.Path
	out.Type = (*core.HostPathType)(unsafe.Pointer(in.Type))
	return nil
}

// Convert_v1_HostPathVolumeSource_To_core_HostPathVolumeSource is an autogenerated conversion function.
func Convert_v1_HostPathVolumeSource_To_core_HostPathVolumeSource(in *corev1.HostPathVolumeSource, out *core.HostPathVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_HostPathVolumeSource_To_core_HostPathVolumeSource(in, out, s)
}

func autoConvert_core_HostPathVolumeSource_To_v1_HostPathVolumeSource(in *core.HostPathVolumeSource, out *corev1.HostPathVolumeSource, s conversion.Scope) error {
	out.Path = in.Path
	out.Type = (*corev1.HostPathType)(unsafe.Pointer(in.Type))
	return nil
}

// Convert_core_HostPathVolumeSource_To_v1_HostPathVolumeSource is an autogenerated conversion function.
func Convert_core_HostPathVolumeSource_To_v1_HostPathVolumeSource(in *core.HostPathVolumeSource, out *corev1.HostPathVolumeSource, s conversion.Scope) error {
	return autoConvert_core_HostPathVolumeSource_To_v1_HostPathVolumeSource(in, out, s)
}

func autoConvert_v1_ISCSIPersistentVolumeSource_To_core_ISCSIPersistentVolumeSource(in *corev1.ISCSIPersistentVolumeSource, out *core.ISCSIPersistentVolumeSource, s conversion.Scope) error {
	out.TargetPortal = in.TargetPortal
	out.IQN = in.IQN
	out.Lun = in.Lun
	out.ISCSIInterface = in.ISCSIInterface
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.Portals = *(*[]string)(unsafe.Pointer(&in.Portals))
	out.DiscoveryCHAPAuth = in.DiscoveryCHAPAuth
	out.SessionCHAPAuth = in.SessionCHAPAuth
	out.SecretRef = (*core.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.InitiatorName = (*string)(unsafe.Pointer(in.InitiatorName))
	return nil
}

// Convert_v1_ISCSIPersistentVolumeSource_To_core_ISCSIPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_ISCSIPersistentVolumeSource_To_core_ISCSIPersistentVolumeSource(in *corev1.ISCSIPersistentVolumeSource, out *core.ISCSIPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_ISCSIPersistentVolumeSource_To_core_ISCSIPersistentVolumeSource(in, out, s)
}

func autoConvert_core_ISCSIPersistentVolumeSource_To_v1_ISCSIPersistentVolumeSource(in *core.ISCSIPersistentVolumeSource, out *corev1.ISCSIPersistentVolumeSource, s conversion.Scope) error {
	out.TargetPortal = in.TargetPortal
	out.IQN = in.IQN
	out.Lun = in.Lun
	out.ISCSIInterface = in.ISCSIInterface
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.Portals = *(*[]string)(unsafe.Pointer(&in.Portals))
	out.DiscoveryCHAPAuth = in.DiscoveryCHAPAuth
	out.SessionCHAPAuth = in.SessionCHAPAuth
	out.SecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.InitiatorName = (*string)(unsafe.Pointer(in.InitiatorName))
	return nil
}

// Convert_core_ISCSIPersistentVolumeSource_To_v1_ISCSIPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_ISCSIPersistentVolumeSource_To_v1_ISCSIPersistentVolumeSource(in *core.ISCSIPersistentVolumeSource, out *corev1.ISCSIPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_ISCSIPersistentVolumeSource_To_v1_ISCSIPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_ISCSIVolumeSource_To_core_ISCSIVolumeSource(in *corev1.ISCSIVolumeSource, out *core.ISCSIVolumeSource, s conversion.Scope) error {
	out.TargetPortal = in.TargetPortal
	out.IQN = in.IQN
	out.Lun = in.Lun
	out.ISCSIInterface = in.ISCSIInterface
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.Portals = *(*[]string)(unsafe.Pointer(&in.Portals))
	out.DiscoveryCHAPAuth = in.DiscoveryCHAPAuth
	out.SessionCHAPAuth = in.SessionCHAPAuth
	out.SecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.InitiatorName = (*string)(unsafe.Pointer(in.InitiatorName))
	return nil
}

// Convert_v1_ISCSIVolumeSource_To_core_ISCSIVolumeSource is an autogenerated conversion function.
func Convert_v1_ISCSIVolumeSource_To_core_ISCSIVolumeSource(in *corev1.ISCSIVolumeSource, out *core.ISCSIVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_ISCSIVolumeSource_To_core_ISCSIVolumeSource(in, out, s)
}

func autoConvert_core_ISCSIVolumeSource_To_v1_ISCSIVolumeSource(in *core.ISCSIVolumeSource, out *corev1.ISCSIVolumeSource, s conversion.Scope) error {
	out.TargetPortal = in.TargetPortal
	out.IQN = in.IQN
	out.Lun = in.Lun
	out.ISCSIInterface = in.ISCSIInterface
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.Portals = *(*[]string)(unsafe.Pointer(&in.Portals))
	out.DiscoveryCHAPAuth = in.DiscoveryCHAPAuth
	out.SessionCHAPAuth = in.SessionCHAPAuth
	out.SecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.InitiatorName = (*string)(unsafe.Pointer(in.InitiatorName))
	return nil
}

// Convert_core_ISCSIVolumeSource_To_v1_ISCSIVolumeSource is an autogenerated conversion function.
func Convert_core_ISCSIVolumeSource_To_v1_ISCSIVolumeSource(in *core.ISCSIVolumeSource, out *corev1.ISCSIVolumeSource, s conversion.Scope) error {
	return autoConvert_core_ISCSIVolumeSource_To_v1_ISCSIVolumeSource(in, out, s)
}

func autoConvert_v1_ImageVolumeSource_To_core_ImageVolumeSource(in *corev1.ImageVolumeSource, out *core.ImageVolumeSource, s conversion.Scope) error {
	out.Reference = in.Reference
	out.PullPolicy = core.PullPolicy(in.PullPolicy)
	return nil
}

// Convert_v1_ImageVolumeSource_To_core_ImageVolumeSource is an autogenerated conversion function.
func Convert_v1_ImageVolumeSource_To_core_ImageVolumeSource(in *corev1.ImageVolumeSource, out *core.ImageVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_ImageVolumeSource_To_core_ImageVolumeSource(in, out, s)
}

func autoConvert_core_ImageVolumeSource_To_v1_ImageVolumeSource(in *core.ImageVolumeSource, out *corev1.ImageVolumeSource, s conversion.Scope) error {
	out.Reference = in.Reference
	out.PullPolicy = corev1.PullPolicy(in.PullPolicy)
	return nil
}

// Convert_core_ImageVolumeSource_To_v1_ImageVolumeSource is an autogenerated conversion function.
func Convert_core_ImageVolumeSource_To_v1_ImageVolumeSource(in *core.ImageVolumeSource, out *corev1.ImageVolumeSource, s conversion.Scope) error {
	return autoConvert_core_ImageVolumeSource_To_v1_ImageVolumeSource(in, out, s)
}

func autoConvert_v1_KeyToPath_To_core_KeyToPath(in *corev1.KeyToPath, out *core.KeyToPath, s conversion.Scope) error {
	out.Key = in.Key
	out.Path = in.Path
	out.Mode = (*int32)(unsafe.Pointer(in.Mode))
	return nil
}

// Convert_v1_KeyToPath_To_core_KeyToPath is an autogenerated conversion function.
func Convert_v1_KeyToPath_To_core_KeyToPath(in *corev1.KeyToPath, out *core.KeyToPath, s conversion.Scope) error {
	return autoConvert_v1_KeyToPath_To_core_KeyToPath(in, out, s)
}

func autoConvert_core_KeyToPath_To_v1_KeyToPath(in *core.KeyToPath, out *corev1.KeyToPath, s conversion.Scope) error {
	out.Key = in.Key
	out.Path = in.Path
	out.Mode = (*int32)(unsafe.Pointer(in.Mode))
	return nil
}

// Convert_core_KeyToPath_To_v1_KeyToPath is an autogenerated conversion function.
func Convert_core_KeyToPath_To_v1_KeyToPath(in *core.KeyToPath, out *corev1.KeyToPath, s conversion.Scope) error {
	return autoConvert_core_KeyToPath_To_v1_KeyToPath(in, out, s)
}

func autoConvert_v1_Lifecycle_To_core_Lifecycle(in *corev1.Lifecycle, out *core.Lifecycle, s conversion.Scope) error {
	out.PostStart = (*core.LifecycleHandler)(unsafe.Pointer(in.PostStart))
	out.PreStop = (*core.LifecycleHandler)(unsafe.Pointer(in.PreStop))
	out.StopSignal = (*core.Signal)(unsafe.Pointer(in.StopSignal))
	return nil
}

// Convert_v1_Lifecycle_To_core_Lifecycle is an autogenerated conversion function.
func Convert_v1_Lifecycle_To_core_Lifecycle(in *corev1.Lifecycle, out *core.Lifecycle, s conversion.Scope) error {
	return autoConvert_v1_Lifecycle_To_core_Lifecycle(in, out, s)
}

func autoConvert_core_Lifecycle_To_v1_Lifecycle(in *core.Lifecycle, out *corev1.Lifecycle, s conversion.Scope) error {
	out.PostStart = (*corev1.LifecycleHandler)(unsafe.Pointer(in.PostStart))
	out.PreStop = (*corev1.LifecycleHandler)(unsafe.Pointer(in.PreStop))
	out.StopSignal = (*corev1.Signal)(unsafe.Pointer(in.StopSignal))
	return nil
}

// Convert_core_Lifecycle_To_v1_Lifecycle is an autogenerated conversion function.
func Convert_core_Lifecycle_To_v1_Lifecycle(in *core.Lifecycle, out *corev1.Lifecycle, s conversion.Scope) error {
	return autoConvert_core_Lifecycle_To_v1_Lifecycle(in, out, s)
}

func autoConvert_v1_LifecycleHandler_To_core_LifecycleHandler(in *corev1.LifecycleHandler, out *core.LifecycleHandler, s conversion.Scope) error {
	out.Exec = (*core.ExecAction)(unsafe.Pointer(in.Exec))
	out.HTTPGet = (*core.HTTPGetAction)(unsafe.Pointer(in.HTTPGet))
	out.TCPSocket = (*core.TCPSocketAction)(unsafe.Pointer(in.TCPSocket))
	out.Sleep = (*core.SleepAction)(unsafe.Pointer(in.Sleep))
	return nil
}

// Convert_v1_LifecycleHandler_To_core_LifecycleHandler is an autogenerated conversion function.
func Convert_v1_LifecycleHandler_To_core_LifecycleHandler(in *corev1.LifecycleHandler, out *core.LifecycleHandler, s conversion.Scope) error {
	return autoConvert_v1_LifecycleHandler_To_core_LifecycleHandler(in, out, s)
}

func autoConvert_core_LifecycleHandler_To_v1_LifecycleHandler(in *core.LifecycleHandler, out *corev1.LifecycleHandler, s conversion.Scope) error {
	out.Exec = (*corev1.ExecAction)(unsafe.Pointer(in.Exec))
	out.HTTPGet = (*corev1.HTTPGetAction)(unsafe.Pointer(in.HTTPGet))
	out.TCPSocket = (*corev1.TCPSocketAction)(unsafe.Pointer(in.TCPSocket))
	out.Sleep = (*corev1.SleepAction)(unsafe.Pointer(in.Sleep))
	return nil
}

// Convert_core_LifecycleHandler_To_v1_LifecycleHandler is an autogenerated conversion function.
func Convert_core_LifecycleHandler_To_v1_LifecycleHandler(in *core.LifecycleHandler, out *corev1.LifecycleHandler, s conversion.Scope) error {
	return autoConvert_core_LifecycleHandler_To_v1_LifecycleHandler(in, out, s)
}

func autoConvert_v1_LimitRange_To_core_LimitRange(in *corev1.LimitRange, out *core.LimitRange, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_LimitRangeSpec_To_core_LimitRangeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_LimitRange_To_core_LimitRange is an autogenerated conversion function.
func Convert_v1_LimitRange_To_core_LimitRange(in *corev1.LimitRange, out *core.LimitRange, s conversion.Scope) error {
	return autoConvert_v1_LimitRange_To_core_LimitRange(in, out, s)
}

func autoConvert_core_LimitRange_To_v1_LimitRange(in *core.LimitRange, out *corev1.LimitRange, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_LimitRangeSpec_To_v1_LimitRangeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_LimitRange_To_v1_LimitRange is an autogenerated conversion function.
func Convert_core_LimitRange_To_v1_LimitRange(in *core.LimitRange, out *corev1.LimitRange, s conversion.Scope) error {
	return autoConvert_core_LimitRange_To_v1_LimitRange(in, out, s)
}

func autoConvert_v1_LimitRangeItem_To_core_LimitRangeItem(in *corev1.LimitRangeItem, out *core.LimitRangeItem, s conversion.Scope) error {
	out.Type = core.LimitType(in.Type)
	out.Max = *(*core.ResourceList)(unsafe.Pointer(&in.Max))
	out.Min = *(*core.ResourceList)(unsafe.Pointer(&in.Min))
	out.Default = *(*core.ResourceList)(unsafe.Pointer(&in.Default))
	out.DefaultRequest = *(*core.ResourceList)(unsafe.Pointer(&in.DefaultRequest))
	out.MaxLimitRequestRatio = *(*core.ResourceList)(unsafe.Pointer(&in.MaxLimitRequestRatio))
	return nil
}

// Convert_v1_LimitRangeItem_To_core_LimitRangeItem is an autogenerated conversion function.
func Convert_v1_LimitRangeItem_To_core_LimitRangeItem(in *corev1.LimitRangeItem, out *core.LimitRangeItem, s conversion.Scope) error {
	return autoConvert_v1_LimitRangeItem_To_core_LimitRangeItem(in, out, s)
}

func autoConvert_core_LimitRangeItem_To_v1_LimitRangeItem(in *core.LimitRangeItem, out *corev1.LimitRangeItem, s conversion.Scope) error {
	out.Type = corev1.LimitType(in.Type)
	out.Max = *(*corev1.ResourceList)(unsafe.Pointer(&in.Max))
	out.Min = *(*corev1.ResourceList)(unsafe.Pointer(&in.Min))
	out.Default = *(*corev1.ResourceList)(unsafe.Pointer(&in.Default))
	out.DefaultRequest = *(*corev1.ResourceList)(unsafe.Pointer(&in.DefaultRequest))
	out.MaxLimitRequestRatio = *(*corev1.ResourceList)(unsafe.Pointer(&in.MaxLimitRequestRatio))
	return nil
}

// Convert_core_LimitRangeItem_To_v1_LimitRangeItem is an autogenerated conversion function.
func Convert_core_LimitRangeItem_To_v1_LimitRangeItem(in *core.LimitRangeItem, out *corev1.LimitRangeItem, s conversion.Scope) error {
	return autoConvert_core_LimitRangeItem_To_v1_LimitRangeItem(in, out, s)
}

func autoConvert_v1_LimitRangeList_To_core_LimitRangeList(in *corev1.LimitRangeList, out *core.LimitRangeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.LimitRange)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_LimitRangeList_To_core_LimitRangeList is an autogenerated conversion function.
func Convert_v1_LimitRangeList_To_core_LimitRangeList(in *corev1.LimitRangeList, out *core.LimitRangeList, s conversion.Scope) error {
	return autoConvert_v1_LimitRangeList_To_core_LimitRangeList(in, out, s)
}

func autoConvert_core_LimitRangeList_To_v1_LimitRangeList(in *core.LimitRangeList, out *corev1.LimitRangeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.LimitRange)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_LimitRangeList_To_v1_LimitRangeList is an autogenerated conversion function.
func Convert_core_LimitRangeList_To_v1_LimitRangeList(in *core.LimitRangeList, out *corev1.LimitRangeList, s conversion.Scope) error {
	return autoConvert_core_LimitRangeList_To_v1_LimitRangeList(in, out, s)
}

func autoConvert_v1_LimitRangeSpec_To_core_LimitRangeSpec(in *corev1.LimitRangeSpec, out *core.LimitRangeSpec, s conversion.Scope) error {
	out.Limits = *(*[]core.LimitRangeItem)(unsafe.Pointer(&in.Limits))
	return nil
}

// Convert_v1_LimitRangeSpec_To_core_LimitRangeSpec is an autogenerated conversion function.
func Convert_v1_LimitRangeSpec_To_core_LimitRangeSpec(in *corev1.LimitRangeSpec, out *core.LimitRangeSpec, s conversion.Scope) error {
	return autoConvert_v1_LimitRangeSpec_To_core_LimitRangeSpec(in, out, s)
}

func autoConvert_core_LimitRangeSpec_To_v1_LimitRangeSpec(in *core.LimitRangeSpec, out *corev1.LimitRangeSpec, s conversion.Scope) error {
	out.Limits = *(*[]corev1.LimitRangeItem)(unsafe.Pointer(&in.Limits))
	return nil
}

// Convert_core_LimitRangeSpec_To_v1_LimitRangeSpec is an autogenerated conversion function.
func Convert_core_LimitRangeSpec_To_v1_LimitRangeSpec(in *core.LimitRangeSpec, out *corev1.LimitRangeSpec, s conversion.Scope) error {
	return autoConvert_core_LimitRangeSpec_To_v1_LimitRangeSpec(in, out, s)
}

func autoConvert_v1_LinuxContainerUser_To_core_LinuxContainerUser(in *corev1.LinuxContainerUser, out *core.LinuxContainerUser, s conversion.Scope) error {
	out.UID = in.UID
	out.GID = in.GID
	out.SupplementalGroups = *(*[]int64)(unsafe.Pointer(&in.SupplementalGroups))
	return nil
}

// Convert_v1_LinuxContainerUser_To_core_LinuxContainerUser is an autogenerated conversion function.
func Convert_v1_LinuxContainerUser_To_core_LinuxContainerUser(in *corev1.LinuxContainerUser, out *core.LinuxContainerUser, s conversion.Scope) error {
	return autoConvert_v1_LinuxContainerUser_To_core_LinuxContainerUser(in, out, s)
}

func autoConvert_core_LinuxContainerUser_To_v1_LinuxContainerUser(in *core.LinuxContainerUser, out *corev1.LinuxContainerUser, s conversion.Scope) error {
	out.UID = in.UID
	out.GID = in.GID
	out.SupplementalGroups = *(*[]int64)(unsafe.Pointer(&in.SupplementalGroups))
	return nil
}

// Convert_core_LinuxContainerUser_To_v1_LinuxContainerUser is an autogenerated conversion function.
func Convert_core_LinuxContainerUser_To_v1_LinuxContainerUser(in *core.LinuxContainerUser, out *corev1.LinuxContainerUser, s conversion.Scope) error {
	return autoConvert_core_LinuxContainerUser_To_v1_LinuxContainerUser(in, out, s)
}

func autoConvert_v1_List_To_core_List(in *corev1.List, out *core.List, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]runtime.Object, len(*in))
		for i := range *in {
			if err := runtime.Convert_runtime_RawExtension_To_runtime_Object(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_List_To_core_List is an autogenerated conversion function.
func Convert_v1_List_To_core_List(in *corev1.List, out *core.List, s conversion.Scope) error {
	return autoConvert_v1_List_To_core_List(in, out, s)
}

func autoConvert_core_List_To_v1_List(in *core.List, out *corev1.List, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]runtime.RawExtension, len(*in))
		for i := range *in {
			if err := runtime.Convert_runtime_Object_To_runtime_RawExtension(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_List_To_v1_List is an autogenerated conversion function.
func Convert_core_List_To_v1_List(in *core.List, out *corev1.List, s conversion.Scope) error {
	return autoConvert_core_List_To_v1_List(in, out, s)
}

func autoConvert_v1_LoadBalancerIngress_To_core_LoadBalancerIngress(in *corev1.LoadBalancerIngress, out *core.LoadBalancerIngress, s conversion.Scope) error {
	out.IP = in.IP
	out.Hostname = in.Hostname
	out.IPMode = (*core.LoadBalancerIPMode)(unsafe.Pointer(in.IPMode))
	out.Ports = *(*[]core.PortStatus)(unsafe.Pointer(&in.Ports))
	return nil
}

// Convert_v1_LoadBalancerIngress_To_core_LoadBalancerIngress is an autogenerated conversion function.
func Convert_v1_LoadBalancerIngress_To_core_LoadBalancerIngress(in *corev1.LoadBalancerIngress, out *core.LoadBalancerIngress, s conversion.Scope) error {
	return autoConvert_v1_LoadBalancerIngress_To_core_LoadBalancerIngress(in, out, s)
}

func autoConvert_core_LoadBalancerIngress_To_v1_LoadBalancerIngress(in *core.LoadBalancerIngress, out *corev1.LoadBalancerIngress, s conversion.Scope) error {
	out.IP = in.IP
	out.Hostname = in.Hostname
	out.IPMode = (*corev1.LoadBalancerIPMode)(unsafe.Pointer(in.IPMode))
	out.Ports = *(*[]corev1.PortStatus)(unsafe.Pointer(&in.Ports))
	return nil
}

// Convert_core_LoadBalancerIngress_To_v1_LoadBalancerIngress is an autogenerated conversion function.
func Convert_core_LoadBalancerIngress_To_v1_LoadBalancerIngress(in *core.LoadBalancerIngress, out *corev1.LoadBalancerIngress, s conversion.Scope) error {
	return autoConvert_core_LoadBalancerIngress_To_v1_LoadBalancerIngress(in, out, s)
}

func autoConvert_v1_LoadBalancerStatus_To_core_LoadBalancerStatus(in *corev1.LoadBalancerStatus, out *core.LoadBalancerStatus, s conversion.Scope) error {
	out.Ingress = *(*[]core.LoadBalancerIngress)(unsafe.Pointer(&in.Ingress))
	return nil
}

// Convert_v1_LoadBalancerStatus_To_core_LoadBalancerStatus is an autogenerated conversion function.
func Convert_v1_LoadBalancerStatus_To_core_LoadBalancerStatus(in *corev1.LoadBalancerStatus, out *core.LoadBalancerStatus, s conversion.Scope) error {
	return autoConvert_v1_LoadBalancerStatus_To_core_LoadBalancerStatus(in, out, s)
}

func autoConvert_core_LoadBalancerStatus_To_v1_LoadBalancerStatus(in *core.LoadBalancerStatus, out *corev1.LoadBalancerStatus, s conversion.Scope) error {
	out.Ingress = *(*[]corev1.LoadBalancerIngress)(unsafe.Pointer(&in.Ingress))
	return nil
}

// Convert_core_LoadBalancerStatus_To_v1_LoadBalancerStatus is an autogenerated conversion function.
func Convert_core_LoadBalancerStatus_To_v1_LoadBalancerStatus(in *core.LoadBalancerStatus, out *corev1.LoadBalancerStatus, s conversion.Scope) error {
	return autoConvert_core_LoadBalancerStatus_To_v1_LoadBalancerStatus(in, out, s)
}

func autoConvert_v1_LocalObjectReference_To_core_LocalObjectReference(in *corev1.LocalObjectReference, out *core.LocalObjectReference, s conversion.Scope) error {
	out.Name = in.Name
	return nil
}

// Convert_v1_LocalObjectReference_To_core_LocalObjectReference is an autogenerated conversion function.
func Convert_v1_LocalObjectReference_To_core_LocalObjectReference(in *corev1.LocalObjectReference, out *core.LocalObjectReference, s conversion.Scope) error {
	return autoConvert_v1_LocalObjectReference_To_core_LocalObjectReference(in, out, s)
}

func autoConvert_core_LocalObjectReference_To_v1_LocalObjectReference(in *core.LocalObjectReference, out *corev1.LocalObjectReference, s conversion.Scope) error {
	out.Name = in.Name
	return nil
}

// Convert_core_LocalObjectReference_To_v1_LocalObjectReference is an autogenerated conversion function.
func Convert_core_LocalObjectReference_To_v1_LocalObjectReference(in *core.LocalObjectReference, out *corev1.LocalObjectReference, s conversion.Scope) error {
	return autoConvert_core_LocalObjectReference_To_v1_LocalObjectReference(in, out, s)
}

func autoConvert_v1_LocalVolumeSource_To_core_LocalVolumeSource(in *corev1.LocalVolumeSource, out *core.LocalVolumeSource, s conversion.Scope) error {
	out.Path = in.Path
	out.FSType = (*string)(unsafe.Pointer(in.FSType))
	return nil
}

// Convert_v1_LocalVolumeSource_To_core_LocalVolumeSource is an autogenerated conversion function.
func Convert_v1_LocalVolumeSource_To_core_LocalVolumeSource(in *corev1.LocalVolumeSource, out *core.LocalVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_LocalVolumeSource_To_core_LocalVolumeSource(in, out, s)
}

func autoConvert_core_LocalVolumeSource_To_v1_LocalVolumeSource(in *core.LocalVolumeSource, out *corev1.LocalVolumeSource, s conversion.Scope) error {
	out.Path = in.Path
	out.FSType = (*string)(unsafe.Pointer(in.FSType))
	return nil
}

// Convert_core_LocalVolumeSource_To_v1_LocalVolumeSource is an autogenerated conversion function.
func Convert_core_LocalVolumeSource_To_v1_LocalVolumeSource(in *core.LocalVolumeSource, out *corev1.LocalVolumeSource, s conversion.Scope) error {
	return autoConvert_core_LocalVolumeSource_To_v1_LocalVolumeSource(in, out, s)
}

func autoConvert_v1_ModifyVolumeStatus_To_core_ModifyVolumeStatus(in *corev1.ModifyVolumeStatus, out *core.ModifyVolumeStatus, s conversion.Scope) error {
	out.TargetVolumeAttributesClassName = in.TargetVolumeAttributesClassName
	out.Status = core.PersistentVolumeClaimModifyVolumeStatus(in.Status)
	return nil
}

// Convert_v1_ModifyVolumeStatus_To_core_ModifyVolumeStatus is an autogenerated conversion function.
func Convert_v1_ModifyVolumeStatus_To_core_ModifyVolumeStatus(in *corev1.ModifyVolumeStatus, out *core.ModifyVolumeStatus, s conversion.Scope) error {
	return autoConvert_v1_ModifyVolumeStatus_To_core_ModifyVolumeStatus(in, out, s)
}

func autoConvert_core_ModifyVolumeStatus_To_v1_ModifyVolumeStatus(in *core.ModifyVolumeStatus, out *corev1.ModifyVolumeStatus, s conversion.Scope) error {
	out.TargetVolumeAttributesClassName = in.TargetVolumeAttributesClassName
	out.Status = corev1.PersistentVolumeClaimModifyVolumeStatus(in.Status)
	return nil
}

// Convert_core_ModifyVolumeStatus_To_v1_ModifyVolumeStatus is an autogenerated conversion function.
func Convert_core_ModifyVolumeStatus_To_v1_ModifyVolumeStatus(in *core.ModifyVolumeStatus, out *corev1.ModifyVolumeStatus, s conversion.Scope) error {
	return autoConvert_core_ModifyVolumeStatus_To_v1_ModifyVolumeStatus(in, out, s)
}

func autoConvert_v1_NFSVolumeSource_To_core_NFSVolumeSource(in *corev1.NFSVolumeSource, out *core.NFSVolumeSource, s conversion.Scope) error {
	out.Server = in.Server
	out.Path = in.Path
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_NFSVolumeSource_To_core_NFSVolumeSource is an autogenerated conversion function.
func Convert_v1_NFSVolumeSource_To_core_NFSVolumeSource(in *corev1.NFSVolumeSource, out *core.NFSVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_NFSVolumeSource_To_core_NFSVolumeSource(in, out, s)
}

func autoConvert_core_NFSVolumeSource_To_v1_NFSVolumeSource(in *core.NFSVolumeSource, out *corev1.NFSVolumeSource, s conversion.Scope) error {
	out.Server = in.Server
	out.Path = in.Path
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_NFSVolumeSource_To_v1_NFSVolumeSource is an autogenerated conversion function.
func Convert_core_NFSVolumeSource_To_v1_NFSVolumeSource(in *core.NFSVolumeSource, out *corev1.NFSVolumeSource, s conversion.Scope) error {
	return autoConvert_core_NFSVolumeSource_To_v1_NFSVolumeSource(in, out, s)
}

func autoConvert_v1_Namespace_To_core_Namespace(in *corev1.Namespace, out *core.Namespace, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_NamespaceSpec_To_core_NamespaceSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_NamespaceStatus_To_core_NamespaceStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Namespace_To_core_Namespace is an autogenerated conversion function.
func Convert_v1_Namespace_To_core_Namespace(in *corev1.Namespace, out *core.Namespace, s conversion.Scope) error {
	return autoConvert_v1_Namespace_To_core_Namespace(in, out, s)
}

func autoConvert_core_Namespace_To_v1_Namespace(in *core.Namespace, out *corev1.Namespace, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_NamespaceSpec_To_v1_NamespaceSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_NamespaceStatus_To_v1_NamespaceStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_Namespace_To_v1_Namespace is an autogenerated conversion function.
func Convert_core_Namespace_To_v1_Namespace(in *core.Namespace, out *corev1.Namespace, s conversion.Scope) error {
	return autoConvert_core_Namespace_To_v1_Namespace(in, out, s)
}

func autoConvert_v1_NamespaceCondition_To_core_NamespaceCondition(in *corev1.NamespaceCondition, out *core.NamespaceCondition, s conversion.Scope) error {
	out.Type = core.NamespaceConditionType(in.Type)
	out.Status = core.ConditionStatus(in.Status)
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_NamespaceCondition_To_core_NamespaceCondition is an autogenerated conversion function.
func Convert_v1_NamespaceCondition_To_core_NamespaceCondition(in *corev1.NamespaceCondition, out *core.NamespaceCondition, s conversion.Scope) error {
	return autoConvert_v1_NamespaceCondition_To_core_NamespaceCondition(in, out, s)
}

func autoConvert_core_NamespaceCondition_To_v1_NamespaceCondition(in *core.NamespaceCondition, out *corev1.NamespaceCondition, s conversion.Scope) error {
	out.Type = corev1.NamespaceConditionType(in.Type)
	out.Status = corev1.ConditionStatus(in.Status)
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_core_NamespaceCondition_To_v1_NamespaceCondition is an autogenerated conversion function.
func Convert_core_NamespaceCondition_To_v1_NamespaceCondition(in *core.NamespaceCondition, out *corev1.NamespaceCondition, s conversion.Scope) error {
	return autoConvert_core_NamespaceCondition_To_v1_NamespaceCondition(in, out, s)
}

func autoConvert_v1_NamespaceList_To_core_NamespaceList(in *corev1.NamespaceList, out *core.NamespaceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.Namespace)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_NamespaceList_To_core_NamespaceList is an autogenerated conversion function.
func Convert_v1_NamespaceList_To_core_NamespaceList(in *corev1.NamespaceList, out *core.NamespaceList, s conversion.Scope) error {
	return autoConvert_v1_NamespaceList_To_core_NamespaceList(in, out, s)
}

func autoConvert_core_NamespaceList_To_v1_NamespaceList(in *core.NamespaceList, out *corev1.NamespaceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.Namespace)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_NamespaceList_To_v1_NamespaceList is an autogenerated conversion function.
func Convert_core_NamespaceList_To_v1_NamespaceList(in *core.NamespaceList, out *corev1.NamespaceList, s conversion.Scope) error {
	return autoConvert_core_NamespaceList_To_v1_NamespaceList(in, out, s)
}

func autoConvert_v1_NamespaceSpec_To_core_NamespaceSpec(in *corev1.NamespaceSpec, out *core.NamespaceSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]core.FinalizerName)(unsafe.Pointer(&in.Finalizers))
	return nil
}

// Convert_v1_NamespaceSpec_To_core_NamespaceSpec is an autogenerated conversion function.
func Convert_v1_NamespaceSpec_To_core_NamespaceSpec(in *corev1.NamespaceSpec, out *core.NamespaceSpec, s conversion.Scope) error {
	return autoConvert_v1_NamespaceSpec_To_core_NamespaceSpec(in, out, s)
}

func autoConvert_core_NamespaceSpec_To_v1_NamespaceSpec(in *core.NamespaceSpec, out *corev1.NamespaceSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]corev1.FinalizerName)(unsafe.Pointer(&in.Finalizers))
	return nil
}

// Convert_core_NamespaceSpec_To_v1_NamespaceSpec is an autogenerated conversion function.
func Convert_core_NamespaceSpec_To_v1_NamespaceSpec(in *core.NamespaceSpec, out *corev1.NamespaceSpec, s conversion.Scope) error {
	return autoConvert_core_NamespaceSpec_To_v1_NamespaceSpec(in, out, s)
}

func autoConvert_v1_NamespaceStatus_To_core_NamespaceStatus(in *corev1.NamespaceStatus, out *core.NamespaceStatus, s conversion.Scope) error {
	out.Phase = core.NamespacePhase(in.Phase)
	out.Conditions = *(*[]core.NamespaceCondition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_v1_NamespaceStatus_To_core_NamespaceStatus is an autogenerated conversion function.
func Convert_v1_NamespaceStatus_To_core_NamespaceStatus(in *corev1.NamespaceStatus, out *core.NamespaceStatus, s conversion.Scope) error {
	return autoConvert_v1_NamespaceStatus_To_core_NamespaceStatus(in, out, s)
}

func autoConvert_core_NamespaceStatus_To_v1_NamespaceStatus(in *core.NamespaceStatus, out *corev1.NamespaceStatus, s conversion.Scope) error {
	out.Phase = corev1.NamespacePhase(in.Phase)
	out.Conditions = *(*[]corev1.NamespaceCondition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_core_NamespaceStatus_To_v1_NamespaceStatus is an autogenerated conversion function.
func Convert_core_NamespaceStatus_To_v1_NamespaceStatus(in *core.NamespaceStatus, out *corev1.NamespaceStatus, s conversion.Scope) error {
	return autoConvert_core_NamespaceStatus_To_v1_NamespaceStatus(in, out, s)
}

func autoConvert_v1_Node_To_core_Node(in *corev1.Node, out *core.Node, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_NodeSpec_To_core_NodeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_NodeStatus_To_core_NodeStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Node_To_core_Node is an autogenerated conversion function.
func Convert_v1_Node_To_core_Node(in *corev1.Node, out *core.Node, s conversion.Scope) error {
	return autoConvert_v1_Node_To_core_Node(in, out, s)
}

func autoConvert_core_Node_To_v1_Node(in *core.Node, out *corev1.Node, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_NodeSpec_To_v1_NodeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_NodeStatus_To_v1_NodeStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_Node_To_v1_Node is an autogenerated conversion function.
func Convert_core_Node_To_v1_Node(in *core.Node, out *corev1.Node, s conversion.Scope) error {
	return autoConvert_core_Node_To_v1_Node(in, out, s)
}

func autoConvert_v1_NodeAddress_To_core_NodeAddress(in *corev1.NodeAddress, out *core.NodeAddress, s conversion.Scope) error {
	out.Type = core.NodeAddressType(in.Type)
	out.Address = in.Address
	return nil
}

// Convert_v1_NodeAddress_To_core_NodeAddress is an autogenerated conversion function.
func Convert_v1_NodeAddress_To_core_NodeAddress(in *corev1.NodeAddress, out *core.NodeAddress, s conversion.Scope) error {
	return autoConvert_v1_NodeAddress_To_core_NodeAddress(in, out, s)
}

func autoConvert_core_NodeAddress_To_v1_NodeAddress(in *core.NodeAddress, out *corev1.NodeAddress, s conversion.Scope) error {
	out.Type = corev1.NodeAddressType(in.Type)
	out.Address = in.Address
	return nil
}

// Convert_core_NodeAddress_To_v1_NodeAddress is an autogenerated conversion function.
func Convert_core_NodeAddress_To_v1_NodeAddress(in *core.NodeAddress, out *corev1.NodeAddress, s conversion.Scope) error {
	return autoConvert_core_NodeAddress_To_v1_NodeAddress(in, out, s)
}

func autoConvert_v1_NodeAffinity_To_core_NodeAffinity(in *corev1.NodeAffinity, out *core.NodeAffinity, s conversion.Scope) error {
	out.RequiredDuringSchedulingIgnoredDuringExecution = (*core.NodeSelector)(unsafe.Pointer(in.RequiredDuringSchedulingIgnoredDuringExecution))
	out.PreferredDuringSchedulingIgnoredDuringExecution = *(*[]core.PreferredSchedulingTerm)(unsafe.Pointer(&in.PreferredDuringSchedulingIgnoredDuringExecution))
	return nil
}

// Convert_v1_NodeAffinity_To_core_NodeAffinity is an autogenerated conversion function.
func Convert_v1_NodeAffinity_To_core_NodeAffinity(in *corev1.NodeAffinity, out *core.NodeAffinity, s conversion.Scope) error {
	return autoConvert_v1_NodeAffinity_To_core_NodeAffinity(in, out, s)
}

func autoConvert_core_NodeAffinity_To_v1_NodeAffinity(in *core.NodeAffinity, out *corev1.NodeAffinity, s conversion.Scope) error {
	out.RequiredDuringSchedulingIgnoredDuringExecution = (*corev1.NodeSelector)(unsafe.Pointer(in.RequiredDuringSchedulingIgnoredDuringExecution))
	out.PreferredDuringSchedulingIgnoredDuringExecution = *(*[]corev1.PreferredSchedulingTerm)(unsafe.Pointer(&in.PreferredDuringSchedulingIgnoredDuringExecution))
	return nil
}

// Convert_core_NodeAffinity_To_v1_NodeAffinity is an autogenerated conversion function.
func Convert_core_NodeAffinity_To_v1_NodeAffinity(in *core.NodeAffinity, out *corev1.NodeAffinity, s conversion.Scope) error {
	return autoConvert_core_NodeAffinity_To_v1_NodeAffinity(in, out, s)
}

func autoConvert_v1_NodeCondition_To_core_NodeCondition(in *corev1.NodeCondition, out *core.NodeCondition, s conversion.Scope) error {
	out.Type = core.NodeConditionType(in.Type)
	out.Status = core.ConditionStatus(in.Status)
	out.LastHeartbeatTime = in.LastHeartbeatTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_NodeCondition_To_core_NodeCondition is an autogenerated conversion function.
func Convert_v1_NodeCondition_To_core_NodeCondition(in *corev1.NodeCondition, out *core.NodeCondition, s conversion.Scope) error {
	return autoConvert_v1_NodeCondition_To_core_NodeCondition(in, out, s)
}

func autoConvert_core_NodeCondition_To_v1_NodeCondition(in *core.NodeCondition, out *corev1.NodeCondition, s conversion.Scope) error {
	out.Type = corev1.NodeConditionType(in.Type)
	out.Status = corev1.ConditionStatus(in.Status)
	out.LastHeartbeatTime = in.LastHeartbeatTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_core_NodeCondition_To_v1_NodeCondition is an autogenerated conversion function.
func Convert_core_NodeCondition_To_v1_NodeCondition(in *core.NodeCondition, out *corev1.NodeCondition, s conversion.Scope) error {
	return autoConvert_core_NodeCondition_To_v1_NodeCondition(in, out, s)
}

func autoConvert_v1_NodeConfigSource_To_core_NodeConfigSource(in *corev1.NodeConfigSource, out *core.NodeConfigSource, s conversion.Scope) error {
	out.ConfigMap = (*core.ConfigMapNodeConfigSource)(unsafe.Pointer(in.ConfigMap))
	return nil
}

// Convert_v1_NodeConfigSource_To_core_NodeConfigSource is an autogenerated conversion function.
func Convert_v1_NodeConfigSource_To_core_NodeConfigSource(in *corev1.NodeConfigSource, out *core.NodeConfigSource, s conversion.Scope) error {
	return autoConvert_v1_NodeConfigSource_To_core_NodeConfigSource(in, out, s)
}

func autoConvert_core_NodeConfigSource_To_v1_NodeConfigSource(in *core.NodeConfigSource, out *corev1.NodeConfigSource, s conversion.Scope) error {
	out.ConfigMap = (*corev1.ConfigMapNodeConfigSource)(unsafe.Pointer(in.ConfigMap))
	return nil
}

// Convert_core_NodeConfigSource_To_v1_NodeConfigSource is an autogenerated conversion function.
func Convert_core_NodeConfigSource_To_v1_NodeConfigSource(in *core.NodeConfigSource, out *corev1.NodeConfigSource, s conversion.Scope) error {
	return autoConvert_core_NodeConfigSource_To_v1_NodeConfigSource(in, out, s)
}

func autoConvert_v1_NodeConfigStatus_To_core_NodeConfigStatus(in *corev1.NodeConfigStatus, out *core.NodeConfigStatus, s conversion.Scope) error {
	out.Assigned = (*core.NodeConfigSource)(unsafe.Pointer(in.Assigned))
	out.Active = (*core.NodeConfigSource)(unsafe.Pointer(in.Active))
	out.LastKnownGood = (*core.NodeConfigSource)(unsafe.Pointer(in.LastKnownGood))
	out.Error = in.Error
	return nil
}

// Convert_v1_NodeConfigStatus_To_core_NodeConfigStatus is an autogenerated conversion function.
func Convert_v1_NodeConfigStatus_To_core_NodeConfigStatus(in *corev1.NodeConfigStatus, out *core.NodeConfigStatus, s conversion.Scope) error {
	return autoConvert_v1_NodeConfigStatus_To_core_NodeConfigStatus(in, out, s)
}

func autoConvert_core_NodeConfigStatus_To_v1_NodeConfigStatus(in *core.NodeConfigStatus, out *corev1.NodeConfigStatus, s conversion.Scope) error {
	out.Assigned = (*corev1.NodeConfigSource)(unsafe.Pointer(in.Assigned))
	out.Active = (*corev1.NodeConfigSource)(unsafe.Pointer(in.Active))
	out.LastKnownGood = (*corev1.NodeConfigSource)(unsafe.Pointer(in.LastKnownGood))
	out.Error = in.Error
	return nil
}

// Convert_core_NodeConfigStatus_To_v1_NodeConfigStatus is an autogenerated conversion function.
func Convert_core_NodeConfigStatus_To_v1_NodeConfigStatus(in *core.NodeConfigStatus, out *corev1.NodeConfigStatus, s conversion.Scope) error {
	return autoConvert_core_NodeConfigStatus_To_v1_NodeConfigStatus(in, out, s)
}

func autoConvert_v1_NodeDaemonEndpoints_To_core_NodeDaemonEndpoints(in *corev1.NodeDaemonEndpoints, out *core.NodeDaemonEndpoints, s conversion.Scope) error {
	if err := Convert_v1_DaemonEndpoint_To_core_DaemonEndpoint(&in.KubeletEndpoint, &out.KubeletEndpoint, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_NodeDaemonEndpoints_To_core_NodeDaemonEndpoints is an autogenerated conversion function.
func Convert_v1_NodeDaemonEndpoints_To_core_NodeDaemonEndpoints(in *corev1.NodeDaemonEndpoints, out *core.NodeDaemonEndpoints, s conversion.Scope) error {
	return autoConvert_v1_NodeDaemonEndpoints_To_core_NodeDaemonEndpoints(in, out, s)
}

func autoConvert_core_NodeDaemonEndpoints_To_v1_NodeDaemonEndpoints(in *core.NodeDaemonEndpoints, out *corev1.NodeDaemonEndpoints, s conversion.Scope) error {
	if err := Convert_core_DaemonEndpoint_To_v1_DaemonEndpoint(&in.KubeletEndpoint, &out.KubeletEndpoint, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_NodeDaemonEndpoints_To_v1_NodeDaemonEndpoints is an autogenerated conversion function.
func Convert_core_NodeDaemonEndpoints_To_v1_NodeDaemonEndpoints(in *core.NodeDaemonEndpoints, out *corev1.NodeDaemonEndpoints, s conversion.Scope) error {
	return autoConvert_core_NodeDaemonEndpoints_To_v1_NodeDaemonEndpoints(in, out, s)
}

func autoConvert_v1_NodeFeatures_To_core_NodeFeatures(in *corev1.NodeFeatures, out *core.NodeFeatures, s conversion.Scope) error {
	out.SupplementalGroupsPolicy = (*bool)(unsafe.Pointer(in.SupplementalGroupsPolicy))
	return nil
}

// Convert_v1_NodeFeatures_To_core_NodeFeatures is an autogenerated conversion function.
func Convert_v1_NodeFeatures_To_core_NodeFeatures(in *corev1.NodeFeatures, out *core.NodeFeatures, s conversion.Scope) error {
	return autoConvert_v1_NodeFeatures_To_core_NodeFeatures(in, out, s)
}

func autoConvert_core_NodeFeatures_To_v1_NodeFeatures(in *core.NodeFeatures, out *corev1.NodeFeatures, s conversion.Scope) error {
	out.SupplementalGroupsPolicy = (*bool)(unsafe.Pointer(in.SupplementalGroupsPolicy))
	return nil
}

// Convert_core_NodeFeatures_To_v1_NodeFeatures is an autogenerated conversion function.
func Convert_core_NodeFeatures_To_v1_NodeFeatures(in *core.NodeFeatures, out *corev1.NodeFeatures, s conversion.Scope) error {
	return autoConvert_core_NodeFeatures_To_v1_NodeFeatures(in, out, s)
}

func autoConvert_v1_NodeList_To_core_NodeList(in *corev1.NodeList, out *core.NodeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]core.Node, len(*in))
		for i := range *in {
			if err := Convert_v1_Node_To_core_Node(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_NodeList_To_core_NodeList is an autogenerated conversion function.
func Convert_v1_NodeList_To_core_NodeList(in *corev1.NodeList, out *core.NodeList, s conversion.Scope) error {
	return autoConvert_v1_NodeList_To_core_NodeList(in, out, s)
}

func autoConvert_core_NodeList_To_v1_NodeList(in *core.NodeList, out *corev1.NodeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]corev1.Node, len(*in))
		for i := range *in {
			if err := Convert_core_Node_To_v1_Node(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_NodeList_To_v1_NodeList is an autogenerated conversion function.
func Convert_core_NodeList_To_v1_NodeList(in *core.NodeList, out *corev1.NodeList, s conversion.Scope) error {
	return autoConvert_core_NodeList_To_v1_NodeList(in, out, s)
}

func autoConvert_v1_NodeProxyOptions_To_core_NodeProxyOptions(in *corev1.NodeProxyOptions, out *core.NodeProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_v1_NodeProxyOptions_To_core_NodeProxyOptions is an autogenerated conversion function.
func Convert_v1_NodeProxyOptions_To_core_NodeProxyOptions(in *corev1.NodeProxyOptions, out *core.NodeProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_NodeProxyOptions_To_core_NodeProxyOptions(in, out, s)
}

func autoConvert_core_NodeProxyOptions_To_v1_NodeProxyOptions(in *core.NodeProxyOptions, out *corev1.NodeProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_core_NodeProxyOptions_To_v1_NodeProxyOptions is an autogenerated conversion function.
func Convert_core_NodeProxyOptions_To_v1_NodeProxyOptions(in *core.NodeProxyOptions, out *corev1.NodeProxyOptions, s conversion.Scope) error {
	return autoConvert_core_NodeProxyOptions_To_v1_NodeProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_NodeProxyOptions(in *url.Values, out *corev1.NodeProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["path"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Path, s); err != nil {
			return err
		}
	} else {
		out.Path = ""
	}
	return nil
}

// Convert_url_Values_To_v1_NodeProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_NodeProxyOptions(in *url.Values, out *corev1.NodeProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_NodeProxyOptions(in, out, s)
}

func autoConvert_v1_NodeRuntimeHandler_To_core_NodeRuntimeHandler(in *corev1.NodeRuntimeHandler, out *core.NodeRuntimeHandler, s conversion.Scope) error {
	out.Name = in.Name
	out.Features = (*core.NodeRuntimeHandlerFeatures)(unsafe.Pointer(in.Features))
	return nil
}

// Convert_v1_NodeRuntimeHandler_To_core_NodeRuntimeHandler is an autogenerated conversion function.
func Convert_v1_NodeRuntimeHandler_To_core_NodeRuntimeHandler(in *corev1.NodeRuntimeHandler, out *core.NodeRuntimeHandler, s conversion.Scope) error {
	return autoConvert_v1_NodeRuntimeHandler_To_core_NodeRuntimeHandler(in, out, s)
}

func autoConvert_core_NodeRuntimeHandler_To_v1_NodeRuntimeHandler(in *core.NodeRuntimeHandler, out *corev1.NodeRuntimeHandler, s conversion.Scope) error {
	out.Name = in.Name
	out.Features = (*corev1.NodeRuntimeHandlerFeatures)(unsafe.Pointer(in.Features))
	return nil
}

// Convert_core_NodeRuntimeHandler_To_v1_NodeRuntimeHandler is an autogenerated conversion function.
func Convert_core_NodeRuntimeHandler_To_v1_NodeRuntimeHandler(in *core.NodeRuntimeHandler, out *corev1.NodeRuntimeHandler, s conversion.Scope) error {
	return autoConvert_core_NodeRuntimeHandler_To_v1_NodeRuntimeHandler(in, out, s)
}

func autoConvert_v1_NodeRuntimeHandlerFeatures_To_core_NodeRuntimeHandlerFeatures(in *corev1.NodeRuntimeHandlerFeatures, out *core.NodeRuntimeHandlerFeatures, s conversion.Scope) error {
	out.RecursiveReadOnlyMounts = (*bool)(unsafe.Pointer(in.RecursiveReadOnlyMounts))
	out.UserNamespaces = (*bool)(unsafe.Pointer(in.UserNamespaces))
	return nil
}

// Convert_v1_NodeRuntimeHandlerFeatures_To_core_NodeRuntimeHandlerFeatures is an autogenerated conversion function.
func Convert_v1_NodeRuntimeHandlerFeatures_To_core_NodeRuntimeHandlerFeatures(in *corev1.NodeRuntimeHandlerFeatures, out *core.NodeRuntimeHandlerFeatures, s conversion.Scope) error {
	return autoConvert_v1_NodeRuntimeHandlerFeatures_To_core_NodeRuntimeHandlerFeatures(in, out, s)
}

func autoConvert_core_NodeRuntimeHandlerFeatures_To_v1_NodeRuntimeHandlerFeatures(in *core.NodeRuntimeHandlerFeatures, out *corev1.NodeRuntimeHandlerFeatures, s conversion.Scope) error {
	out.RecursiveReadOnlyMounts = (*bool)(unsafe.Pointer(in.RecursiveReadOnlyMounts))
	out.UserNamespaces = (*bool)(unsafe.Pointer(in.UserNamespaces))
	return nil
}

// Convert_core_NodeRuntimeHandlerFeatures_To_v1_NodeRuntimeHandlerFeatures is an autogenerated conversion function.
func Convert_core_NodeRuntimeHandlerFeatures_To_v1_NodeRuntimeHandlerFeatures(in *core.NodeRuntimeHandlerFeatures, out *corev1.NodeRuntimeHandlerFeatures, s conversion.Scope) error {
	return autoConvert_core_NodeRuntimeHandlerFeatures_To_v1_NodeRuntimeHandlerFeatures(in, out, s)
}

func autoConvert_v1_NodeSelector_To_core_NodeSelector(in *corev1.NodeSelector, out *core.NodeSelector, s conversion.Scope) error {
	out.NodeSelectorTerms = *(*[]core.NodeSelectorTerm)(unsafe.Pointer(&in.NodeSelectorTerms))
	return nil
}

// Convert_v1_NodeSelector_To_core_NodeSelector is an autogenerated conversion function.
func Convert_v1_NodeSelector_To_core_NodeSelector(in *corev1.NodeSelector, out *core.NodeSelector, s conversion.Scope) error {
	return autoConvert_v1_NodeSelector_To_core_NodeSelector(in, out, s)
}

func autoConvert_core_NodeSelector_To_v1_NodeSelector(in *core.NodeSelector, out *corev1.NodeSelector, s conversion.Scope) error {
	out.NodeSelectorTerms = *(*[]corev1.NodeSelectorTerm)(unsafe.Pointer(&in.NodeSelectorTerms))
	return nil
}

// Convert_core_NodeSelector_To_v1_NodeSelector is an autogenerated conversion function.
func Convert_core_NodeSelector_To_v1_NodeSelector(in *core.NodeSelector, out *corev1.NodeSelector, s conversion.Scope) error {
	return autoConvert_core_NodeSelector_To_v1_NodeSelector(in, out, s)
}

func autoConvert_v1_NodeSelectorRequirement_To_core_NodeSelectorRequirement(in *corev1.NodeSelectorRequirement, out *core.NodeSelectorRequirement, s conversion.Scope) error {
	out.Key = in.Key
	out.Operator = core.NodeSelectorOperator(in.Operator)
	out.Values = *(*[]string)(unsafe.Pointer(&in.Values))
	return nil
}

// Convert_v1_NodeSelectorRequirement_To_core_NodeSelectorRequirement is an autogenerated conversion function.
func Convert_v1_NodeSelectorRequirement_To_core_NodeSelectorRequirement(in *corev1.NodeSelectorRequirement, out *core.NodeSelectorRequirement, s conversion.Scope) error {
	return autoConvert_v1_NodeSelectorRequirement_To_core_NodeSelectorRequirement(in, out, s)
}

func autoConvert_core_NodeSelectorRequirement_To_v1_NodeSelectorRequirement(in *core.NodeSelectorRequirement, out *corev1.NodeSelectorRequirement, s conversion.Scope) error {
	out.Key = in.Key
	out.Operator = corev1.NodeSelectorOperator(in.Operator)
	out.Values = *(*[]string)(unsafe.Pointer(&in.Values))
	return nil
}

// Convert_core_NodeSelectorRequirement_To_v1_NodeSelectorRequirement is an autogenerated conversion function.
func Convert_core_NodeSelectorRequirement_To_v1_NodeSelectorRequirement(in *core.NodeSelectorRequirement, out *corev1.NodeSelectorRequirement, s conversion.Scope) error {
	return autoConvert_core_NodeSelectorRequirement_To_v1_NodeSelectorRequirement(in, out, s)
}

func autoConvert_v1_NodeSelectorTerm_To_core_NodeSelectorTerm(in *corev1.NodeSelectorTerm, out *core.NodeSelectorTerm, s conversion.Scope) error {
	out.MatchExpressions = *(*[]core.NodeSelectorRequirement)(unsafe.Pointer(&in.MatchExpressions))
	out.MatchFields = *(*[]core.NodeSelectorRequirement)(unsafe.Pointer(&in.MatchFields))
	return nil
}

// Convert_v1_NodeSelectorTerm_To_core_NodeSelectorTerm is an autogenerated conversion function.
func Convert_v1_NodeSelectorTerm_To_core_NodeSelectorTerm(in *corev1.NodeSelectorTerm, out *core.NodeSelectorTerm, s conversion.Scope) error {
	return autoConvert_v1_NodeSelectorTerm_To_core_NodeSelectorTerm(in, out, s)
}

func autoConvert_core_NodeSelectorTerm_To_v1_NodeSelectorTerm(in *core.NodeSelectorTerm, out *corev1.NodeSelectorTerm, s conversion.Scope) error {
	out.MatchExpressions = *(*[]corev1.NodeSelectorRequirement)(unsafe.Pointer(&in.MatchExpressions))
	out.MatchFields = *(*[]corev1.NodeSelectorRequirement)(unsafe.Pointer(&in.MatchFields))
	return nil
}

// Convert_core_NodeSelectorTerm_To_v1_NodeSelectorTerm is an autogenerated conversion function.
func Convert_core_NodeSelectorTerm_To_v1_NodeSelectorTerm(in *core.NodeSelectorTerm, out *corev1.NodeSelectorTerm, s conversion.Scope) error {
	return autoConvert_core_NodeSelectorTerm_To_v1_NodeSelectorTerm(in, out, s)
}

func autoConvert_v1_NodeSpec_To_core_NodeSpec(in *corev1.NodeSpec, out *core.NodeSpec, s conversion.Scope) error {
	// WARNING: in.PodCIDR requires manual conversion: does not exist in peer-type
	out.PodCIDRs = *(*[]string)(unsafe.Pointer(&in.PodCIDRs))
	out.ProviderID = in.ProviderID
	out.Unschedulable = in.Unschedulable
	out.Taints = *(*[]core.Taint)(unsafe.Pointer(&in.Taints))
	out.ConfigSource = (*core.NodeConfigSource)(unsafe.Pointer(in.ConfigSource))
	out.DoNotUseExternalID = in.DoNotUseExternalID
	return nil
}

func autoConvert_core_NodeSpec_To_v1_NodeSpec(in *core.NodeSpec, out *corev1.NodeSpec, s conversion.Scope) error {
	out.PodCIDRs = *(*[]string)(unsafe.Pointer(&in.PodCIDRs))
	out.ProviderID = in.ProviderID
	out.Unschedulable = in.Unschedulable
	out.Taints = *(*[]corev1.Taint)(unsafe.Pointer(&in.Taints))
	out.ConfigSource = (*corev1.NodeConfigSource)(unsafe.Pointer(in.ConfigSource))
	out.DoNotUseExternalID = in.DoNotUseExternalID
	return nil
}

func autoConvert_v1_NodeStatus_To_core_NodeStatus(in *corev1.NodeStatus, out *core.NodeStatus, s conversion.Scope) error {
	out.Capacity = *(*core.ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Allocatable = *(*core.ResourceList)(unsafe.Pointer(&in.Allocatable))
	out.Phase = core.NodePhase(in.Phase)
	out.Conditions = *(*[]core.NodeCondition)(unsafe.Pointer(&in.Conditions))
	out.Addresses = *(*[]core.NodeAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_v1_NodeDaemonEndpoints_To_core_NodeDaemonEndpoints(&in.DaemonEndpoints, &out.DaemonEndpoints, s); err != nil {
		return err
	}
	if err := Convert_v1_NodeSystemInfo_To_core_NodeSystemInfo(&in.NodeInfo, &out.NodeInfo, s); err != nil {
		return err
	}
	out.Images = *(*[]core.ContainerImage)(unsafe.Pointer(&in.Images))
	out.VolumesInUse = *(*[]core.UniqueVolumeName)(unsafe.Pointer(&in.VolumesInUse))
	out.VolumesAttached = *(*[]core.AttachedVolume)(unsafe.Pointer(&in.VolumesAttached))
	out.Config = (*core.NodeConfigStatus)(unsafe.Pointer(in.Config))
	out.RuntimeHandlers = *(*[]core.NodeRuntimeHandler)(unsafe.Pointer(&in.RuntimeHandlers))
	out.Features = (*core.NodeFeatures)(unsafe.Pointer(in.Features))
	return nil
}

// Convert_v1_NodeStatus_To_core_NodeStatus is an autogenerated conversion function.
func Convert_v1_NodeStatus_To_core_NodeStatus(in *corev1.NodeStatus, out *core.NodeStatus, s conversion.Scope) error {
	return autoConvert_v1_NodeStatus_To_core_NodeStatus(in, out, s)
}

func autoConvert_core_NodeStatus_To_v1_NodeStatus(in *core.NodeStatus, out *corev1.NodeStatus, s conversion.Scope) error {
	out.Capacity = *(*corev1.ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Allocatable = *(*corev1.ResourceList)(unsafe.Pointer(&in.Allocatable))
	out.Phase = corev1.NodePhase(in.Phase)
	out.Conditions = *(*[]corev1.NodeCondition)(unsafe.Pointer(&in.Conditions))
	out.Addresses = *(*[]corev1.NodeAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_core_NodeDaemonEndpoints_To_v1_NodeDaemonEndpoints(&in.DaemonEndpoints, &out.DaemonEndpoints, s); err != nil {
		return err
	}
	if err := Convert_core_NodeSystemInfo_To_v1_NodeSystemInfo(&in.NodeInfo, &out.NodeInfo, s); err != nil {
		return err
	}
	out.Images = *(*[]corev1.ContainerImage)(unsafe.Pointer(&in.Images))
	out.VolumesInUse = *(*[]corev1.UniqueVolumeName)(unsafe.Pointer(&in.VolumesInUse))
	out.VolumesAttached = *(*[]corev1.AttachedVolume)(unsafe.Pointer(&in.VolumesAttached))
	out.Config = (*corev1.NodeConfigStatus)(unsafe.Pointer(in.Config))
	out.RuntimeHandlers = *(*[]corev1.NodeRuntimeHandler)(unsafe.Pointer(&in.RuntimeHandlers))
	out.Features = (*corev1.NodeFeatures)(unsafe.Pointer(in.Features))
	return nil
}

// Convert_core_NodeStatus_To_v1_NodeStatus is an autogenerated conversion function.
func Convert_core_NodeStatus_To_v1_NodeStatus(in *core.NodeStatus, out *corev1.NodeStatus, s conversion.Scope) error {
	return autoConvert_core_NodeStatus_To_v1_NodeStatus(in, out, s)
}

func autoConvert_v1_NodeSwapStatus_To_core_NodeSwapStatus(in *corev1.NodeSwapStatus, out *core.NodeSwapStatus, s conversion.Scope) error {
	out.Capacity = (*int64)(unsafe.Pointer(in.Capacity))
	return nil
}

// Convert_v1_NodeSwapStatus_To_core_NodeSwapStatus is an autogenerated conversion function.
func Convert_v1_NodeSwapStatus_To_core_NodeSwapStatus(in *corev1.NodeSwapStatus, out *core.NodeSwapStatus, s conversion.Scope) error {
	return autoConvert_v1_NodeSwapStatus_To_core_NodeSwapStatus(in, out, s)
}

func autoConvert_core_NodeSwapStatus_To_v1_NodeSwapStatus(in *core.NodeSwapStatus, out *corev1.NodeSwapStatus, s conversion.Scope) error {
	out.Capacity = (*int64)(unsafe.Pointer(in.Capacity))
	return nil
}

// Convert_core_NodeSwapStatus_To_v1_NodeSwapStatus is an autogenerated conversion function.
func Convert_core_NodeSwapStatus_To_v1_NodeSwapStatus(in *core.NodeSwapStatus, out *corev1.NodeSwapStatus, s conversion.Scope) error {
	return autoConvert_core_NodeSwapStatus_To_v1_NodeSwapStatus(in, out, s)
}

func autoConvert_v1_NodeSystemInfo_To_core_NodeSystemInfo(in *corev1.NodeSystemInfo, out *core.NodeSystemInfo, s conversion.Scope) error {
	out.MachineID = in.MachineID
	out.SystemUUID = in.SystemUUID
	out.BootID = in.BootID
	out.KernelVersion = in.KernelVersion
	out.OSImage = in.OSImage
	out.ContainerRuntimeVersion = in.ContainerRuntimeVersion
	out.KubeletVersion = in.KubeletVersion
	out.KubeProxyVersion = in.KubeProxyVersion
	out.OperatingSystem = in.OperatingSystem
	out.Architecture = in.Architecture
	out.Swap = (*core.NodeSwapStatus)(unsafe.Pointer(in.Swap))
	return nil
}

// Convert_v1_NodeSystemInfo_To_core_NodeSystemInfo is an autogenerated conversion function.
func Convert_v1_NodeSystemInfo_To_core_NodeSystemInfo(in *corev1.NodeSystemInfo, out *core.NodeSystemInfo, s conversion.Scope) error {
	return autoConvert_v1_NodeSystemInfo_To_core_NodeSystemInfo(in, out, s)
}

func autoConvert_core_NodeSystemInfo_To_v1_NodeSystemInfo(in *core.NodeSystemInfo, out *corev1.NodeSystemInfo, s conversion.Scope) error {
	out.MachineID = in.MachineID
	out.SystemUUID = in.SystemUUID
	out.BootID = in.BootID
	out.KernelVersion = in.KernelVersion
	out.OSImage = in.OSImage
	out.ContainerRuntimeVersion = in.ContainerRuntimeVersion
	out.KubeletVersion = in.KubeletVersion
	out.KubeProxyVersion = in.KubeProxyVersion
	out.OperatingSystem = in.OperatingSystem
	out.Architecture = in.Architecture
	out.Swap = (*corev1.NodeSwapStatus)(unsafe.Pointer(in.Swap))
	return nil
}

// Convert_core_NodeSystemInfo_To_v1_NodeSystemInfo is an autogenerated conversion function.
func Convert_core_NodeSystemInfo_To_v1_NodeSystemInfo(in *core.NodeSystemInfo, out *corev1.NodeSystemInfo, s conversion.Scope) error {
	return autoConvert_core_NodeSystemInfo_To_v1_NodeSystemInfo(in, out, s)
}

func autoConvert_v1_ObjectFieldSelector_To_core_ObjectFieldSelector(in *corev1.ObjectFieldSelector, out *core.ObjectFieldSelector, s conversion.Scope) error {
	out.APIVersion = in.APIVersion
	out.FieldPath = in.FieldPath
	return nil
}

// Convert_v1_ObjectFieldSelector_To_core_ObjectFieldSelector is an autogenerated conversion function.
func Convert_v1_ObjectFieldSelector_To_core_ObjectFieldSelector(in *corev1.ObjectFieldSelector, out *core.ObjectFieldSelector, s conversion.Scope) error {
	return autoConvert_v1_ObjectFieldSelector_To_core_ObjectFieldSelector(in, out, s)
}

func autoConvert_core_ObjectFieldSelector_To_v1_ObjectFieldSelector(in *core.ObjectFieldSelector, out *corev1.ObjectFieldSelector, s conversion.Scope) error {
	out.APIVersion = in.APIVersion
	out.FieldPath = in.FieldPath
	return nil
}

// Convert_core_ObjectFieldSelector_To_v1_ObjectFieldSelector is an autogenerated conversion function.
func Convert_core_ObjectFieldSelector_To_v1_ObjectFieldSelector(in *core.ObjectFieldSelector, out *corev1.ObjectFieldSelector, s conversion.Scope) error {
	return autoConvert_core_ObjectFieldSelector_To_v1_ObjectFieldSelector(in, out, s)
}

func autoConvert_v1_ObjectReference_To_core_ObjectReference(in *corev1.ObjectReference, out *core.ObjectReference, s conversion.Scope) error {
	out.Kind = in.Kind
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.UID = types.UID(in.UID)
	out.APIVersion = in.APIVersion
	out.ResourceVersion = in.ResourceVersion
	out.FieldPath = in.FieldPath
	return nil
}

// Convert_v1_ObjectReference_To_core_ObjectReference is an autogenerated conversion function.
func Convert_v1_ObjectReference_To_core_ObjectReference(in *corev1.ObjectReference, out *core.ObjectReference, s conversion.Scope) error {
	return autoConvert_v1_ObjectReference_To_core_ObjectReference(in, out, s)
}

func autoConvert_core_ObjectReference_To_v1_ObjectReference(in *core.ObjectReference, out *corev1.ObjectReference, s conversion.Scope) error {
	out.Kind = in.Kind
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.UID = types.UID(in.UID)
	out.APIVersion = in.APIVersion
	out.ResourceVersion = in.ResourceVersion
	out.FieldPath = in.FieldPath
	return nil
}

// Convert_core_ObjectReference_To_v1_ObjectReference is an autogenerated conversion function.
func Convert_core_ObjectReference_To_v1_ObjectReference(in *core.ObjectReference, out *corev1.ObjectReference, s conversion.Scope) error {
	return autoConvert_core_ObjectReference_To_v1_ObjectReference(in, out, s)
}

func autoConvert_v1_PersistentVolume_To_core_PersistentVolume(in *corev1.PersistentVolume, out *core.PersistentVolume, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PersistentVolumeSpec_To_core_PersistentVolumeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_PersistentVolumeStatus_To_core_PersistentVolumeStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PersistentVolume_To_core_PersistentVolume is an autogenerated conversion function.
func Convert_v1_PersistentVolume_To_core_PersistentVolume(in *corev1.PersistentVolume, out *core.PersistentVolume, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolume_To_core_PersistentVolume(in, out, s)
}

func autoConvert_core_PersistentVolume_To_v1_PersistentVolume(in *core.PersistentVolume, out *corev1.PersistentVolume, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_PersistentVolumeSpec_To_v1_PersistentVolumeSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_PersistentVolumeStatus_To_v1_PersistentVolumeStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_PersistentVolume_To_v1_PersistentVolume is an autogenerated conversion function.
func Convert_core_PersistentVolume_To_v1_PersistentVolume(in *core.PersistentVolume, out *corev1.PersistentVolume, s conversion.Scope) error {
	return autoConvert_core_PersistentVolume_To_v1_PersistentVolume(in, out, s)
}

func autoConvert_v1_PersistentVolumeClaim_To_core_PersistentVolumeClaim(in *corev1.PersistentVolumeClaim, out *core.PersistentVolumeClaim, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PersistentVolumeClaimSpec_To_core_PersistentVolumeClaimSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_PersistentVolumeClaimStatus_To_core_PersistentVolumeClaimStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PersistentVolumeClaim_To_core_PersistentVolumeClaim is an autogenerated conversion function.
func Convert_v1_PersistentVolumeClaim_To_core_PersistentVolumeClaim(in *corev1.PersistentVolumeClaim, out *core.PersistentVolumeClaim, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeClaim_To_core_PersistentVolumeClaim(in, out, s)
}

func autoConvert_core_PersistentVolumeClaim_To_v1_PersistentVolumeClaim(in *core.PersistentVolumeClaim, out *corev1.PersistentVolumeClaim, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_PersistentVolumeClaimSpec_To_v1_PersistentVolumeClaimSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_PersistentVolumeClaimStatus_To_v1_PersistentVolumeClaimStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_PersistentVolumeClaim_To_v1_PersistentVolumeClaim is an autogenerated conversion function.
func Convert_core_PersistentVolumeClaim_To_v1_PersistentVolumeClaim(in *core.PersistentVolumeClaim, out *corev1.PersistentVolumeClaim, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeClaim_To_v1_PersistentVolumeClaim(in, out, s)
}

func autoConvert_v1_PersistentVolumeClaimCondition_To_core_PersistentVolumeClaimCondition(in *corev1.PersistentVolumeClaimCondition, out *core.PersistentVolumeClaimCondition, s conversion.Scope) error {
	out.Type = core.PersistentVolumeClaimConditionType(in.Type)
	out.Status = core.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_PersistentVolumeClaimCondition_To_core_PersistentVolumeClaimCondition is an autogenerated conversion function.
func Convert_v1_PersistentVolumeClaimCondition_To_core_PersistentVolumeClaimCondition(in *corev1.PersistentVolumeClaimCondition, out *core.PersistentVolumeClaimCondition, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeClaimCondition_To_core_PersistentVolumeClaimCondition(in, out, s)
}

func autoConvert_core_PersistentVolumeClaimCondition_To_v1_PersistentVolumeClaimCondition(in *core.PersistentVolumeClaimCondition, out *corev1.PersistentVolumeClaimCondition, s conversion.Scope) error {
	out.Type = corev1.PersistentVolumeClaimConditionType(in.Type)
	out.Status = corev1.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_core_PersistentVolumeClaimCondition_To_v1_PersistentVolumeClaimCondition is an autogenerated conversion function.
func Convert_core_PersistentVolumeClaimCondition_To_v1_PersistentVolumeClaimCondition(in *core.PersistentVolumeClaimCondition, out *corev1.PersistentVolumeClaimCondition, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeClaimCondition_To_v1_PersistentVolumeClaimCondition(in, out, s)
}

func autoConvert_v1_PersistentVolumeClaimList_To_core_PersistentVolumeClaimList(in *corev1.PersistentVolumeClaimList, out *core.PersistentVolumeClaimList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.PersistentVolumeClaim)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_PersistentVolumeClaimList_To_core_PersistentVolumeClaimList is an autogenerated conversion function.
func Convert_v1_PersistentVolumeClaimList_To_core_PersistentVolumeClaimList(in *corev1.PersistentVolumeClaimList, out *core.PersistentVolumeClaimList, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeClaimList_To_core_PersistentVolumeClaimList(in, out, s)
}

func autoConvert_core_PersistentVolumeClaimList_To_v1_PersistentVolumeClaimList(in *core.PersistentVolumeClaimList, out *corev1.PersistentVolumeClaimList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.PersistentVolumeClaim)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_PersistentVolumeClaimList_To_v1_PersistentVolumeClaimList is an autogenerated conversion function.
func Convert_core_PersistentVolumeClaimList_To_v1_PersistentVolumeClaimList(in *core.PersistentVolumeClaimList, out *corev1.PersistentVolumeClaimList, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeClaimList_To_v1_PersistentVolumeClaimList(in, out, s)
}

func autoConvert_v1_PersistentVolumeClaimSpec_To_core_PersistentVolumeClaimSpec(in *corev1.PersistentVolumeClaimSpec, out *core.PersistentVolumeClaimSpec, s conversion.Scope) error {
	out.AccessModes = *(*[]core.PersistentVolumeAccessMode)(unsafe.Pointer(&in.AccessModes))
	out.Selector = (*metav1.LabelSelector)(unsafe.Pointer(in.Selector))
	if err := Convert_v1_VolumeResourceRequirements_To_core_VolumeResourceRequirements(&in.Resources, &out.Resources, s); err != nil {
		return err
	}
	out.VolumeName = in.VolumeName
	out.StorageClassName = (*string)(unsafe.Pointer(in.StorageClassName))
	out.VolumeMode = (*core.PersistentVolumeMode)(unsafe.Pointer(in.VolumeMode))
	out.DataSource = (*core.TypedLocalObjectReference)(unsafe.Pointer(in.DataSource))
	out.DataSourceRef = (*core.TypedObjectReference)(unsafe.Pointer(in.DataSourceRef))
	out.VolumeAttributesClassName = (*string)(unsafe.Pointer(in.VolumeAttributesClassName))
	return nil
}

// Convert_v1_PersistentVolumeClaimSpec_To_core_PersistentVolumeClaimSpec is an autogenerated conversion function.
func Convert_v1_PersistentVolumeClaimSpec_To_core_PersistentVolumeClaimSpec(in *corev1.PersistentVolumeClaimSpec, out *core.PersistentVolumeClaimSpec, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeClaimSpec_To_core_PersistentVolumeClaimSpec(in, out, s)
}

func autoConvert_core_PersistentVolumeClaimSpec_To_v1_PersistentVolumeClaimSpec(in *core.PersistentVolumeClaimSpec, out *corev1.PersistentVolumeClaimSpec, s conversion.Scope) error {
	out.AccessModes = *(*[]corev1.PersistentVolumeAccessMode)(unsafe.Pointer(&in.AccessModes))
	out.Selector = (*metav1.LabelSelector)(unsafe.Pointer(in.Selector))
	if err := Convert_core_VolumeResourceRequirements_To_v1_VolumeResourceRequirements(&in.Resources, &out.Resources, s); err != nil {
		return err
	}
	out.VolumeName = in.VolumeName
	out.StorageClassName = (*string)(unsafe.Pointer(in.StorageClassName))
	out.VolumeMode = (*corev1.PersistentVolumeMode)(unsafe.Pointer(in.VolumeMode))
	out.DataSource = (*corev1.TypedLocalObjectReference)(unsafe.Pointer(in.DataSource))
	out.DataSourceRef = (*corev1.TypedObjectReference)(unsafe.Pointer(in.DataSourceRef))
	out.VolumeAttributesClassName = (*string)(unsafe.Pointer(in.VolumeAttributesClassName))
	return nil
}

// Convert_core_PersistentVolumeClaimSpec_To_v1_PersistentVolumeClaimSpec is an autogenerated conversion function.
func Convert_core_PersistentVolumeClaimSpec_To_v1_PersistentVolumeClaimSpec(in *core.PersistentVolumeClaimSpec, out *corev1.PersistentVolumeClaimSpec, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeClaimSpec_To_v1_PersistentVolumeClaimSpec(in, out, s)
}

func autoConvert_v1_PersistentVolumeClaimStatus_To_core_PersistentVolumeClaimStatus(in *corev1.PersistentVolumeClaimStatus, out *core.PersistentVolumeClaimStatus, s conversion.Scope) error {
	out.Phase = core.PersistentVolumeClaimPhase(in.Phase)
	out.AccessModes = *(*[]core.PersistentVolumeAccessMode)(unsafe.Pointer(&in.AccessModes))
	out.Capacity = *(*core.ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Conditions = *(*[]core.PersistentVolumeClaimCondition)(unsafe.Pointer(&in.Conditions))
	out.AllocatedResources = *(*core.ResourceList)(unsafe.Pointer(&in.AllocatedResources))
	out.AllocatedResourceStatuses = *(*map[core.ResourceName]core.ClaimResourceStatus)(unsafe.Pointer(&in.AllocatedResourceStatuses))
	out.CurrentVolumeAttributesClassName = (*string)(unsafe.Pointer(in.CurrentVolumeAttributesClassName))
	out.ModifyVolumeStatus = (*core.ModifyVolumeStatus)(unsafe.Pointer(in.ModifyVolumeStatus))
	return nil
}

// Convert_v1_PersistentVolumeClaimStatus_To_core_PersistentVolumeClaimStatus is an autogenerated conversion function.
func Convert_v1_PersistentVolumeClaimStatus_To_core_PersistentVolumeClaimStatus(in *corev1.PersistentVolumeClaimStatus, out *core.PersistentVolumeClaimStatus, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeClaimStatus_To_core_PersistentVolumeClaimStatus(in, out, s)
}

func autoConvert_core_PersistentVolumeClaimStatus_To_v1_PersistentVolumeClaimStatus(in *core.PersistentVolumeClaimStatus, out *corev1.PersistentVolumeClaimStatus, s conversion.Scope) error {
	out.Phase = corev1.PersistentVolumeClaimPhase(in.Phase)
	out.AccessModes = *(*[]corev1.PersistentVolumeAccessMode)(unsafe.Pointer(&in.AccessModes))
	out.Capacity = *(*corev1.ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Conditions = *(*[]corev1.PersistentVolumeClaimCondition)(unsafe.Pointer(&in.Conditions))
	out.AllocatedResources = *(*corev1.ResourceList)(unsafe.Pointer(&in.AllocatedResources))
	out.AllocatedResourceStatuses = *(*map[corev1.ResourceName]corev1.ClaimResourceStatus)(unsafe.Pointer(&in.AllocatedResourceStatuses))
	out.CurrentVolumeAttributesClassName = (*string)(unsafe.Pointer(in.CurrentVolumeAttributesClassName))
	out.ModifyVolumeStatus = (*corev1.ModifyVolumeStatus)(unsafe.Pointer(in.ModifyVolumeStatus))
	return nil
}

// Convert_core_PersistentVolumeClaimStatus_To_v1_PersistentVolumeClaimStatus is an autogenerated conversion function.
func Convert_core_PersistentVolumeClaimStatus_To_v1_PersistentVolumeClaimStatus(in *core.PersistentVolumeClaimStatus, out *corev1.PersistentVolumeClaimStatus, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeClaimStatus_To_v1_PersistentVolumeClaimStatus(in, out, s)
}

func autoConvert_v1_PersistentVolumeClaimTemplate_To_core_PersistentVolumeClaimTemplate(in *corev1.PersistentVolumeClaimTemplate, out *core.PersistentVolumeClaimTemplate, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PersistentVolumeClaimSpec_To_core_PersistentVolumeClaimSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PersistentVolumeClaimTemplate_To_core_PersistentVolumeClaimTemplate is an autogenerated conversion function.
func Convert_v1_PersistentVolumeClaimTemplate_To_core_PersistentVolumeClaimTemplate(in *corev1.PersistentVolumeClaimTemplate, out *core.PersistentVolumeClaimTemplate, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeClaimTemplate_To_core_PersistentVolumeClaimTemplate(in, out, s)
}

func autoConvert_core_PersistentVolumeClaimTemplate_To_v1_PersistentVolumeClaimTemplate(in *core.PersistentVolumeClaimTemplate, out *corev1.PersistentVolumeClaimTemplate, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_PersistentVolumeClaimSpec_To_v1_PersistentVolumeClaimSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_PersistentVolumeClaimTemplate_To_v1_PersistentVolumeClaimTemplate is an autogenerated conversion function.
func Convert_core_PersistentVolumeClaimTemplate_To_v1_PersistentVolumeClaimTemplate(in *core.PersistentVolumeClaimTemplate, out *corev1.PersistentVolumeClaimTemplate, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeClaimTemplate_To_v1_PersistentVolumeClaimTemplate(in, out, s)
}

func autoConvert_v1_PersistentVolumeClaimVolumeSource_To_core_PersistentVolumeClaimVolumeSource(in *corev1.PersistentVolumeClaimVolumeSource, out *core.PersistentVolumeClaimVolumeSource, s conversion.Scope) error {
	out.ClaimName = in.ClaimName
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_PersistentVolumeClaimVolumeSource_To_core_PersistentVolumeClaimVolumeSource is an autogenerated conversion function.
func Convert_v1_PersistentVolumeClaimVolumeSource_To_core_PersistentVolumeClaimVolumeSource(in *corev1.PersistentVolumeClaimVolumeSource, out *core.PersistentVolumeClaimVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeClaimVolumeSource_To_core_PersistentVolumeClaimVolumeSource(in, out, s)
}

func autoConvert_core_PersistentVolumeClaimVolumeSource_To_v1_PersistentVolumeClaimVolumeSource(in *core.PersistentVolumeClaimVolumeSource, out *corev1.PersistentVolumeClaimVolumeSource, s conversion.Scope) error {
	out.ClaimName = in.ClaimName
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_PersistentVolumeClaimVolumeSource_To_v1_PersistentVolumeClaimVolumeSource is an autogenerated conversion function.
func Convert_core_PersistentVolumeClaimVolumeSource_To_v1_PersistentVolumeClaimVolumeSource(in *core.PersistentVolumeClaimVolumeSource, out *corev1.PersistentVolumeClaimVolumeSource, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeClaimVolumeSource_To_v1_PersistentVolumeClaimVolumeSource(in, out, s)
}

func autoConvert_v1_PersistentVolumeList_To_core_PersistentVolumeList(in *corev1.PersistentVolumeList, out *core.PersistentVolumeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]core.PersistentVolume, len(*in))
		for i := range *in {
			if err := Convert_v1_PersistentVolume_To_core_PersistentVolume(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_PersistentVolumeList_To_core_PersistentVolumeList is an autogenerated conversion function.
func Convert_v1_PersistentVolumeList_To_core_PersistentVolumeList(in *corev1.PersistentVolumeList, out *core.PersistentVolumeList, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeList_To_core_PersistentVolumeList(in, out, s)
}

func autoConvert_core_PersistentVolumeList_To_v1_PersistentVolumeList(in *core.PersistentVolumeList, out *corev1.PersistentVolumeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]corev1.PersistentVolume, len(*in))
		for i := range *in {
			if err := Convert_core_PersistentVolume_To_v1_PersistentVolume(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_PersistentVolumeList_To_v1_PersistentVolumeList is an autogenerated conversion function.
func Convert_core_PersistentVolumeList_To_v1_PersistentVolumeList(in *core.PersistentVolumeList, out *corev1.PersistentVolumeList, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeList_To_v1_PersistentVolumeList(in, out, s)
}

func autoConvert_v1_PersistentVolumeSource_To_core_PersistentVolumeSource(in *corev1.PersistentVolumeSource, out *core.PersistentVolumeSource, s conversion.Scope) error {
	out.GCEPersistentDisk = (*core.GCEPersistentDiskVolumeSource)(unsafe.Pointer(in.GCEPersistentDisk))
	out.AWSElasticBlockStore = (*core.AWSElasticBlockStoreVolumeSource)(unsafe.Pointer(in.AWSElasticBlockStore))
	out.HostPath = (*core.HostPathVolumeSource)(unsafe.Pointer(in.HostPath))
	out.Glusterfs = (*core.GlusterfsPersistentVolumeSource)(unsafe.Pointer(in.Glusterfs))
	out.NFS = (*core.NFSVolumeSource)(unsafe.Pointer(in.NFS))
	out.RBD = (*core.RBDPersistentVolumeSource)(unsafe.Pointer(in.RBD))
	out.ISCSI = (*core.ISCSIPersistentVolumeSource)(unsafe.Pointer(in.ISCSI))
	out.Cinder = (*core.CinderPersistentVolumeSource)(unsafe.Pointer(in.Cinder))
	out.CephFS = (*core.CephFSPersistentVolumeSource)(unsafe.Pointer(in.CephFS))
	out.FC = (*core.FCVolumeSource)(unsafe.Pointer(in.FC))
	out.Flocker = (*core.FlockerVolumeSource)(unsafe.Pointer(in.Flocker))
	out.FlexVolume = (*core.FlexPersistentVolumeSource)(unsafe.Pointer(in.FlexVolume))
	out.AzureFile = (*core.AzureFilePersistentVolumeSource)(unsafe.Pointer(in.AzureFile))
	out.VsphereVolume = (*core.VsphereVirtualDiskVolumeSource)(unsafe.Pointer(in.VsphereVolume))
	out.Quobyte = (*core.QuobyteVolumeSource)(unsafe.Pointer(in.Quobyte))
	out.AzureDisk = (*core.AzureDiskVolumeSource)(unsafe.Pointer(in.AzureDisk))
	out.PhotonPersistentDisk = (*core.PhotonPersistentDiskVolumeSource)(unsafe.Pointer(in.PhotonPersistentDisk))
	out.PortworxVolume = (*core.PortworxVolumeSource)(unsafe.Pointer(in.PortworxVolume))
	out.ScaleIO = (*core.ScaleIOPersistentVolumeSource)(unsafe.Pointer(in.ScaleIO))
	out.Local = (*core.LocalVolumeSource)(unsafe.Pointer(in.Local))
	out.StorageOS = (*core.StorageOSPersistentVolumeSource)(unsafe.Pointer(in.StorageOS))
	out.CSI = (*core.CSIPersistentVolumeSource)(unsafe.Pointer(in.CSI))
	return nil
}

// Convert_v1_PersistentVolumeSource_To_core_PersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_PersistentVolumeSource_To_core_PersistentVolumeSource(in *corev1.PersistentVolumeSource, out *core.PersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeSource_To_core_PersistentVolumeSource(in, out, s)
}

func autoConvert_core_PersistentVolumeSource_To_v1_PersistentVolumeSource(in *core.PersistentVolumeSource, out *corev1.PersistentVolumeSource, s conversion.Scope) error {
	out.GCEPersistentDisk = (*corev1.GCEPersistentDiskVolumeSource)(unsafe.Pointer(in.GCEPersistentDisk))
	out.AWSElasticBlockStore = (*corev1.AWSElasticBlockStoreVolumeSource)(unsafe.Pointer(in.AWSElasticBlockStore))
	out.HostPath = (*corev1.HostPathVolumeSource)(unsafe.Pointer(in.HostPath))
	out.Glusterfs = (*corev1.GlusterfsPersistentVolumeSource)(unsafe.Pointer(in.Glusterfs))
	out.NFS = (*corev1.NFSVolumeSource)(unsafe.Pointer(in.NFS))
	out.RBD = (*corev1.RBDPersistentVolumeSource)(unsafe.Pointer(in.RBD))
	out.Quobyte = (*corev1.QuobyteVolumeSource)(unsafe.Pointer(in.Quobyte))
	out.ISCSI = (*corev1.ISCSIPersistentVolumeSource)(unsafe.Pointer(in.ISCSI))
	out.FlexVolume = (*corev1.FlexPersistentVolumeSource)(unsafe.Pointer(in.FlexVolume))
	out.Cinder = (*corev1.CinderPersistentVolumeSource)(unsafe.Pointer(in.Cinder))
	out.CephFS = (*corev1.CephFSPersistentVolumeSource)(unsafe.Pointer(in.CephFS))
	out.FC = (*corev1.FCVolumeSource)(unsafe.Pointer(in.FC))
	out.Flocker = (*corev1.FlockerVolumeSource)(unsafe.Pointer(in.Flocker))
	out.AzureFile = (*corev1.AzureFilePersistentVolumeSource)(unsafe.Pointer(in.AzureFile))
	out.VsphereVolume = (*corev1.VsphereVirtualDiskVolumeSource)(unsafe.Pointer(in.VsphereVolume))
	out.AzureDisk = (*corev1.AzureDiskVolumeSource)(unsafe.Pointer(in.AzureDisk))
	out.PhotonPersistentDisk = (*corev1.PhotonPersistentDiskVolumeSource)(unsafe.Pointer(in.PhotonPersistentDisk))
	out.PortworxVolume = (*corev1.PortworxVolumeSource)(unsafe.Pointer(in.PortworxVolume))
	out.ScaleIO = (*corev1.ScaleIOPersistentVolumeSource)(unsafe.Pointer(in.ScaleIO))
	out.Local = (*corev1.LocalVolumeSource)(unsafe.Pointer(in.Local))
	out.StorageOS = (*corev1.StorageOSPersistentVolumeSource)(unsafe.Pointer(in.StorageOS))
	out.CSI = (*corev1.CSIPersistentVolumeSource)(unsafe.Pointer(in.CSI))
	return nil
}

// Convert_core_PersistentVolumeSource_To_v1_PersistentVolumeSource is an autogenerated conversion function.
func Convert_core_PersistentVolumeSource_To_v1_PersistentVolumeSource(in *core.PersistentVolumeSource, out *corev1.PersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeSource_To_v1_PersistentVolumeSource(in, out, s)
}

func autoConvert_v1_PersistentVolumeSpec_To_core_PersistentVolumeSpec(in *corev1.PersistentVolumeSpec, out *core.PersistentVolumeSpec, s conversion.Scope) error {
	out.Capacity = *(*core.ResourceList)(unsafe.Pointer(&in.Capacity))
	if err := Convert_v1_PersistentVolumeSource_To_core_PersistentVolumeSource(&in.PersistentVolumeSource, &out.PersistentVolumeSource, s); err != nil {
		return err
	}
	out.AccessModes = *(*[]core.PersistentVolumeAccessMode)(unsafe.Pointer(&in.AccessModes))
	out.ClaimRef = (*core.ObjectReference)(unsafe.Pointer(in.ClaimRef))
	out.PersistentVolumeReclaimPolicy = core.PersistentVolumeReclaimPolicy(in.PersistentVolumeReclaimPolicy)
	out.StorageClassName = in.StorageClassName
	out.MountOptions = *(*[]string)(unsafe.Pointer(&in.MountOptions))
	out.VolumeMode = (*core.PersistentVolumeMode)(unsafe.Pointer(in.VolumeMode))
	out.NodeAffinity = (*core.VolumeNodeAffinity)(unsafe.Pointer(in.NodeAffinity))
	out.VolumeAttributesClassName = (*string)(unsafe.Pointer(in.VolumeAttributesClassName))
	return nil
}

func autoConvert_core_PersistentVolumeSpec_To_v1_PersistentVolumeSpec(in *core.PersistentVolumeSpec, out *corev1.PersistentVolumeSpec, s conversion.Scope) error {
	out.Capacity = *(*corev1.ResourceList)(unsafe.Pointer(&in.Capacity))
	if err := Convert_core_PersistentVolumeSource_To_v1_PersistentVolumeSource(&in.PersistentVolumeSource, &out.PersistentVolumeSource, s); err != nil {
		return err
	}
	out.AccessModes = *(*[]corev1.PersistentVolumeAccessMode)(unsafe.Pointer(&in.AccessModes))
	out.ClaimRef = (*corev1.ObjectReference)(unsafe.Pointer(in.ClaimRef))
	out.PersistentVolumeReclaimPolicy = corev1.PersistentVolumeReclaimPolicy(in.PersistentVolumeReclaimPolicy)
	out.StorageClassName = in.StorageClassName
	out.MountOptions = *(*[]string)(unsafe.Pointer(&in.MountOptions))
	out.VolumeMode = (*corev1.PersistentVolumeMode)(unsafe.Pointer(in.VolumeMode))
	out.NodeAffinity = (*corev1.VolumeNodeAffinity)(unsafe.Pointer(in.NodeAffinity))
	out.VolumeAttributesClassName = (*string)(unsafe.Pointer(in.VolumeAttributesClassName))
	return nil
}

func autoConvert_v1_PersistentVolumeStatus_To_core_PersistentVolumeStatus(in *corev1.PersistentVolumeStatus, out *core.PersistentVolumeStatus, s conversion.Scope) error {
	out.Phase = core.PersistentVolumePhase(in.Phase)
	out.Message = in.Message
	out.Reason = in.Reason
	out.LastPhaseTransitionTime = (*metav1.Time)(unsafe.Pointer(in.LastPhaseTransitionTime))
	return nil
}

// Convert_v1_PersistentVolumeStatus_To_core_PersistentVolumeStatus is an autogenerated conversion function.
func Convert_v1_PersistentVolumeStatus_To_core_PersistentVolumeStatus(in *corev1.PersistentVolumeStatus, out *core.PersistentVolumeStatus, s conversion.Scope) error {
	return autoConvert_v1_PersistentVolumeStatus_To_core_PersistentVolumeStatus(in, out, s)
}

func autoConvert_core_PersistentVolumeStatus_To_v1_PersistentVolumeStatus(in *core.PersistentVolumeStatus, out *corev1.PersistentVolumeStatus, s conversion.Scope) error {
	out.Phase = corev1.PersistentVolumePhase(in.Phase)
	out.Message = in.Message
	out.Reason = in.Reason
	out.LastPhaseTransitionTime = (*metav1.Time)(unsafe.Pointer(in.LastPhaseTransitionTime))
	return nil
}

// Convert_core_PersistentVolumeStatus_To_v1_PersistentVolumeStatus is an autogenerated conversion function.
func Convert_core_PersistentVolumeStatus_To_v1_PersistentVolumeStatus(in *core.PersistentVolumeStatus, out *corev1.PersistentVolumeStatus, s conversion.Scope) error {
	return autoConvert_core_PersistentVolumeStatus_To_v1_PersistentVolumeStatus(in, out, s)
}

func autoConvert_v1_PhotonPersistentDiskVolumeSource_To_core_PhotonPersistentDiskVolumeSource(in *corev1.PhotonPersistentDiskVolumeSource, out *core.PhotonPersistentDiskVolumeSource, s conversion.Scope) error {
	out.PdID = in.PdID
	out.FSType = in.FSType
	return nil
}

// Convert_v1_PhotonPersistentDiskVolumeSource_To_core_PhotonPersistentDiskVolumeSource is an autogenerated conversion function.
func Convert_v1_PhotonPersistentDiskVolumeSource_To_core_PhotonPersistentDiskVolumeSource(in *corev1.PhotonPersistentDiskVolumeSource, out *core.PhotonPersistentDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_PhotonPersistentDiskVolumeSource_To_core_PhotonPersistentDiskVolumeSource(in, out, s)
}

func autoConvert_core_PhotonPersistentDiskVolumeSource_To_v1_PhotonPersistentDiskVolumeSource(in *core.PhotonPersistentDiskVolumeSource, out *corev1.PhotonPersistentDiskVolumeSource, s conversion.Scope) error {
	out.PdID = in.PdID
	out.FSType = in.FSType
	return nil
}

// Convert_core_PhotonPersistentDiskVolumeSource_To_v1_PhotonPersistentDiskVolumeSource is an autogenerated conversion function.
func Convert_core_PhotonPersistentDiskVolumeSource_To_v1_PhotonPersistentDiskVolumeSource(in *core.PhotonPersistentDiskVolumeSource, out *corev1.PhotonPersistentDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_core_PhotonPersistentDiskVolumeSource_To_v1_PhotonPersistentDiskVolumeSource(in, out, s)
}

func autoConvert_v1_Pod_To_core_Pod(in *corev1.Pod, out *core.Pod, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PodSpec_To_core_PodSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_PodStatus_To_core_PodStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

func autoConvert_core_Pod_To_v1_Pod(in *core.Pod, out *corev1.Pod, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_PodSpec_To_v1_PodSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_PodStatus_To_v1_PodStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1_PodAffinity_To_core_PodAffinity(in *corev1.PodAffinity, out *core.PodAffinity, s conversion.Scope) error {
	out.RequiredDuringSchedulingIgnoredDuringExecution = *(*[]core.PodAffinityTerm)(unsafe.Pointer(&in.RequiredDuringSchedulingIgnoredDuringExecution))
	out.PreferredDuringSchedulingIgnoredDuringExecution = *(*[]core.WeightedPodAffinityTerm)(unsafe.Pointer(&in.PreferredDuringSchedulingIgnoredDuringExecution))
	return nil
}

// Convert_v1_PodAffinity_To_core_PodAffinity is an autogenerated conversion function.
func Convert_v1_PodAffinity_To_core_PodAffinity(in *corev1.PodAffinity, out *core.PodAffinity, s conversion.Scope) error {
	return autoConvert_v1_PodAffinity_To_core_PodAffinity(in, out, s)
}

func autoConvert_core_PodAffinity_To_v1_PodAffinity(in *core.PodAffinity, out *corev1.PodAffinity, s conversion.Scope) error {
	out.RequiredDuringSchedulingIgnoredDuringExecution = *(*[]corev1.PodAffinityTerm)(unsafe.Pointer(&in.RequiredDuringSchedulingIgnoredDuringExecution))
	out.PreferredDuringSchedulingIgnoredDuringExecution = *(*[]corev1.WeightedPodAffinityTerm)(unsafe.Pointer(&in.PreferredDuringSchedulingIgnoredDuringExecution))
	return nil
}

// Convert_core_PodAffinity_To_v1_PodAffinity is an autogenerated conversion function.
func Convert_core_PodAffinity_To_v1_PodAffinity(in *core.PodAffinity, out *corev1.PodAffinity, s conversion.Scope) error {
	return autoConvert_core_PodAffinity_To_v1_PodAffinity(in, out, s)
}

func autoConvert_v1_PodAffinityTerm_To_core_PodAffinityTerm(in *corev1.PodAffinityTerm, out *core.PodAffinityTerm, s conversion.Scope) error {
	out.LabelSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.LabelSelector))
	out.Namespaces = *(*[]string)(unsafe.Pointer(&in.Namespaces))
	out.TopologyKey = in.TopologyKey
	out.NamespaceSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.NamespaceSelector))
	out.MatchLabelKeys = *(*[]string)(unsafe.Pointer(&in.MatchLabelKeys))
	out.MismatchLabelKeys = *(*[]string)(unsafe.Pointer(&in.MismatchLabelKeys))
	return nil
}

// Convert_v1_PodAffinityTerm_To_core_PodAffinityTerm is an autogenerated conversion function.
func Convert_v1_PodAffinityTerm_To_core_PodAffinityTerm(in *corev1.PodAffinityTerm, out *core.PodAffinityTerm, s conversion.Scope) error {
	return autoConvert_v1_PodAffinityTerm_To_core_PodAffinityTerm(in, out, s)
}

func autoConvert_core_PodAffinityTerm_To_v1_PodAffinityTerm(in *core.PodAffinityTerm, out *corev1.PodAffinityTerm, s conversion.Scope) error {
	out.LabelSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.LabelSelector))
	out.Namespaces = *(*[]string)(unsafe.Pointer(&in.Namespaces))
	out.TopologyKey = in.TopologyKey
	out.NamespaceSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.NamespaceSelector))
	out.MatchLabelKeys = *(*[]string)(unsafe.Pointer(&in.MatchLabelKeys))
	out.MismatchLabelKeys = *(*[]string)(unsafe.Pointer(&in.MismatchLabelKeys))
	return nil
}

// Convert_core_PodAffinityTerm_To_v1_PodAffinityTerm is an autogenerated conversion function.
func Convert_core_PodAffinityTerm_To_v1_PodAffinityTerm(in *core.PodAffinityTerm, out *corev1.PodAffinityTerm, s conversion.Scope) error {
	return autoConvert_core_PodAffinityTerm_To_v1_PodAffinityTerm(in, out, s)
}

func autoConvert_v1_PodAntiAffinity_To_core_PodAntiAffinity(in *corev1.PodAntiAffinity, out *core.PodAntiAffinity, s conversion.Scope) error {
	out.RequiredDuringSchedulingIgnoredDuringExecution = *(*[]core.PodAffinityTerm)(unsafe.Pointer(&in.RequiredDuringSchedulingIgnoredDuringExecution))
	out.PreferredDuringSchedulingIgnoredDuringExecution = *(*[]core.WeightedPodAffinityTerm)(unsafe.Pointer(&in.PreferredDuringSchedulingIgnoredDuringExecution))
	return nil
}

// Convert_v1_PodAntiAffinity_To_core_PodAntiAffinity is an autogenerated conversion function.
func Convert_v1_PodAntiAffinity_To_core_PodAntiAffinity(in *corev1.PodAntiAffinity, out *core.PodAntiAffinity, s conversion.Scope) error {
	return autoConvert_v1_PodAntiAffinity_To_core_PodAntiAffinity(in, out, s)
}

func autoConvert_core_PodAntiAffinity_To_v1_PodAntiAffinity(in *core.PodAntiAffinity, out *corev1.PodAntiAffinity, s conversion.Scope) error {
	out.RequiredDuringSchedulingIgnoredDuringExecution = *(*[]corev1.PodAffinityTerm)(unsafe.Pointer(&in.RequiredDuringSchedulingIgnoredDuringExecution))
	out.PreferredDuringSchedulingIgnoredDuringExecution = *(*[]corev1.WeightedPodAffinityTerm)(unsafe.Pointer(&in.PreferredDuringSchedulingIgnoredDuringExecution))
	return nil
}

// Convert_core_PodAntiAffinity_To_v1_PodAntiAffinity is an autogenerated conversion function.
func Convert_core_PodAntiAffinity_To_v1_PodAntiAffinity(in *core.PodAntiAffinity, out *corev1.PodAntiAffinity, s conversion.Scope) error {
	return autoConvert_core_PodAntiAffinity_To_v1_PodAntiAffinity(in, out, s)
}

func autoConvert_v1_PodAttachOptions_To_core_PodAttachOptions(in *corev1.PodAttachOptions, out *core.PodAttachOptions, s conversion.Scope) error {
	out.Stdin = in.Stdin
	out.Stdout = in.Stdout
	out.Stderr = in.Stderr
	out.TTY = in.TTY
	out.Container = in.Container
	return nil
}

// Convert_v1_PodAttachOptions_To_core_PodAttachOptions is an autogenerated conversion function.
func Convert_v1_PodAttachOptions_To_core_PodAttachOptions(in *corev1.PodAttachOptions, out *core.PodAttachOptions, s conversion.Scope) error {
	return autoConvert_v1_PodAttachOptions_To_core_PodAttachOptions(in, out, s)
}

func autoConvert_core_PodAttachOptions_To_v1_PodAttachOptions(in *core.PodAttachOptions, out *corev1.PodAttachOptions, s conversion.Scope) error {
	out.Stdin = in.Stdin
	out.Stdout = in.Stdout
	out.Stderr = in.Stderr
	out.TTY = in.TTY
	out.Container = in.Container
	return nil
}

// Convert_core_PodAttachOptions_To_v1_PodAttachOptions is an autogenerated conversion function.
func Convert_core_PodAttachOptions_To_v1_PodAttachOptions(in *core.PodAttachOptions, out *corev1.PodAttachOptions, s conversion.Scope) error {
	return autoConvert_core_PodAttachOptions_To_v1_PodAttachOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_PodAttachOptions(in *url.Values, out *corev1.PodAttachOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["stdin"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Stdin, s); err != nil {
			return err
		}
	} else {
		out.Stdin = false
	}
	if values, ok := map[string][]string(*in)["stdout"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Stdout, s); err != nil {
			return err
		}
	} else {
		out.Stdout = false
	}
	if values, ok := map[string][]string(*in)["stderr"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Stderr, s); err != nil {
			return err
		}
	} else {
		out.Stderr = false
	}
	if values, ok := map[string][]string(*in)["tty"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.TTY, s); err != nil {
			return err
		}
	} else {
		out.TTY = false
	}
	if values, ok := map[string][]string(*in)["container"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Container, s); err != nil {
			return err
		}
	} else {
		out.Container = ""
	}
	return nil
}

// Convert_url_Values_To_v1_PodAttachOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_PodAttachOptions(in *url.Values, out *corev1.PodAttachOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_PodAttachOptions(in, out, s)
}

func autoConvert_v1_PodCertificateProjection_To_core_PodCertificateProjection(in *corev1.PodCertificateProjection, out *core.PodCertificateProjection, s conversion.Scope) error {
	out.SignerName = in.SignerName
	out.KeyType = in.KeyType
	out.MaxExpirationSeconds = (*int32)(unsafe.Pointer(in.MaxExpirationSeconds))
	out.CredentialBundlePath = in.CredentialBundlePath
	out.KeyPath = in.KeyPath
	out.CertificateChainPath = in.CertificateChainPath
	return nil
}

// Convert_v1_PodCertificateProjection_To_core_PodCertificateProjection is an autogenerated conversion function.
func Convert_v1_PodCertificateProjection_To_core_PodCertificateProjection(in *corev1.PodCertificateProjection, out *core.PodCertificateProjection, s conversion.Scope) error {
	return autoConvert_v1_PodCertificateProjection_To_core_PodCertificateProjection(in, out, s)
}

func autoConvert_core_PodCertificateProjection_To_v1_PodCertificateProjection(in *core.PodCertificateProjection, out *corev1.PodCertificateProjection, s conversion.Scope) error {
	out.SignerName = in.SignerName
	out.KeyType = in.KeyType
	out.MaxExpirationSeconds = (*int32)(unsafe.Pointer(in.MaxExpirationSeconds))
	out.CredentialBundlePath = in.CredentialBundlePath
	out.KeyPath = in.KeyPath
	out.CertificateChainPath = in.CertificateChainPath
	return nil
}

// Convert_core_PodCertificateProjection_To_v1_PodCertificateProjection is an autogenerated conversion function.
func Convert_core_PodCertificateProjection_To_v1_PodCertificateProjection(in *core.PodCertificateProjection, out *corev1.PodCertificateProjection, s conversion.Scope) error {
	return autoConvert_core_PodCertificateProjection_To_v1_PodCertificateProjection(in, out, s)
}

func autoConvert_v1_PodCondition_To_core_PodCondition(in *corev1.PodCondition, out *core.PodCondition, s conversion.Scope) error {
	out.Type = core.PodConditionType(in.Type)
	out.ObservedGeneration = in.ObservedGeneration
	out.Status = core.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_PodCondition_To_core_PodCondition is an autogenerated conversion function.
func Convert_v1_PodCondition_To_core_PodCondition(in *corev1.PodCondition, out *core.PodCondition, s conversion.Scope) error {
	return autoConvert_v1_PodCondition_To_core_PodCondition(in, out, s)
}

func autoConvert_core_PodCondition_To_v1_PodCondition(in *core.PodCondition, out *corev1.PodCondition, s conversion.Scope) error {
	out.Type = corev1.PodConditionType(in.Type)
	out.ObservedGeneration = in.ObservedGeneration
	out.Status = corev1.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_core_PodCondition_To_v1_PodCondition is an autogenerated conversion function.
func Convert_core_PodCondition_To_v1_PodCondition(in *core.PodCondition, out *corev1.PodCondition, s conversion.Scope) error {
	return autoConvert_core_PodCondition_To_v1_PodCondition(in, out, s)
}

func autoConvert_v1_PodDNSConfig_To_core_PodDNSConfig(in *corev1.PodDNSConfig, out *core.PodDNSConfig, s conversion.Scope) error {
	out.Nameservers = *(*[]string)(unsafe.Pointer(&in.Nameservers))
	out.Searches = *(*[]string)(unsafe.Pointer(&in.Searches))
	out.Options = *(*[]core.PodDNSConfigOption)(unsafe.Pointer(&in.Options))
	return nil
}

// Convert_v1_PodDNSConfig_To_core_PodDNSConfig is an autogenerated conversion function.
func Convert_v1_PodDNSConfig_To_core_PodDNSConfig(in *corev1.PodDNSConfig, out *core.PodDNSConfig, s conversion.Scope) error {
	return autoConvert_v1_PodDNSConfig_To_core_PodDNSConfig(in, out, s)
}

func autoConvert_core_PodDNSConfig_To_v1_PodDNSConfig(in *core.PodDNSConfig, out *corev1.PodDNSConfig, s conversion.Scope) error {
	out.Nameservers = *(*[]string)(unsafe.Pointer(&in.Nameservers))
	out.Searches = *(*[]string)(unsafe.Pointer(&in.Searches))
	out.Options = *(*[]corev1.PodDNSConfigOption)(unsafe.Pointer(&in.Options))
	return nil
}

// Convert_core_PodDNSConfig_To_v1_PodDNSConfig is an autogenerated conversion function.
func Convert_core_PodDNSConfig_To_v1_PodDNSConfig(in *core.PodDNSConfig, out *corev1.PodDNSConfig, s conversion.Scope) error {
	return autoConvert_core_PodDNSConfig_To_v1_PodDNSConfig(in, out, s)
}

func autoConvert_v1_PodDNSConfigOption_To_core_PodDNSConfigOption(in *corev1.PodDNSConfigOption, out *core.PodDNSConfigOption, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = (*string)(unsafe.Pointer(in.Value))
	return nil
}

// Convert_v1_PodDNSConfigOption_To_core_PodDNSConfigOption is an autogenerated conversion function.
func Convert_v1_PodDNSConfigOption_To_core_PodDNSConfigOption(in *corev1.PodDNSConfigOption, out *core.PodDNSConfigOption, s conversion.Scope) error {
	return autoConvert_v1_PodDNSConfigOption_To_core_PodDNSConfigOption(in, out, s)
}

func autoConvert_core_PodDNSConfigOption_To_v1_PodDNSConfigOption(in *core.PodDNSConfigOption, out *corev1.PodDNSConfigOption, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = (*string)(unsafe.Pointer(in.Value))
	return nil
}

// Convert_core_PodDNSConfigOption_To_v1_PodDNSConfigOption is an autogenerated conversion function.
func Convert_core_PodDNSConfigOption_To_v1_PodDNSConfigOption(in *core.PodDNSConfigOption, out *corev1.PodDNSConfigOption, s conversion.Scope) error {
	return autoConvert_core_PodDNSConfigOption_To_v1_PodDNSConfigOption(in, out, s)
}

func autoConvert_v1_PodExecOptions_To_core_PodExecOptions(in *corev1.PodExecOptions, out *core.PodExecOptions, s conversion.Scope) error {
	out.Stdin = in.Stdin
	out.Stdout = in.Stdout
	out.Stderr = in.Stderr
	out.TTY = in.TTY
	out.Container = in.Container
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	return nil
}

// Convert_v1_PodExecOptions_To_core_PodExecOptions is an autogenerated conversion function.
func Convert_v1_PodExecOptions_To_core_PodExecOptions(in *corev1.PodExecOptions, out *core.PodExecOptions, s conversion.Scope) error {
	return autoConvert_v1_PodExecOptions_To_core_PodExecOptions(in, out, s)
}

func autoConvert_core_PodExecOptions_To_v1_PodExecOptions(in *core.PodExecOptions, out *corev1.PodExecOptions, s conversion.Scope) error {
	out.Stdin = in.Stdin
	out.Stdout = in.Stdout
	out.Stderr = in.Stderr
	out.TTY = in.TTY
	out.Container = in.Container
	out.Command = *(*[]string)(unsafe.Pointer(&in.Command))
	return nil
}

// Convert_core_PodExecOptions_To_v1_PodExecOptions is an autogenerated conversion function.
func Convert_core_PodExecOptions_To_v1_PodExecOptions(in *core.PodExecOptions, out *corev1.PodExecOptions, s conversion.Scope) error {
	return autoConvert_core_PodExecOptions_To_v1_PodExecOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_PodExecOptions(in *url.Values, out *corev1.PodExecOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["stdin"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Stdin, s); err != nil {
			return err
		}
	} else {
		out.Stdin = false
	}
	if values, ok := map[string][]string(*in)["stdout"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Stdout, s); err != nil {
			return err
		}
	} else {
		out.Stdout = false
	}
	if values, ok := map[string][]string(*in)["stderr"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Stderr, s); err != nil {
			return err
		}
	} else {
		out.Stderr = false
	}
	if values, ok := map[string][]string(*in)["tty"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.TTY, s); err != nil {
			return err
		}
	} else {
		out.TTY = false
	}
	if values, ok := map[string][]string(*in)["container"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Container, s); err != nil {
			return err
		}
	} else {
		out.Container = ""
	}
	if values, ok := map[string][]string(*in)["command"]; ok && len(values) > 0 {
		out.Command = *(*[]string)(unsafe.Pointer(&values))
	} else {
		out.Command = nil
	}
	return nil
}

// Convert_url_Values_To_v1_PodExecOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_PodExecOptions(in *url.Values, out *corev1.PodExecOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_PodExecOptions(in, out, s)
}

func autoConvert_v1_PodIP_To_core_PodIP(in *corev1.PodIP, out *core.PodIP, s conversion.Scope) error {
	out.IP = in.IP
	return nil
}

// Convert_v1_PodIP_To_core_PodIP is an autogenerated conversion function.
func Convert_v1_PodIP_To_core_PodIP(in *corev1.PodIP, out *core.PodIP, s conversion.Scope) error {
	return autoConvert_v1_PodIP_To_core_PodIP(in, out, s)
}

func autoConvert_core_PodIP_To_v1_PodIP(in *core.PodIP, out *corev1.PodIP, s conversion.Scope) error {
	out.IP = in.IP
	return nil
}

// Convert_core_PodIP_To_v1_PodIP is an autogenerated conversion function.
func Convert_core_PodIP_To_v1_PodIP(in *core.PodIP, out *corev1.PodIP, s conversion.Scope) error {
	return autoConvert_core_PodIP_To_v1_PodIP(in, out, s)
}

func autoConvert_v1_PodList_To_core_PodList(in *corev1.PodList, out *core.PodList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]core.Pod, len(*in))
		for i := range *in {
			if err := Convert_v1_Pod_To_core_Pod(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_PodList_To_core_PodList is an autogenerated conversion function.
func Convert_v1_PodList_To_core_PodList(in *corev1.PodList, out *core.PodList, s conversion.Scope) error {
	return autoConvert_v1_PodList_To_core_PodList(in, out, s)
}

func autoConvert_core_PodList_To_v1_PodList(in *core.PodList, out *corev1.PodList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]corev1.Pod, len(*in))
		for i := range *in {
			if err := Convert_core_Pod_To_v1_Pod(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_PodList_To_v1_PodList is an autogenerated conversion function.
func Convert_core_PodList_To_v1_PodList(in *core.PodList, out *corev1.PodList, s conversion.Scope) error {
	return autoConvert_core_PodList_To_v1_PodList(in, out, s)
}

func autoConvert_v1_PodLogOptions_To_core_PodLogOptions(in *corev1.PodLogOptions, out *core.PodLogOptions, s conversion.Scope) error {
	out.Container = in.Container
	out.Follow = in.Follow
	out.Previous = in.Previous
	out.SinceSeconds = (*int64)(unsafe.Pointer(in.SinceSeconds))
	out.SinceTime = (*metav1.Time)(unsafe.Pointer(in.SinceTime))
	out.Timestamps = in.Timestamps
	out.TailLines = (*int64)(unsafe.Pointer(in.TailLines))
	out.LimitBytes = (*int64)(unsafe.Pointer(in.LimitBytes))
	out.InsecureSkipTLSVerifyBackend = in.InsecureSkipTLSVerifyBackend
	out.Stream = (*string)(unsafe.Pointer(in.Stream))
	return nil
}

// Convert_v1_PodLogOptions_To_core_PodLogOptions is an autogenerated conversion function.
func Convert_v1_PodLogOptions_To_core_PodLogOptions(in *corev1.PodLogOptions, out *core.PodLogOptions, s conversion.Scope) error {
	return autoConvert_v1_PodLogOptions_To_core_PodLogOptions(in, out, s)
}

func autoConvert_core_PodLogOptions_To_v1_PodLogOptions(in *core.PodLogOptions, out *corev1.PodLogOptions, s conversion.Scope) error {
	out.Container = in.Container
	out.Follow = in.Follow
	out.Previous = in.Previous
	out.SinceSeconds = (*int64)(unsafe.Pointer(in.SinceSeconds))
	out.SinceTime = (*metav1.Time)(unsafe.Pointer(in.SinceTime))
	out.Timestamps = in.Timestamps
	out.TailLines = (*int64)(unsafe.Pointer(in.TailLines))
	out.LimitBytes = (*int64)(unsafe.Pointer(in.LimitBytes))
	out.InsecureSkipTLSVerifyBackend = in.InsecureSkipTLSVerifyBackend
	out.Stream = (*string)(unsafe.Pointer(in.Stream))
	return nil
}

// Convert_core_PodLogOptions_To_v1_PodLogOptions is an autogenerated conversion function.
func Convert_core_PodLogOptions_To_v1_PodLogOptions(in *core.PodLogOptions, out *corev1.PodLogOptions, s conversion.Scope) error {
	return autoConvert_core_PodLogOptions_To_v1_PodLogOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_PodLogOptions(in *url.Values, out *corev1.PodLogOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["container"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Container, s); err != nil {
			return err
		}
	} else {
		out.Container = ""
	}
	if values, ok := map[string][]string(*in)["follow"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Follow, s); err != nil {
			return err
		}
	} else {
		out.Follow = false
	}
	if values, ok := map[string][]string(*in)["previous"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Previous, s); err != nil {
			return err
		}
	} else {
		out.Previous = false
	}
	if values, ok := map[string][]string(*in)["sinceSeconds"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_Pointer_int64(&values, &out.SinceSeconds, s); err != nil {
			return err
		}
	} else {
		out.SinceSeconds = nil
	}
	if values, ok := map[string][]string(*in)["sinceTime"]; ok && len(values) > 0 {
		if err := metav1.Convert_Slice_string_To_Pointer_v1_Time(&values, &out.SinceTime, s); err != nil {
			return err
		}
	} else {
		out.SinceTime = nil
	}
	if values, ok := map[string][]string(*in)["timestamps"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.Timestamps, s); err != nil {
			return err
		}
	} else {
		out.Timestamps = false
	}
	if values, ok := map[string][]string(*in)["tailLines"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_Pointer_int64(&values, &out.TailLines, s); err != nil {
			return err
		}
	} else {
		out.TailLines = nil
	}
	if values, ok := map[string][]string(*in)["limitBytes"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_Pointer_int64(&values, &out.LimitBytes, s); err != nil {
			return err
		}
	} else {
		out.LimitBytes = nil
	}
	if values, ok := map[string][]string(*in)["insecureSkipTLSVerifyBackend"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.InsecureSkipTLSVerifyBackend, s); err != nil {
			return err
		}
	} else {
		out.InsecureSkipTLSVerifyBackend = false
	}
	if values, ok := map[string][]string(*in)["stream"]; ok && len(values) > 0 {
		if err := Convert_Slice_string_To_Pointer_string(&values, &out.Stream, s); err != nil {
			return err
		}
	} else {
		out.Stream = nil
	}
	return nil
}

// Convert_url_Values_To_v1_PodLogOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_PodLogOptions(in *url.Values, out *corev1.PodLogOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_PodLogOptions(in, out, s)
}

func autoConvert_v1_PodOS_To_core_PodOS(in *corev1.PodOS, out *core.PodOS, s conversion.Scope) error {
	out.Name = core.OSName(in.Name)
	return nil
}

// Convert_v1_PodOS_To_core_PodOS is an autogenerated conversion function.
func Convert_v1_PodOS_To_core_PodOS(in *corev1.PodOS, out *core.PodOS, s conversion.Scope) error {
	return autoConvert_v1_PodOS_To_core_PodOS(in, out, s)
}

func autoConvert_core_PodOS_To_v1_PodOS(in *core.PodOS, out *corev1.PodOS, s conversion.Scope) error {
	out.Name = corev1.OSName(in.Name)
	return nil
}

// Convert_core_PodOS_To_v1_PodOS is an autogenerated conversion function.
func Convert_core_PodOS_To_v1_PodOS(in *core.PodOS, out *corev1.PodOS, s conversion.Scope) error {
	return autoConvert_core_PodOS_To_v1_PodOS(in, out, s)
}

func autoConvert_v1_PodPortForwardOptions_To_core_PodPortForwardOptions(in *corev1.PodPortForwardOptions, out *core.PodPortForwardOptions, s conversion.Scope) error {
	out.Ports = *(*[]int32)(unsafe.Pointer(&in.Ports))
	return nil
}

// Convert_v1_PodPortForwardOptions_To_core_PodPortForwardOptions is an autogenerated conversion function.
func Convert_v1_PodPortForwardOptions_To_core_PodPortForwardOptions(in *corev1.PodPortForwardOptions, out *core.PodPortForwardOptions, s conversion.Scope) error {
	return autoConvert_v1_PodPortForwardOptions_To_core_PodPortForwardOptions(in, out, s)
}

func autoConvert_core_PodPortForwardOptions_To_v1_PodPortForwardOptions(in *core.PodPortForwardOptions, out *corev1.PodPortForwardOptions, s conversion.Scope) error {
	out.Ports = *(*[]int32)(unsafe.Pointer(&in.Ports))
	return nil
}

// Convert_core_PodPortForwardOptions_To_v1_PodPortForwardOptions is an autogenerated conversion function.
func Convert_core_PodPortForwardOptions_To_v1_PodPortForwardOptions(in *core.PodPortForwardOptions, out *corev1.PodPortForwardOptions, s conversion.Scope) error {
	return autoConvert_core_PodPortForwardOptions_To_v1_PodPortForwardOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_PodPortForwardOptions(in *url.Values, out *corev1.PodPortForwardOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["ports"]; ok && len(values) > 0 {
		if err := metav1.Convert_Slice_string_To_Slice_int32(&values, &out.Ports, s); err != nil {
			return err
		}
	} else {
		out.Ports = nil
	}
	return nil
}

// Convert_url_Values_To_v1_PodPortForwardOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_PodPortForwardOptions(in *url.Values, out *corev1.PodPortForwardOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_PodPortForwardOptions(in, out, s)
}

func autoConvert_v1_PodProxyOptions_To_core_PodProxyOptions(in *corev1.PodProxyOptions, out *core.PodProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_v1_PodProxyOptions_To_core_PodProxyOptions is an autogenerated conversion function.
func Convert_v1_PodProxyOptions_To_core_PodProxyOptions(in *corev1.PodProxyOptions, out *core.PodProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_PodProxyOptions_To_core_PodProxyOptions(in, out, s)
}

func autoConvert_core_PodProxyOptions_To_v1_PodProxyOptions(in *core.PodProxyOptions, out *corev1.PodProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_core_PodProxyOptions_To_v1_PodProxyOptions is an autogenerated conversion function.
func Convert_core_PodProxyOptions_To_v1_PodProxyOptions(in *core.PodProxyOptions, out *corev1.PodProxyOptions, s conversion.Scope) error {
	return autoConvert_core_PodProxyOptions_To_v1_PodProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_PodProxyOptions(in *url.Values, out *corev1.PodProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["path"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Path, s); err != nil {
			return err
		}
	} else {
		out.Path = ""
	}
	return nil
}

// Convert_url_Values_To_v1_PodProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_PodProxyOptions(in *url.Values, out *corev1.PodProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_PodProxyOptions(in, out, s)
}

func autoConvert_v1_PodReadinessGate_To_core_PodReadinessGate(in *corev1.PodReadinessGate, out *core.PodReadinessGate, s conversion.Scope) error {
	out.ConditionType = core.PodConditionType(in.ConditionType)
	return nil
}

// Convert_v1_PodReadinessGate_To_core_PodReadinessGate is an autogenerated conversion function.
func Convert_v1_PodReadinessGate_To_core_PodReadinessGate(in *corev1.PodReadinessGate, out *core.PodReadinessGate, s conversion.Scope) error {
	return autoConvert_v1_PodReadinessGate_To_core_PodReadinessGate(in, out, s)
}

func autoConvert_core_PodReadinessGate_To_v1_PodReadinessGate(in *core.PodReadinessGate, out *corev1.PodReadinessGate, s conversion.Scope) error {
	out.ConditionType = corev1.PodConditionType(in.ConditionType)
	return nil
}

// Convert_core_PodReadinessGate_To_v1_PodReadinessGate is an autogenerated conversion function.
func Convert_core_PodReadinessGate_To_v1_PodReadinessGate(in *core.PodReadinessGate, out *corev1.PodReadinessGate, s conversion.Scope) error {
	return autoConvert_core_PodReadinessGate_To_v1_PodReadinessGate(in, out, s)
}

func autoConvert_v1_PodResourceClaim_To_core_PodResourceClaim(in *corev1.PodResourceClaim, out *core.PodResourceClaim, s conversion.Scope) error {
	out.Name = in.Name
	out.ResourceClaimName = (*string)(unsafe.Pointer(in.ResourceClaimName))
	out.ResourceClaimTemplateName = (*string)(unsafe.Pointer(in.ResourceClaimTemplateName))
	return nil
}

// Convert_v1_PodResourceClaim_To_core_PodResourceClaim is an autogenerated conversion function.
func Convert_v1_PodResourceClaim_To_core_PodResourceClaim(in *corev1.PodResourceClaim, out *core.PodResourceClaim, s conversion.Scope) error {
	return autoConvert_v1_PodResourceClaim_To_core_PodResourceClaim(in, out, s)
}

func autoConvert_core_PodResourceClaim_To_v1_PodResourceClaim(in *core.PodResourceClaim, out *corev1.PodResourceClaim, s conversion.Scope) error {
	out.Name = in.Name
	out.ResourceClaimName = (*string)(unsafe.Pointer(in.ResourceClaimName))
	out.ResourceClaimTemplateName = (*string)(unsafe.Pointer(in.ResourceClaimTemplateName))
	return nil
}

// Convert_core_PodResourceClaim_To_v1_PodResourceClaim is an autogenerated conversion function.
func Convert_core_PodResourceClaim_To_v1_PodResourceClaim(in *core.PodResourceClaim, out *corev1.PodResourceClaim, s conversion.Scope) error {
	return autoConvert_core_PodResourceClaim_To_v1_PodResourceClaim(in, out, s)
}

func autoConvert_v1_PodResourceClaimStatus_To_core_PodResourceClaimStatus(in *corev1.PodResourceClaimStatus, out *core.PodResourceClaimStatus, s conversion.Scope) error {
	out.Name = in.Name
	out.ResourceClaimName = (*string)(unsafe.Pointer(in.ResourceClaimName))
	return nil
}

// Convert_v1_PodResourceClaimStatus_To_core_PodResourceClaimStatus is an autogenerated conversion function.
func Convert_v1_PodResourceClaimStatus_To_core_PodResourceClaimStatus(in *corev1.PodResourceClaimStatus, out *core.PodResourceClaimStatus, s conversion.Scope) error {
	return autoConvert_v1_PodResourceClaimStatus_To_core_PodResourceClaimStatus(in, out, s)
}

func autoConvert_core_PodResourceClaimStatus_To_v1_PodResourceClaimStatus(in *core.PodResourceClaimStatus, out *corev1.PodResourceClaimStatus, s conversion.Scope) error {
	out.Name = in.Name
	out.ResourceClaimName = (*string)(unsafe.Pointer(in.ResourceClaimName))
	return nil
}

// Convert_core_PodResourceClaimStatus_To_v1_PodResourceClaimStatus is an autogenerated conversion function.
func Convert_core_PodResourceClaimStatus_To_v1_PodResourceClaimStatus(in *core.PodResourceClaimStatus, out *corev1.PodResourceClaimStatus, s conversion.Scope) error {
	return autoConvert_core_PodResourceClaimStatus_To_v1_PodResourceClaimStatus(in, out, s)
}

func autoConvert_v1_PodSchedulingGate_To_core_PodSchedulingGate(in *corev1.PodSchedulingGate, out *core.PodSchedulingGate, s conversion.Scope) error {
	out.Name = in.Name
	return nil
}

// Convert_v1_PodSchedulingGate_To_core_PodSchedulingGate is an autogenerated conversion function.
func Convert_v1_PodSchedulingGate_To_core_PodSchedulingGate(in *corev1.PodSchedulingGate, out *core.PodSchedulingGate, s conversion.Scope) error {
	return autoConvert_v1_PodSchedulingGate_To_core_PodSchedulingGate(in, out, s)
}

func autoConvert_core_PodSchedulingGate_To_v1_PodSchedulingGate(in *core.PodSchedulingGate, out *corev1.PodSchedulingGate, s conversion.Scope) error {
	out.Name = in.Name
	return nil
}

// Convert_core_PodSchedulingGate_To_v1_PodSchedulingGate is an autogenerated conversion function.
func Convert_core_PodSchedulingGate_To_v1_PodSchedulingGate(in *core.PodSchedulingGate, out *corev1.PodSchedulingGate, s conversion.Scope) error {
	return autoConvert_core_PodSchedulingGate_To_v1_PodSchedulingGate(in, out, s)
}

func autoConvert_v1_PodSecurityContext_To_core_PodSecurityContext(in *corev1.PodSecurityContext, out *core.PodSecurityContext, s conversion.Scope) error {
	out.SELinuxOptions = (*core.SELinuxOptions)(unsafe.Pointer(in.SELinuxOptions))
	out.WindowsOptions = (*core.WindowsSecurityContextOptions)(unsafe.Pointer(in.WindowsOptions))
	out.RunAsUser = (*int64)(unsafe.Pointer(in.RunAsUser))
	out.RunAsGroup = (*int64)(unsafe.Pointer(in.RunAsGroup))
	out.RunAsNonRoot = (*bool)(unsafe.Pointer(in.RunAsNonRoot))
	out.SupplementalGroups = *(*[]int64)(unsafe.Pointer(&in.SupplementalGroups))
	out.SupplementalGroupsPolicy = (*core.SupplementalGroupsPolicy)(unsafe.Pointer(in.SupplementalGroupsPolicy))
	out.FSGroup = (*int64)(unsafe.Pointer(in.FSGroup))
	out.Sysctls = *(*[]core.Sysctl)(unsafe.Pointer(&in.Sysctls))
	out.FSGroupChangePolicy = (*core.PodFSGroupChangePolicy)(unsafe.Pointer(in.FSGroupChangePolicy))
	out.SeccompProfile = (*core.SeccompProfile)(unsafe.Pointer(in.SeccompProfile))
	out.AppArmorProfile = (*core.AppArmorProfile)(unsafe.Pointer(in.AppArmorProfile))
	out.SELinuxChangePolicy = (*core.PodSELinuxChangePolicy)(unsafe.Pointer(in.SELinuxChangePolicy))
	return nil
}

// Convert_v1_PodSecurityContext_To_core_PodSecurityContext is an autogenerated conversion function.
func Convert_v1_PodSecurityContext_To_core_PodSecurityContext(in *corev1.PodSecurityContext, out *core.PodSecurityContext, s conversion.Scope) error {
	return autoConvert_v1_PodSecurityContext_To_core_PodSecurityContext(in, out, s)
}

func autoConvert_core_PodSecurityContext_To_v1_PodSecurityContext(in *core.PodSecurityContext, out *corev1.PodSecurityContext, s conversion.Scope) error {
	// INFO: in.HostNetwork opted out of conversion generation
	// INFO: in.HostPID opted out of conversion generation
	// INFO: in.HostIPC opted out of conversion generation
	// INFO: in.ShareProcessNamespace opted out of conversion generation
	// INFO: in.HostUsers opted out of conversion generation
	out.SELinuxOptions = (*corev1.SELinuxOptions)(unsafe.Pointer(in.SELinuxOptions))
	out.WindowsOptions = (*corev1.WindowsSecurityContextOptions)(unsafe.Pointer(in.WindowsOptions))
	out.RunAsUser = (*int64)(unsafe.Pointer(in.RunAsUser))
	out.RunAsGroup = (*int64)(unsafe.Pointer(in.RunAsGroup))
	out.RunAsNonRoot = (*bool)(unsafe.Pointer(in.RunAsNonRoot))
	out.SupplementalGroups = *(*[]int64)(unsafe.Pointer(&in.SupplementalGroups))
	out.SupplementalGroupsPolicy = (*corev1.SupplementalGroupsPolicy)(unsafe.Pointer(in.SupplementalGroupsPolicy))
	out.FSGroup = (*int64)(unsafe.Pointer(in.FSGroup))
	out.FSGroupChangePolicy = (*corev1.PodFSGroupChangePolicy)(unsafe.Pointer(in.FSGroupChangePolicy))
	out.Sysctls = *(*[]corev1.Sysctl)(unsafe.Pointer(&in.Sysctls))
	out.SeccompProfile = (*corev1.SeccompProfile)(unsafe.Pointer(in.SeccompProfile))
	out.AppArmorProfile = (*corev1.AppArmorProfile)(unsafe.Pointer(in.AppArmorProfile))
	out.SELinuxChangePolicy = (*corev1.PodSELinuxChangePolicy)(unsafe.Pointer(in.SELinuxChangePolicy))
	return nil
}

// Convert_core_PodSecurityContext_To_v1_PodSecurityContext is an autogenerated conversion function.
func Convert_core_PodSecurityContext_To_v1_PodSecurityContext(in *core.PodSecurityContext, out *corev1.PodSecurityContext, s conversion.Scope) error {
	return autoConvert_core_PodSecurityContext_To_v1_PodSecurityContext(in, out, s)
}

func autoConvert_v1_PodSignature_To_core_PodSignature(in *corev1.PodSignature, out *core.PodSignature, s conversion.Scope) error {
	out.PodController = (*metav1.OwnerReference)(unsafe.Pointer(in.PodController))
	return nil
}

// Convert_v1_PodSignature_To_core_PodSignature is an autogenerated conversion function.
func Convert_v1_PodSignature_To_core_PodSignature(in *corev1.PodSignature, out *core.PodSignature, s conversion.Scope) error {
	return autoConvert_v1_PodSignature_To_core_PodSignature(in, out, s)
}

func autoConvert_core_PodSignature_To_v1_PodSignature(in *core.PodSignature, out *corev1.PodSignature, s conversion.Scope) error {
	out.PodController = (*metav1.OwnerReference)(unsafe.Pointer(in.PodController))
	return nil
}

// Convert_core_PodSignature_To_v1_PodSignature is an autogenerated conversion function.
func Convert_core_PodSignature_To_v1_PodSignature(in *core.PodSignature, out *corev1.PodSignature, s conversion.Scope) error {
	return autoConvert_core_PodSignature_To_v1_PodSignature(in, out, s)
}

func autoConvert_v1_PodSpec_To_core_PodSpec(in *corev1.PodSpec, out *core.PodSpec, s conversion.Scope) error {
	if in.Volumes != nil {
		in, out := &in.Volumes, &out.Volumes
		*out = make([]core.Volume, len(*in))
		for i := range *in {
			if err := Convert_v1_Volume_To_core_Volume(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Volumes = nil
	}
	out.InitContainers = *(*[]core.Container)(unsafe.Pointer(&in.InitContainers))
	out.Containers = *(*[]core.Container)(unsafe.Pointer(&in.Containers))
	out.EphemeralContainers = *(*[]core.EphemeralContainer)(unsafe.Pointer(&in.EphemeralContainers))
	out.RestartPolicy = core.RestartPolicy(in.RestartPolicy)
	out.TerminationGracePeriodSeconds = (*int64)(unsafe.Pointer(in.TerminationGracePeriodSeconds))
	out.ActiveDeadlineSeconds = (*int64)(unsafe.Pointer(in.ActiveDeadlineSeconds))
	out.DNSPolicy = core.DNSPolicy(in.DNSPolicy)
	out.NodeSelector = *(*map[string]string)(unsafe.Pointer(&in.NodeSelector))
	out.ServiceAccountName = in.ServiceAccountName
	// INFO: in.DeprecatedServiceAccount opted out of conversion generation
	out.AutomountServiceAccountToken = (*bool)(unsafe.Pointer(in.AutomountServiceAccountToken))
	out.NodeName = in.NodeName
	// INFO: in.HostNetwork opted out of conversion generation
	// INFO: in.HostPID opted out of conversion generation
	// INFO: in.HostIPC opted out of conversion generation
	// INFO: in.ShareProcessNamespace opted out of conversion generation
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(core.PodSecurityContext)
		if err := Convert_v1_PodSecurityContext_To_core_PodSecurityContext(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.SecurityContext = nil
	}
	out.ImagePullSecrets = *(*[]core.LocalObjectReference)(unsafe.Pointer(&in.ImagePullSecrets))
	out.Hostname = in.Hostname
	out.Subdomain = in.Subdomain
	out.Affinity = (*core.Affinity)(unsafe.Pointer(in.Affinity))
	out.SchedulerName = in.SchedulerName
	out.Tolerations = *(*[]core.Toleration)(unsafe.Pointer(&in.Tolerations))
	out.HostAliases = *(*[]core.HostAlias)(unsafe.Pointer(&in.HostAliases))
	out.PriorityClassName = in.PriorityClassName
	out.Priority = (*int32)(unsafe.Pointer(in.Priority))
	out.DNSConfig = (*core.PodDNSConfig)(unsafe.Pointer(in.DNSConfig))
	out.ReadinessGates = *(*[]core.PodReadinessGate)(unsafe.Pointer(&in.ReadinessGates))
	out.RuntimeClassName = (*string)(unsafe.Pointer(in.RuntimeClassName))
	out.EnableServiceLinks = (*bool)(unsafe.Pointer(in.EnableServiceLinks))
	out.PreemptionPolicy = (*core.PreemptionPolicy)(unsafe.Pointer(in.PreemptionPolicy))
	out.Overhead = *(*core.ResourceList)(unsafe.Pointer(&in.Overhead))
	out.TopologySpreadConstraints = *(*[]core.TopologySpreadConstraint)(unsafe.Pointer(&in.TopologySpreadConstraints))
	out.SetHostnameAsFQDN = (*bool)(unsafe.Pointer(in.SetHostnameAsFQDN))
	out.OS = (*core.PodOS)(unsafe.Pointer(in.OS))
	// INFO: in.HostUsers opted out of conversion generation
	out.SchedulingGates = *(*[]core.PodSchedulingGate)(unsafe.Pointer(&in.SchedulingGates))
	out.ResourceClaims = *(*[]core.PodResourceClaim)(unsafe.Pointer(&in.ResourceClaims))
	out.Resources = (*core.ResourceRequirements)(unsafe.Pointer(in.Resources))
	out.HostnameOverride = (*string)(unsafe.Pointer(in.HostnameOverride))
	return nil
}

func autoConvert_core_PodSpec_To_v1_PodSpec(in *core.PodSpec, out *corev1.PodSpec, s conversion.Scope) error {
	if in.Volumes != nil {
		in, out := &in.Volumes, &out.Volumes
		*out = make([]corev1.Volume, len(*in))
		for i := range *in {
			if err := Convert_core_Volume_To_v1_Volume(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Volumes = nil
	}
	out.InitContainers = *(*[]corev1.Container)(unsafe.Pointer(&in.InitContainers))
	out.Containers = *(*[]corev1.Container)(unsafe.Pointer(&in.Containers))
	out.EphemeralContainers = *(*[]corev1.EphemeralContainer)(unsafe.Pointer(&in.EphemeralContainers))
	out.RestartPolicy = corev1.RestartPolicy(in.RestartPolicy)
	out.TerminationGracePeriodSeconds = (*int64)(unsafe.Pointer(in.TerminationGracePeriodSeconds))
	out.ActiveDeadlineSeconds = (*int64)(unsafe.Pointer(in.ActiveDeadlineSeconds))
	out.DNSPolicy = corev1.DNSPolicy(in.DNSPolicy)
	out.NodeSelector = *(*map[string]string)(unsafe.Pointer(&in.NodeSelector))
	out.ServiceAccountName = in.ServiceAccountName
	out.AutomountServiceAccountToken = (*bool)(unsafe.Pointer(in.AutomountServiceAccountToken))
	out.NodeName = in.NodeName
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(corev1.PodSecurityContext)
		if err := Convert_core_PodSecurityContext_To_v1_PodSecurityContext(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.SecurityContext = nil
	}
	out.ImagePullSecrets = *(*[]corev1.LocalObjectReference)(unsafe.Pointer(&in.ImagePullSecrets))
	out.Hostname = in.Hostname
	out.Subdomain = in.Subdomain
	out.SetHostnameAsFQDN = (*bool)(unsafe.Pointer(in.SetHostnameAsFQDN))
	out.Affinity = (*corev1.Affinity)(unsafe.Pointer(in.Affinity))
	out.SchedulerName = in.SchedulerName
	out.Tolerations = *(*[]corev1.Toleration)(unsafe.Pointer(&in.Tolerations))
	out.HostAliases = *(*[]corev1.HostAlias)(unsafe.Pointer(&in.HostAliases))
	out.PriorityClassName = in.PriorityClassName
	out.Priority = (*int32)(unsafe.Pointer(in.Priority))
	out.PreemptionPolicy = (*corev1.PreemptionPolicy)(unsafe.Pointer(in.PreemptionPolicy))
	out.DNSConfig = (*corev1.PodDNSConfig)(unsafe.Pointer(in.DNSConfig))
	out.ReadinessGates = *(*[]corev1.PodReadinessGate)(unsafe.Pointer(&in.ReadinessGates))
	out.RuntimeClassName = (*string)(unsafe.Pointer(in.RuntimeClassName))
	out.Overhead = *(*corev1.ResourceList)(unsafe.Pointer(&in.Overhead))
	out.EnableServiceLinks = (*bool)(unsafe.Pointer(in.EnableServiceLinks))
	out.TopologySpreadConstraints = *(*[]corev1.TopologySpreadConstraint)(unsafe.Pointer(&in.TopologySpreadConstraints))
	out.OS = (*corev1.PodOS)(unsafe.Pointer(in.OS))
	out.SchedulingGates = *(*[]corev1.PodSchedulingGate)(unsafe.Pointer(&in.SchedulingGates))
	out.ResourceClaims = *(*[]corev1.PodResourceClaim)(unsafe.Pointer(&in.ResourceClaims))
	out.Resources = (*corev1.ResourceRequirements)(unsafe.Pointer(in.Resources))
	out.HostnameOverride = (*string)(unsafe.Pointer(in.HostnameOverride))
	return nil
}

func autoConvert_v1_PodStatus_To_core_PodStatus(in *corev1.PodStatus, out *core.PodStatus, s conversion.Scope) error {
	out.ObservedGeneration = in.ObservedGeneration
	out.Phase = core.PodPhase(in.Phase)
	out.Conditions = *(*[]core.PodCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.NominatedNodeName = in.NominatedNodeName
	out.HostIP = in.HostIP
	out.HostIPs = *(*[]core.HostIP)(unsafe.Pointer(&in.HostIPs))
	// WARNING: in.PodIP requires manual conversion: does not exist in peer-type
	out.PodIPs = *(*[]core.PodIP)(unsafe.Pointer(&in.PodIPs))
	out.StartTime = (*metav1.Time)(unsafe.Pointer(in.StartTime))
	out.InitContainerStatuses = *(*[]core.ContainerStatus)(unsafe.Pointer(&in.InitContainerStatuses))
	out.ContainerStatuses = *(*[]core.ContainerStatus)(unsafe.Pointer(&in.ContainerStatuses))
	out.QOSClass = core.PodQOSClass(in.QOSClass)
	out.EphemeralContainerStatuses = *(*[]core.ContainerStatus)(unsafe.Pointer(&in.EphemeralContainerStatuses))
	out.Resize = core.PodResizeStatus(in.Resize)
	out.ResourceClaimStatuses = *(*[]core.PodResourceClaimStatus)(unsafe.Pointer(&in.ResourceClaimStatuses))
	return nil
}

func autoConvert_core_PodStatus_To_v1_PodStatus(in *core.PodStatus, out *corev1.PodStatus, s conversion.Scope) error {
	out.ObservedGeneration = in.ObservedGeneration
	out.Phase = corev1.PodPhase(in.Phase)
	out.Conditions = *(*[]corev1.PodCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.NominatedNodeName = in.NominatedNodeName
	out.HostIP = in.HostIP
	out.HostIPs = *(*[]corev1.HostIP)(unsafe.Pointer(&in.HostIPs))
	out.PodIPs = *(*[]corev1.PodIP)(unsafe.Pointer(&in.PodIPs))
	out.StartTime = (*metav1.Time)(unsafe.Pointer(in.StartTime))
	out.QOSClass = corev1.PodQOSClass(in.QOSClass)
	out.InitContainerStatuses = *(*[]corev1.ContainerStatus)(unsafe.Pointer(&in.InitContainerStatuses))
	out.ContainerStatuses = *(*[]corev1.ContainerStatus)(unsafe.Pointer(&in.ContainerStatuses))
	out.EphemeralContainerStatuses = *(*[]corev1.ContainerStatus)(unsafe.Pointer(&in.EphemeralContainerStatuses))
	out.Resize = corev1.PodResizeStatus(in.Resize)
	out.ResourceClaimStatuses = *(*[]corev1.PodResourceClaimStatus)(unsafe.Pointer(&in.ResourceClaimStatuses))
	return nil
}

func autoConvert_v1_PodStatusResult_To_core_PodStatusResult(in *corev1.PodStatusResult, out *core.PodStatusResult, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PodStatus_To_core_PodStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PodStatusResult_To_core_PodStatusResult is an autogenerated conversion function.
func Convert_v1_PodStatusResult_To_core_PodStatusResult(in *corev1.PodStatusResult, out *core.PodStatusResult, s conversion.Scope) error {
	return autoConvert_v1_PodStatusResult_To_core_PodStatusResult(in, out, s)
}

func autoConvert_core_PodStatusResult_To_v1_PodStatusResult(in *core.PodStatusResult, out *corev1.PodStatusResult, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_PodStatus_To_v1_PodStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_PodStatusResult_To_v1_PodStatusResult is an autogenerated conversion function.
func Convert_core_PodStatusResult_To_v1_PodStatusResult(in *core.PodStatusResult, out *corev1.PodStatusResult, s conversion.Scope) error {
	return autoConvert_core_PodStatusResult_To_v1_PodStatusResult(in, out, s)
}

func autoConvert_v1_PodTemplate_To_core_PodTemplate(in *corev1.PodTemplate, out *core.PodTemplate, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PodTemplateSpec_To_core_PodTemplateSpec(&in.Template, &out.Template, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PodTemplate_To_core_PodTemplate is an autogenerated conversion function.
func Convert_v1_PodTemplate_To_core_PodTemplate(in *corev1.PodTemplate, out *core.PodTemplate, s conversion.Scope) error {
	return autoConvert_v1_PodTemplate_To_core_PodTemplate(in, out, s)
}

func autoConvert_core_PodTemplate_To_v1_PodTemplate(in *core.PodTemplate, out *corev1.PodTemplate, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_PodTemplateSpec_To_v1_PodTemplateSpec(&in.Template, &out.Template, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_PodTemplate_To_v1_PodTemplate is an autogenerated conversion function.
func Convert_core_PodTemplate_To_v1_PodTemplate(in *core.PodTemplate, out *corev1.PodTemplate, s conversion.Scope) error {
	return autoConvert_core_PodTemplate_To_v1_PodTemplate(in, out, s)
}

func autoConvert_v1_PodTemplateList_To_core_PodTemplateList(in *corev1.PodTemplateList, out *core.PodTemplateList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]core.PodTemplate, len(*in))
		for i := range *in {
			if err := Convert_v1_PodTemplate_To_core_PodTemplate(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_PodTemplateList_To_core_PodTemplateList is an autogenerated conversion function.
func Convert_v1_PodTemplateList_To_core_PodTemplateList(in *corev1.PodTemplateList, out *core.PodTemplateList, s conversion.Scope) error {
	return autoConvert_v1_PodTemplateList_To_core_PodTemplateList(in, out, s)
}

func autoConvert_core_PodTemplateList_To_v1_PodTemplateList(in *core.PodTemplateList, out *corev1.PodTemplateList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]corev1.PodTemplate, len(*in))
		for i := range *in {
			if err := Convert_core_PodTemplate_To_v1_PodTemplate(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_PodTemplateList_To_v1_PodTemplateList is an autogenerated conversion function.
func Convert_core_PodTemplateList_To_v1_PodTemplateList(in *core.PodTemplateList, out *corev1.PodTemplateList, s conversion.Scope) error {
	return autoConvert_core_PodTemplateList_To_v1_PodTemplateList(in, out, s)
}

func autoConvert_v1_PodTemplateSpec_To_core_PodTemplateSpec(in *corev1.PodTemplateSpec, out *core.PodTemplateSpec, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PodSpec_To_core_PodSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

func autoConvert_core_PodTemplateSpec_To_v1_PodTemplateSpec(in *core.PodTemplateSpec, out *corev1.PodTemplateSpec, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_PodSpec_To_v1_PodSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1_PortStatus_To_core_PortStatus(in *corev1.PortStatus, out *core.PortStatus, s conversion.Scope) error {
	out.Port = in.Port
	out.Protocol = core.Protocol(in.Protocol)
	out.Error = (*string)(unsafe.Pointer(in.Error))
	return nil
}

// Convert_v1_PortStatus_To_core_PortStatus is an autogenerated conversion function.
func Convert_v1_PortStatus_To_core_PortStatus(in *corev1.PortStatus, out *core.PortStatus, s conversion.Scope) error {
	return autoConvert_v1_PortStatus_To_core_PortStatus(in, out, s)
}

func autoConvert_core_PortStatus_To_v1_PortStatus(in *core.PortStatus, out *corev1.PortStatus, s conversion.Scope) error {
	out.Port = in.Port
	out.Protocol = corev1.Protocol(in.Protocol)
	out.Error = (*string)(unsafe.Pointer(in.Error))
	return nil
}

// Convert_core_PortStatus_To_v1_PortStatus is an autogenerated conversion function.
func Convert_core_PortStatus_To_v1_PortStatus(in *core.PortStatus, out *corev1.PortStatus, s conversion.Scope) error {
	return autoConvert_core_PortStatus_To_v1_PortStatus(in, out, s)
}

func autoConvert_v1_PortworxVolumeSource_To_core_PortworxVolumeSource(in *corev1.PortworxVolumeSource, out *core.PortworxVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_PortworxVolumeSource_To_core_PortworxVolumeSource is an autogenerated conversion function.
func Convert_v1_PortworxVolumeSource_To_core_PortworxVolumeSource(in *corev1.PortworxVolumeSource, out *core.PortworxVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_PortworxVolumeSource_To_core_PortworxVolumeSource(in, out, s)
}

func autoConvert_core_PortworxVolumeSource_To_v1_PortworxVolumeSource(in *core.PortworxVolumeSource, out *corev1.PortworxVolumeSource, s conversion.Scope) error {
	out.VolumeID = in.VolumeID
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_PortworxVolumeSource_To_v1_PortworxVolumeSource is an autogenerated conversion function.
func Convert_core_PortworxVolumeSource_To_v1_PortworxVolumeSource(in *core.PortworxVolumeSource, out *corev1.PortworxVolumeSource, s conversion.Scope) error {
	return autoConvert_core_PortworxVolumeSource_To_v1_PortworxVolumeSource(in, out, s)
}

func autoConvert_v1_Preconditions_To_core_Preconditions(in *corev1.Preconditions, out *core.Preconditions, s conversion.Scope) error {
	out.UID = (*types.UID)(unsafe.Pointer(in.UID))
	return nil
}

// Convert_v1_Preconditions_To_core_Preconditions is an autogenerated conversion function.
func Convert_v1_Preconditions_To_core_Preconditions(in *corev1.Preconditions, out *core.Preconditions, s conversion.Scope) error {
	return autoConvert_v1_Preconditions_To_core_Preconditions(in, out, s)
}

func autoConvert_core_Preconditions_To_v1_Preconditions(in *core.Preconditions, out *corev1.Preconditions, s conversion.Scope) error {
	out.UID = (*types.UID)(unsafe.Pointer(in.UID))
	return nil
}

// Convert_core_Preconditions_To_v1_Preconditions is an autogenerated conversion function.
func Convert_core_Preconditions_To_v1_Preconditions(in *core.Preconditions, out *corev1.Preconditions, s conversion.Scope) error {
	return autoConvert_core_Preconditions_To_v1_Preconditions(in, out, s)
}

func autoConvert_v1_PreferAvoidPodsEntry_To_core_PreferAvoidPodsEntry(in *corev1.PreferAvoidPodsEntry, out *core.PreferAvoidPodsEntry, s conversion.Scope) error {
	if err := Convert_v1_PodSignature_To_core_PodSignature(&in.PodSignature, &out.PodSignature, s); err != nil {
		return err
	}
	out.EvictionTime = in.EvictionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_PreferAvoidPodsEntry_To_core_PreferAvoidPodsEntry is an autogenerated conversion function.
func Convert_v1_PreferAvoidPodsEntry_To_core_PreferAvoidPodsEntry(in *corev1.PreferAvoidPodsEntry, out *core.PreferAvoidPodsEntry, s conversion.Scope) error {
	return autoConvert_v1_PreferAvoidPodsEntry_To_core_PreferAvoidPodsEntry(in, out, s)
}

func autoConvert_core_PreferAvoidPodsEntry_To_v1_PreferAvoidPodsEntry(in *core.PreferAvoidPodsEntry, out *corev1.PreferAvoidPodsEntry, s conversion.Scope) error {
	if err := Convert_core_PodSignature_To_v1_PodSignature(&in.PodSignature, &out.PodSignature, s); err != nil {
		return err
	}
	out.EvictionTime = in.EvictionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_core_PreferAvoidPodsEntry_To_v1_PreferAvoidPodsEntry is an autogenerated conversion function.
func Convert_core_PreferAvoidPodsEntry_To_v1_PreferAvoidPodsEntry(in *core.PreferAvoidPodsEntry, out *corev1.PreferAvoidPodsEntry, s conversion.Scope) error {
	return autoConvert_core_PreferAvoidPodsEntry_To_v1_PreferAvoidPodsEntry(in, out, s)
}

func autoConvert_v1_PreferredSchedulingTerm_To_core_PreferredSchedulingTerm(in *corev1.PreferredSchedulingTerm, out *core.PreferredSchedulingTerm, s conversion.Scope) error {
	out.Weight = in.Weight
	if err := Convert_v1_NodeSelectorTerm_To_core_NodeSelectorTerm(&in.Preference, &out.Preference, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PreferredSchedulingTerm_To_core_PreferredSchedulingTerm is an autogenerated conversion function.
func Convert_v1_PreferredSchedulingTerm_To_core_PreferredSchedulingTerm(in *corev1.PreferredSchedulingTerm, out *core.PreferredSchedulingTerm, s conversion.Scope) error {
	return autoConvert_v1_PreferredSchedulingTerm_To_core_PreferredSchedulingTerm(in, out, s)
}

func autoConvert_core_PreferredSchedulingTerm_To_v1_PreferredSchedulingTerm(in *core.PreferredSchedulingTerm, out *corev1.PreferredSchedulingTerm, s conversion.Scope) error {
	out.Weight = in.Weight
	if err := Convert_core_NodeSelectorTerm_To_v1_NodeSelectorTerm(&in.Preference, &out.Preference, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_PreferredSchedulingTerm_To_v1_PreferredSchedulingTerm is an autogenerated conversion function.
func Convert_core_PreferredSchedulingTerm_To_v1_PreferredSchedulingTerm(in *core.PreferredSchedulingTerm, out *corev1.PreferredSchedulingTerm, s conversion.Scope) error {
	return autoConvert_core_PreferredSchedulingTerm_To_v1_PreferredSchedulingTerm(in, out, s)
}

func autoConvert_v1_Probe_To_core_Probe(in *corev1.Probe, out *core.Probe, s conversion.Scope) error {
	if err := Convert_v1_ProbeHandler_To_core_ProbeHandler(&in.ProbeHandler, &out.ProbeHandler, s); err != nil {
		return err
	}
	out.InitialDelaySeconds = in.InitialDelaySeconds
	out.TimeoutSeconds = in.TimeoutSeconds
	out.PeriodSeconds = in.PeriodSeconds
	out.SuccessThreshold = in.SuccessThreshold
	out.FailureThreshold = in.FailureThreshold
	out.TerminationGracePeriodSeconds = (*int64)(unsafe.Pointer(in.TerminationGracePeriodSeconds))
	return nil
}

// Convert_v1_Probe_To_core_Probe is an autogenerated conversion function.
func Convert_v1_Probe_To_core_Probe(in *corev1.Probe, out *core.Probe, s conversion.Scope) error {
	return autoConvert_v1_Probe_To_core_Probe(in, out, s)
}

func autoConvert_core_Probe_To_v1_Probe(in *core.Probe, out *corev1.Probe, s conversion.Scope) error {
	if err := Convert_core_ProbeHandler_To_v1_ProbeHandler(&in.ProbeHandler, &out.ProbeHandler, s); err != nil {
		return err
	}
	out.InitialDelaySeconds = in.InitialDelaySeconds
	out.TimeoutSeconds = in.TimeoutSeconds
	out.PeriodSeconds = in.PeriodSeconds
	out.SuccessThreshold = in.SuccessThreshold
	out.FailureThreshold = in.FailureThreshold
	out.TerminationGracePeriodSeconds = (*int64)(unsafe.Pointer(in.TerminationGracePeriodSeconds))
	return nil
}

// Convert_core_Probe_To_v1_Probe is an autogenerated conversion function.
func Convert_core_Probe_To_v1_Probe(in *core.Probe, out *corev1.Probe, s conversion.Scope) error {
	return autoConvert_core_Probe_To_v1_Probe(in, out, s)
}

func autoConvert_v1_ProbeHandler_To_core_ProbeHandler(in *corev1.ProbeHandler, out *core.ProbeHandler, s conversion.Scope) error {
	out.Exec = (*core.ExecAction)(unsafe.Pointer(in.Exec))
	out.HTTPGet = (*core.HTTPGetAction)(unsafe.Pointer(in.HTTPGet))
	out.TCPSocket = (*core.TCPSocketAction)(unsafe.Pointer(in.TCPSocket))
	out.GRPC = (*core.GRPCAction)(unsafe.Pointer(in.GRPC))
	return nil
}

// Convert_v1_ProbeHandler_To_core_ProbeHandler is an autogenerated conversion function.
func Convert_v1_ProbeHandler_To_core_ProbeHandler(in *corev1.ProbeHandler, out *core.ProbeHandler, s conversion.Scope) error {
	return autoConvert_v1_ProbeHandler_To_core_ProbeHandler(in, out, s)
}

func autoConvert_core_ProbeHandler_To_v1_ProbeHandler(in *core.ProbeHandler, out *corev1.ProbeHandler, s conversion.Scope) error {
	out.Exec = (*corev1.ExecAction)(unsafe.Pointer(in.Exec))
	out.HTTPGet = (*corev1.HTTPGetAction)(unsafe.Pointer(in.HTTPGet))
	out.TCPSocket = (*corev1.TCPSocketAction)(unsafe.Pointer(in.TCPSocket))
	out.GRPC = (*corev1.GRPCAction)(unsafe.Pointer(in.GRPC))
	return nil
}

// Convert_core_ProbeHandler_To_v1_ProbeHandler is an autogenerated conversion function.
func Convert_core_ProbeHandler_To_v1_ProbeHandler(in *core.ProbeHandler, out *corev1.ProbeHandler, s conversion.Scope) error {
	return autoConvert_core_ProbeHandler_To_v1_ProbeHandler(in, out, s)
}

func autoConvert_v1_ProjectedVolumeSource_To_core_ProjectedVolumeSource(in *corev1.ProjectedVolumeSource, out *core.ProjectedVolumeSource, s conversion.Scope) error {
	if in.Sources != nil {
		in, out := &in.Sources, &out.Sources
		*out = make([]core.VolumeProjection, len(*in))
		for i := range *in {
			if err := Convert_v1_VolumeProjection_To_core_VolumeProjection(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Sources = nil
	}
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	return nil
}

// Convert_v1_ProjectedVolumeSource_To_core_ProjectedVolumeSource is an autogenerated conversion function.
func Convert_v1_ProjectedVolumeSource_To_core_ProjectedVolumeSource(in *corev1.ProjectedVolumeSource, out *core.ProjectedVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_ProjectedVolumeSource_To_core_ProjectedVolumeSource(in, out, s)
}

func autoConvert_core_ProjectedVolumeSource_To_v1_ProjectedVolumeSource(in *core.ProjectedVolumeSource, out *corev1.ProjectedVolumeSource, s conversion.Scope) error {
	if in.Sources != nil {
		in, out := &in.Sources, &out.Sources
		*out = make([]corev1.VolumeProjection, len(*in))
		for i := range *in {
			if err := Convert_core_VolumeProjection_To_v1_VolumeProjection(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Sources = nil
	}
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	return nil
}

// Convert_core_ProjectedVolumeSource_To_v1_ProjectedVolumeSource is an autogenerated conversion function.
func Convert_core_ProjectedVolumeSource_To_v1_ProjectedVolumeSource(in *core.ProjectedVolumeSource, out *corev1.ProjectedVolumeSource, s conversion.Scope) error {
	return autoConvert_core_ProjectedVolumeSource_To_v1_ProjectedVolumeSource(in, out, s)
}

func autoConvert_v1_QuobyteVolumeSource_To_core_QuobyteVolumeSource(in *corev1.QuobyteVolumeSource, out *core.QuobyteVolumeSource, s conversion.Scope) error {
	out.Registry = in.Registry
	out.Volume = in.Volume
	out.ReadOnly = in.ReadOnly
	out.User = in.User
	out.Group = in.Group
	out.Tenant = in.Tenant
	return nil
}

// Convert_v1_QuobyteVolumeSource_To_core_QuobyteVolumeSource is an autogenerated conversion function.
func Convert_v1_QuobyteVolumeSource_To_core_QuobyteVolumeSource(in *corev1.QuobyteVolumeSource, out *core.QuobyteVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_QuobyteVolumeSource_To_core_QuobyteVolumeSource(in, out, s)
}

func autoConvert_core_QuobyteVolumeSource_To_v1_QuobyteVolumeSource(in *core.QuobyteVolumeSource, out *corev1.QuobyteVolumeSource, s conversion.Scope) error {
	out.Registry = in.Registry
	out.Volume = in.Volume
	out.ReadOnly = in.ReadOnly
	out.User = in.User
	out.Group = in.Group
	out.Tenant = in.Tenant
	return nil
}

// Convert_core_QuobyteVolumeSource_To_v1_QuobyteVolumeSource is an autogenerated conversion function.
func Convert_core_QuobyteVolumeSource_To_v1_QuobyteVolumeSource(in *core.QuobyteVolumeSource, out *corev1.QuobyteVolumeSource, s conversion.Scope) error {
	return autoConvert_core_QuobyteVolumeSource_To_v1_QuobyteVolumeSource(in, out, s)
}

func autoConvert_v1_RBDPersistentVolumeSource_To_core_RBDPersistentVolumeSource(in *corev1.RBDPersistentVolumeSource, out *core.RBDPersistentVolumeSource, s conversion.Scope) error {
	out.CephMonitors = *(*[]string)(unsafe.Pointer(&in.CephMonitors))
	out.RBDImage = in.RBDImage
	out.FSType = in.FSType
	out.RBDPool = in.RBDPool
	out.RadosUser = in.RadosUser
	out.Keyring = in.Keyring
	out.SecretRef = (*core.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_RBDPersistentVolumeSource_To_core_RBDPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_RBDPersistentVolumeSource_To_core_RBDPersistentVolumeSource(in *corev1.RBDPersistentVolumeSource, out *core.RBDPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_RBDPersistentVolumeSource_To_core_RBDPersistentVolumeSource(in, out, s)
}

func autoConvert_core_RBDPersistentVolumeSource_To_v1_RBDPersistentVolumeSource(in *core.RBDPersistentVolumeSource, out *corev1.RBDPersistentVolumeSource, s conversion.Scope) error {
	out.CephMonitors = *(*[]string)(unsafe.Pointer(&in.CephMonitors))
	out.RBDImage = in.RBDImage
	out.FSType = in.FSType
	out.RBDPool = in.RBDPool
	out.RadosUser = in.RadosUser
	out.Keyring = in.Keyring
	out.SecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_RBDPersistentVolumeSource_To_v1_RBDPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_RBDPersistentVolumeSource_To_v1_RBDPersistentVolumeSource(in *core.RBDPersistentVolumeSource, out *corev1.RBDPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_RBDPersistentVolumeSource_To_v1_RBDPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_RBDVolumeSource_To_core_RBDVolumeSource(in *corev1.RBDVolumeSource, out *core.RBDVolumeSource, s conversion.Scope) error {
	out.CephMonitors = *(*[]string)(unsafe.Pointer(&in.CephMonitors))
	out.RBDImage = in.RBDImage
	out.FSType = in.FSType
	out.RBDPool = in.RBDPool
	out.RadosUser = in.RadosUser
	out.Keyring = in.Keyring
	out.SecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_RBDVolumeSource_To_core_RBDVolumeSource is an autogenerated conversion function.
func Convert_v1_RBDVolumeSource_To_core_RBDVolumeSource(in *corev1.RBDVolumeSource, out *core.RBDVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_RBDVolumeSource_To_core_RBDVolumeSource(in, out, s)
}

func autoConvert_core_RBDVolumeSource_To_v1_RBDVolumeSource(in *core.RBDVolumeSource, out *corev1.RBDVolumeSource, s conversion.Scope) error {
	out.CephMonitors = *(*[]string)(unsafe.Pointer(&in.CephMonitors))
	out.RBDImage = in.RBDImage
	out.FSType = in.FSType
	out.RBDPool = in.RBDPool
	out.RadosUser = in.RadosUser
	out.Keyring = in.Keyring
	out.SecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_RBDVolumeSource_To_v1_RBDVolumeSource is an autogenerated conversion function.
func Convert_core_RBDVolumeSource_To_v1_RBDVolumeSource(in *core.RBDVolumeSource, out *corev1.RBDVolumeSource, s conversion.Scope) error {
	return autoConvert_core_RBDVolumeSource_To_v1_RBDVolumeSource(in, out, s)
}

func autoConvert_v1_RangeAllocation_To_core_RangeAllocation(in *corev1.RangeAllocation, out *core.RangeAllocation, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Range = in.Range
	out.Data = *(*[]byte)(unsafe.Pointer(&in.Data))
	return nil
}

// Convert_v1_RangeAllocation_To_core_RangeAllocation is an autogenerated conversion function.
func Convert_v1_RangeAllocation_To_core_RangeAllocation(in *corev1.RangeAllocation, out *core.RangeAllocation, s conversion.Scope) error {
	return autoConvert_v1_RangeAllocation_To_core_RangeAllocation(in, out, s)
}

func autoConvert_core_RangeAllocation_To_v1_RangeAllocation(in *core.RangeAllocation, out *corev1.RangeAllocation, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Range = in.Range
	out.Data = *(*[]byte)(unsafe.Pointer(&in.Data))
	return nil
}

// Convert_core_RangeAllocation_To_v1_RangeAllocation is an autogenerated conversion function.
func Convert_core_RangeAllocation_To_v1_RangeAllocation(in *core.RangeAllocation, out *corev1.RangeAllocation, s conversion.Scope) error {
	return autoConvert_core_RangeAllocation_To_v1_RangeAllocation(in, out, s)
}

func autoConvert_v1_ReplicationController_To_core_ReplicationController(in *corev1.ReplicationController, out *core.ReplicationController, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ReplicationControllerSpec_To_core_ReplicationControllerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ReplicationControllerStatus_To_core_ReplicationControllerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ReplicationController_To_core_ReplicationController is an autogenerated conversion function.
func Convert_v1_ReplicationController_To_core_ReplicationController(in *corev1.ReplicationController, out *core.ReplicationController, s conversion.Scope) error {
	return autoConvert_v1_ReplicationController_To_core_ReplicationController(in, out, s)
}

func autoConvert_core_ReplicationController_To_v1_ReplicationController(in *core.ReplicationController, out *corev1.ReplicationController, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_ReplicationControllerSpec_To_v1_ReplicationControllerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_ReplicationControllerStatus_To_v1_ReplicationControllerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_ReplicationController_To_v1_ReplicationController is an autogenerated conversion function.
func Convert_core_ReplicationController_To_v1_ReplicationController(in *core.ReplicationController, out *corev1.ReplicationController, s conversion.Scope) error {
	return autoConvert_core_ReplicationController_To_v1_ReplicationController(in, out, s)
}

func autoConvert_v1_ReplicationControllerCondition_To_core_ReplicationControllerCondition(in *corev1.ReplicationControllerCondition, out *core.ReplicationControllerCondition, s conversion.Scope) error {
	out.Type = core.ReplicationControllerConditionType(in.Type)
	out.Status = core.ConditionStatus(in.Status)
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_ReplicationControllerCondition_To_core_ReplicationControllerCondition is an autogenerated conversion function.
func Convert_v1_ReplicationControllerCondition_To_core_ReplicationControllerCondition(in *corev1.ReplicationControllerCondition, out *core.ReplicationControllerCondition, s conversion.Scope) error {
	return autoConvert_v1_ReplicationControllerCondition_To_core_ReplicationControllerCondition(in, out, s)
}

func autoConvert_core_ReplicationControllerCondition_To_v1_ReplicationControllerCondition(in *core.ReplicationControllerCondition, out *corev1.ReplicationControllerCondition, s conversion.Scope) error {
	out.Type = corev1.ReplicationControllerConditionType(in.Type)
	out.Status = corev1.ConditionStatus(in.Status)
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_core_ReplicationControllerCondition_To_v1_ReplicationControllerCondition is an autogenerated conversion function.
func Convert_core_ReplicationControllerCondition_To_v1_ReplicationControllerCondition(in *core.ReplicationControllerCondition, out *corev1.ReplicationControllerCondition, s conversion.Scope) error {
	return autoConvert_core_ReplicationControllerCondition_To_v1_ReplicationControllerCondition(in, out, s)
}

func autoConvert_v1_ReplicationControllerList_To_core_ReplicationControllerList(in *corev1.ReplicationControllerList, out *core.ReplicationControllerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]core.ReplicationController, len(*in))
		for i := range *in {
			if err := Convert_v1_ReplicationController_To_core_ReplicationController(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_ReplicationControllerList_To_core_ReplicationControllerList is an autogenerated conversion function.
func Convert_v1_ReplicationControllerList_To_core_ReplicationControllerList(in *corev1.ReplicationControllerList, out *core.ReplicationControllerList, s conversion.Scope) error {
	return autoConvert_v1_ReplicationControllerList_To_core_ReplicationControllerList(in, out, s)
}

func autoConvert_core_ReplicationControllerList_To_v1_ReplicationControllerList(in *core.ReplicationControllerList, out *corev1.ReplicationControllerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]corev1.ReplicationController, len(*in))
		for i := range *in {
			if err := Convert_core_ReplicationController_To_v1_ReplicationController(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_ReplicationControllerList_To_v1_ReplicationControllerList is an autogenerated conversion function.
func Convert_core_ReplicationControllerList_To_v1_ReplicationControllerList(in *core.ReplicationControllerList, out *corev1.ReplicationControllerList, s conversion.Scope) error {
	return autoConvert_core_ReplicationControllerList_To_v1_ReplicationControllerList(in, out, s)
}

func autoConvert_v1_ReplicationControllerSpec_To_core_ReplicationControllerSpec(in *corev1.ReplicationControllerSpec, out *core.ReplicationControllerSpec, s conversion.Scope) error {
	out.Replicas = (*int32)(unsafe.Pointer(in.Replicas))
	out.MinReadySeconds = in.MinReadySeconds
	out.Selector = *(*map[string]string)(unsafe.Pointer(&in.Selector))
	if in.Template != nil {
		in, out := &in.Template, &out.Template
		*out = new(core.PodTemplateSpec)
		if err := Convert_v1_PodTemplateSpec_To_core_PodTemplateSpec(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.Template = nil
	}
	return nil
}

func autoConvert_core_ReplicationControllerSpec_To_v1_ReplicationControllerSpec(in *core.ReplicationControllerSpec, out *corev1.ReplicationControllerSpec, s conversion.Scope) error {
	out.Replicas = (*int32)(unsafe.Pointer(in.Replicas))
	out.MinReadySeconds = in.MinReadySeconds
	out.Selector = *(*map[string]string)(unsafe.Pointer(&in.Selector))
	if in.Template != nil {
		in, out := &in.Template, &out.Template
		*out = new(corev1.PodTemplateSpec)
		if err := Convert_core_PodTemplateSpec_To_v1_PodTemplateSpec(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.Template = nil
	}
	return nil
}

func autoConvert_v1_ReplicationControllerStatus_To_core_ReplicationControllerStatus(in *corev1.ReplicationControllerStatus, out *core.ReplicationControllerStatus, s conversion.Scope) error {
	out.Replicas = in.Replicas
	out.FullyLabeledReplicas = in.FullyLabeledReplicas
	out.ReadyReplicas = in.ReadyReplicas
	out.AvailableReplicas = in.AvailableReplicas
	out.ObservedGeneration = in.ObservedGeneration
	out.Conditions = *(*[]core.ReplicationControllerCondition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_v1_ReplicationControllerStatus_To_core_ReplicationControllerStatus is an autogenerated conversion function.
func Convert_v1_ReplicationControllerStatus_To_core_ReplicationControllerStatus(in *corev1.ReplicationControllerStatus, out *core.ReplicationControllerStatus, s conversion.Scope) error {
	return autoConvert_v1_ReplicationControllerStatus_To_core_ReplicationControllerStatus(in, out, s)
}

func autoConvert_core_ReplicationControllerStatus_To_v1_ReplicationControllerStatus(in *core.ReplicationControllerStatus, out *corev1.ReplicationControllerStatus, s conversion.Scope) error {
	out.Replicas = in.Replicas
	out.FullyLabeledReplicas = in.FullyLabeledReplicas
	out.ReadyReplicas = in.ReadyReplicas
	out.AvailableReplicas = in.AvailableReplicas
	out.ObservedGeneration = in.ObservedGeneration
	out.Conditions = *(*[]corev1.ReplicationControllerCondition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_core_ReplicationControllerStatus_To_v1_ReplicationControllerStatus is an autogenerated conversion function.
func Convert_core_ReplicationControllerStatus_To_v1_ReplicationControllerStatus(in *core.ReplicationControllerStatus, out *corev1.ReplicationControllerStatus, s conversion.Scope) error {
	return autoConvert_core_ReplicationControllerStatus_To_v1_ReplicationControllerStatus(in, out, s)
}

func autoConvert_v1_ResourceClaim_To_core_ResourceClaim(in *corev1.ResourceClaim, out *core.ResourceClaim, s conversion.Scope) error {
	out.Name = in.Name
	out.Request = in.Request
	return nil
}

// Convert_v1_ResourceClaim_To_core_ResourceClaim is an autogenerated conversion function.
func Convert_v1_ResourceClaim_To_core_ResourceClaim(in *corev1.ResourceClaim, out *core.ResourceClaim, s conversion.Scope) error {
	return autoConvert_v1_ResourceClaim_To_core_ResourceClaim(in, out, s)
}

func autoConvert_core_ResourceClaim_To_v1_ResourceClaim(in *core.ResourceClaim, out *corev1.ResourceClaim, s conversion.Scope) error {
	out.Name = in.Name
	out.Request = in.Request
	return nil
}

// Convert_core_ResourceClaim_To_v1_ResourceClaim is an autogenerated conversion function.
func Convert_core_ResourceClaim_To_v1_ResourceClaim(in *core.ResourceClaim, out *corev1.ResourceClaim, s conversion.Scope) error {
	return autoConvert_core_ResourceClaim_To_v1_ResourceClaim(in, out, s)
}

func autoConvert_v1_ResourceFieldSelector_To_core_ResourceFieldSelector(in *corev1.ResourceFieldSelector, out *core.ResourceFieldSelector, s conversion.Scope) error {
	out.ContainerName = in.ContainerName
	out.Resource = in.Resource
	out.Divisor = in.Divisor
	return nil
}

// Convert_v1_ResourceFieldSelector_To_core_ResourceFieldSelector is an autogenerated conversion function.
func Convert_v1_ResourceFieldSelector_To_core_ResourceFieldSelector(in *corev1.ResourceFieldSelector, out *core.ResourceFieldSelector, s conversion.Scope) error {
	return autoConvert_v1_ResourceFieldSelector_To_core_ResourceFieldSelector(in, out, s)
}

func autoConvert_core_ResourceFieldSelector_To_v1_ResourceFieldSelector(in *core.ResourceFieldSelector, out *corev1.ResourceFieldSelector, s conversion.Scope) error {
	out.ContainerName = in.ContainerName
	out.Resource = in.Resource
	out.Divisor = in.Divisor
	return nil
}

// Convert_core_ResourceFieldSelector_To_v1_ResourceFieldSelector is an autogenerated conversion function.
func Convert_core_ResourceFieldSelector_To_v1_ResourceFieldSelector(in *core.ResourceFieldSelector, out *corev1.ResourceFieldSelector, s conversion.Scope) error {
	return autoConvert_core_ResourceFieldSelector_To_v1_ResourceFieldSelector(in, out, s)
}

func autoConvert_v1_ResourceHealth_To_core_ResourceHealth(in *corev1.ResourceHealth, out *core.ResourceHealth, s conversion.Scope) error {
	out.ResourceID = core.ResourceID(in.ResourceID)
	out.Health = core.ResourceHealthStatus(in.Health)
	return nil
}

// Convert_v1_ResourceHealth_To_core_ResourceHealth is an autogenerated conversion function.
func Convert_v1_ResourceHealth_To_core_ResourceHealth(in *corev1.ResourceHealth, out *core.ResourceHealth, s conversion.Scope) error {
	return autoConvert_v1_ResourceHealth_To_core_ResourceHealth(in, out, s)
}

func autoConvert_core_ResourceHealth_To_v1_ResourceHealth(in *core.ResourceHealth, out *corev1.ResourceHealth, s conversion.Scope) error {
	out.ResourceID = corev1.ResourceID(in.ResourceID)
	out.Health = corev1.ResourceHealthStatus(in.Health)
	return nil
}

// Convert_core_ResourceHealth_To_v1_ResourceHealth is an autogenerated conversion function.
func Convert_core_ResourceHealth_To_v1_ResourceHealth(in *core.ResourceHealth, out *corev1.ResourceHealth, s conversion.Scope) error {
	return autoConvert_core_ResourceHealth_To_v1_ResourceHealth(in, out, s)
}

func autoConvert_v1_ResourceQuota_To_core_ResourceQuota(in *corev1.ResourceQuota, out *core.ResourceQuota, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ResourceQuotaSpec_To_core_ResourceQuotaSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ResourceQuotaStatus_To_core_ResourceQuotaStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ResourceQuota_To_core_ResourceQuota is an autogenerated conversion function.
func Convert_v1_ResourceQuota_To_core_ResourceQuota(in *corev1.ResourceQuota, out *core.ResourceQuota, s conversion.Scope) error {
	return autoConvert_v1_ResourceQuota_To_core_ResourceQuota(in, out, s)
}

func autoConvert_core_ResourceQuota_To_v1_ResourceQuota(in *core.ResourceQuota, out *corev1.ResourceQuota, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_ResourceQuotaSpec_To_v1_ResourceQuotaSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_ResourceQuotaStatus_To_v1_ResourceQuotaStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_ResourceQuota_To_v1_ResourceQuota is an autogenerated conversion function.
func Convert_core_ResourceQuota_To_v1_ResourceQuota(in *core.ResourceQuota, out *corev1.ResourceQuota, s conversion.Scope) error {
	return autoConvert_core_ResourceQuota_To_v1_ResourceQuota(in, out, s)
}

func autoConvert_v1_ResourceQuotaList_To_core_ResourceQuotaList(in *corev1.ResourceQuotaList, out *core.ResourceQuotaList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.ResourceQuota)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ResourceQuotaList_To_core_ResourceQuotaList is an autogenerated conversion function.
func Convert_v1_ResourceQuotaList_To_core_ResourceQuotaList(in *corev1.ResourceQuotaList, out *core.ResourceQuotaList, s conversion.Scope) error {
	return autoConvert_v1_ResourceQuotaList_To_core_ResourceQuotaList(in, out, s)
}

func autoConvert_core_ResourceQuotaList_To_v1_ResourceQuotaList(in *core.ResourceQuotaList, out *corev1.ResourceQuotaList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.ResourceQuota)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_ResourceQuotaList_To_v1_ResourceQuotaList is an autogenerated conversion function.
func Convert_core_ResourceQuotaList_To_v1_ResourceQuotaList(in *core.ResourceQuotaList, out *corev1.ResourceQuotaList, s conversion.Scope) error {
	return autoConvert_core_ResourceQuotaList_To_v1_ResourceQuotaList(in, out, s)
}

func autoConvert_v1_ResourceQuotaSpec_To_core_ResourceQuotaSpec(in *corev1.ResourceQuotaSpec, out *core.ResourceQuotaSpec, s conversion.Scope) error {
	out.Hard = *(*core.ResourceList)(unsafe.Pointer(&in.Hard))
	out.Scopes = *(*[]core.ResourceQuotaScope)(unsafe.Pointer(&in.Scopes))
	out.ScopeSelector = (*core.ScopeSelector)(unsafe.Pointer(in.ScopeSelector))
	return nil
}

// Convert_v1_ResourceQuotaSpec_To_core_ResourceQuotaSpec is an autogenerated conversion function.
func Convert_v1_ResourceQuotaSpec_To_core_ResourceQuotaSpec(in *corev1.ResourceQuotaSpec, out *core.ResourceQuotaSpec, s conversion.Scope) error {
	return autoConvert_v1_ResourceQuotaSpec_To_core_ResourceQuotaSpec(in, out, s)
}

func autoConvert_core_ResourceQuotaSpec_To_v1_ResourceQuotaSpec(in *core.ResourceQuotaSpec, out *corev1.ResourceQuotaSpec, s conversion.Scope) error {
	out.Hard = *(*corev1.ResourceList)(unsafe.Pointer(&in.Hard))
	out.Scopes = *(*[]corev1.ResourceQuotaScope)(unsafe.Pointer(&in.Scopes))
	out.ScopeSelector = (*corev1.ScopeSelector)(unsafe.Pointer(in.ScopeSelector))
	return nil
}

// Convert_core_ResourceQuotaSpec_To_v1_ResourceQuotaSpec is an autogenerated conversion function.
func Convert_core_ResourceQuotaSpec_To_v1_ResourceQuotaSpec(in *core.ResourceQuotaSpec, out *corev1.ResourceQuotaSpec, s conversion.Scope) error {
	return autoConvert_core_ResourceQuotaSpec_To_v1_ResourceQuotaSpec(in, out, s)
}

func autoConvert_v1_ResourceQuotaStatus_To_core_ResourceQuotaStatus(in *corev1.ResourceQuotaStatus, out *core.ResourceQuotaStatus, s conversion.Scope) error {
	out.Hard = *(*core.ResourceList)(unsafe.Pointer(&in.Hard))
	out.Used = *(*core.ResourceList)(unsafe.Pointer(&in.Used))
	return nil
}

// Convert_v1_ResourceQuotaStatus_To_core_ResourceQuotaStatus is an autogenerated conversion function.
func Convert_v1_ResourceQuotaStatus_To_core_ResourceQuotaStatus(in *corev1.ResourceQuotaStatus, out *core.ResourceQuotaStatus, s conversion.Scope) error {
	return autoConvert_v1_ResourceQuotaStatus_To_core_ResourceQuotaStatus(in, out, s)
}

func autoConvert_core_ResourceQuotaStatus_To_v1_ResourceQuotaStatus(in *core.ResourceQuotaStatus, out *corev1.ResourceQuotaStatus, s conversion.Scope) error {
	out.Hard = *(*corev1.ResourceList)(unsafe.Pointer(&in.Hard))
	out.Used = *(*corev1.ResourceList)(unsafe.Pointer(&in.Used))
	return nil
}

// Convert_core_ResourceQuotaStatus_To_v1_ResourceQuotaStatus is an autogenerated conversion function.
func Convert_core_ResourceQuotaStatus_To_v1_ResourceQuotaStatus(in *core.ResourceQuotaStatus, out *corev1.ResourceQuotaStatus, s conversion.Scope) error {
	return autoConvert_core_ResourceQuotaStatus_To_v1_ResourceQuotaStatus(in, out, s)
}

func autoConvert_v1_ResourceRequirements_To_core_ResourceRequirements(in *corev1.ResourceRequirements, out *core.ResourceRequirements, s conversion.Scope) error {
	out.Limits = *(*core.ResourceList)(unsafe.Pointer(&in.Limits))
	out.Requests = *(*core.ResourceList)(unsafe.Pointer(&in.Requests))
	out.Claims = *(*[]core.ResourceClaim)(unsafe.Pointer(&in.Claims))
	return nil
}

// Convert_v1_ResourceRequirements_To_core_ResourceRequirements is an autogenerated conversion function.
func Convert_v1_ResourceRequirements_To_core_ResourceRequirements(in *corev1.ResourceRequirements, out *core.ResourceRequirements, s conversion.Scope) error {
	return autoConvert_v1_ResourceRequirements_To_core_ResourceRequirements(in, out, s)
}

func autoConvert_core_ResourceRequirements_To_v1_ResourceRequirements(in *core.ResourceRequirements, out *corev1.ResourceRequirements, s conversion.Scope) error {
	out.Limits = *(*corev1.ResourceList)(unsafe.Pointer(&in.Limits))
	out.Requests = *(*corev1.ResourceList)(unsafe.Pointer(&in.Requests))
	out.Claims = *(*[]corev1.ResourceClaim)(unsafe.Pointer(&in.Claims))
	return nil
}

// Convert_core_ResourceRequirements_To_v1_ResourceRequirements is an autogenerated conversion function.
func Convert_core_ResourceRequirements_To_v1_ResourceRequirements(in *core.ResourceRequirements, out *corev1.ResourceRequirements, s conversion.Scope) error {
	return autoConvert_core_ResourceRequirements_To_v1_ResourceRequirements(in, out, s)
}

func autoConvert_v1_ResourceStatus_To_core_ResourceStatus(in *corev1.ResourceStatus, out *core.ResourceStatus, s conversion.Scope) error {
	out.Name = core.ResourceName(in.Name)
	out.Resources = *(*[]core.ResourceHealth)(unsafe.Pointer(&in.Resources))
	return nil
}

// Convert_v1_ResourceStatus_To_core_ResourceStatus is an autogenerated conversion function.
func Convert_v1_ResourceStatus_To_core_ResourceStatus(in *corev1.ResourceStatus, out *core.ResourceStatus, s conversion.Scope) error {
	return autoConvert_v1_ResourceStatus_To_core_ResourceStatus(in, out, s)
}

func autoConvert_core_ResourceStatus_To_v1_ResourceStatus(in *core.ResourceStatus, out *corev1.ResourceStatus, s conversion.Scope) error {
	out.Name = corev1.ResourceName(in.Name)
	out.Resources = *(*[]corev1.ResourceHealth)(unsafe.Pointer(&in.Resources))
	return nil
}

// Convert_core_ResourceStatus_To_v1_ResourceStatus is an autogenerated conversion function.
func Convert_core_ResourceStatus_To_v1_ResourceStatus(in *core.ResourceStatus, out *corev1.ResourceStatus, s conversion.Scope) error {
	return autoConvert_core_ResourceStatus_To_v1_ResourceStatus(in, out, s)
}

func autoConvert_v1_SELinuxOptions_To_core_SELinuxOptions(in *corev1.SELinuxOptions, out *core.SELinuxOptions, s conversion.Scope) error {
	out.User = in.User
	out.Role = in.Role
	out.Type = in.Type
	out.Level = in.Level
	return nil
}

// Convert_v1_SELinuxOptions_To_core_SELinuxOptions is an autogenerated conversion function.
func Convert_v1_SELinuxOptions_To_core_SELinuxOptions(in *corev1.SELinuxOptions, out *core.SELinuxOptions, s conversion.Scope) error {
	return autoConvert_v1_SELinuxOptions_To_core_SELinuxOptions(in, out, s)
}

func autoConvert_core_SELinuxOptions_To_v1_SELinuxOptions(in *core.SELinuxOptions, out *corev1.SELinuxOptions, s conversion.Scope) error {
	out.User = in.User
	out.Role = in.Role
	out.Type = in.Type
	out.Level = in.Level
	return nil
}

// Convert_core_SELinuxOptions_To_v1_SELinuxOptions is an autogenerated conversion function.
func Convert_core_SELinuxOptions_To_v1_SELinuxOptions(in *core.SELinuxOptions, out *corev1.SELinuxOptions, s conversion.Scope) error {
	return autoConvert_core_SELinuxOptions_To_v1_SELinuxOptions(in, out, s)
}

func autoConvert_v1_ScaleIOPersistentVolumeSource_To_core_ScaleIOPersistentVolumeSource(in *corev1.ScaleIOPersistentVolumeSource, out *core.ScaleIOPersistentVolumeSource, s conversion.Scope) error {
	out.Gateway = in.Gateway
	out.System = in.System
	out.SecretRef = (*core.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.SSLEnabled = in.SSLEnabled
	out.ProtectionDomain = in.ProtectionDomain
	out.StoragePool = in.StoragePool
	out.StorageMode = in.StorageMode
	out.VolumeName = in.VolumeName
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_ScaleIOPersistentVolumeSource_To_core_ScaleIOPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_ScaleIOPersistentVolumeSource_To_core_ScaleIOPersistentVolumeSource(in *corev1.ScaleIOPersistentVolumeSource, out *core.ScaleIOPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_ScaleIOPersistentVolumeSource_To_core_ScaleIOPersistentVolumeSource(in, out, s)
}

func autoConvert_core_ScaleIOPersistentVolumeSource_To_v1_ScaleIOPersistentVolumeSource(in *core.ScaleIOPersistentVolumeSource, out *corev1.ScaleIOPersistentVolumeSource, s conversion.Scope) error {
	out.Gateway = in.Gateway
	out.System = in.System
	out.SecretRef = (*corev1.SecretReference)(unsafe.Pointer(in.SecretRef))
	out.SSLEnabled = in.SSLEnabled
	out.ProtectionDomain = in.ProtectionDomain
	out.StoragePool = in.StoragePool
	out.StorageMode = in.StorageMode
	out.VolumeName = in.VolumeName
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_ScaleIOPersistentVolumeSource_To_v1_ScaleIOPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_ScaleIOPersistentVolumeSource_To_v1_ScaleIOPersistentVolumeSource(in *core.ScaleIOPersistentVolumeSource, out *corev1.ScaleIOPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_ScaleIOPersistentVolumeSource_To_v1_ScaleIOPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_ScaleIOVolumeSource_To_core_ScaleIOVolumeSource(in *corev1.ScaleIOVolumeSource, out *core.ScaleIOVolumeSource, s conversion.Scope) error {
	out.Gateway = in.Gateway
	out.System = in.System
	out.SecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.SSLEnabled = in.SSLEnabled
	out.ProtectionDomain = in.ProtectionDomain
	out.StoragePool = in.StoragePool
	out.StorageMode = in.StorageMode
	out.VolumeName = in.VolumeName
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_v1_ScaleIOVolumeSource_To_core_ScaleIOVolumeSource is an autogenerated conversion function.
func Convert_v1_ScaleIOVolumeSource_To_core_ScaleIOVolumeSource(in *corev1.ScaleIOVolumeSource, out *core.ScaleIOVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_ScaleIOVolumeSource_To_core_ScaleIOVolumeSource(in, out, s)
}

func autoConvert_core_ScaleIOVolumeSource_To_v1_ScaleIOVolumeSource(in *core.ScaleIOVolumeSource, out *corev1.ScaleIOVolumeSource, s conversion.Scope) error {
	out.Gateway = in.Gateway
	out.System = in.System
	out.SecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	out.SSLEnabled = in.SSLEnabled
	out.ProtectionDomain = in.ProtectionDomain
	out.StoragePool = in.StoragePool
	out.StorageMode = in.StorageMode
	out.VolumeName = in.VolumeName
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	return nil
}

// Convert_core_ScaleIOVolumeSource_To_v1_ScaleIOVolumeSource is an autogenerated conversion function.
func Convert_core_ScaleIOVolumeSource_To_v1_ScaleIOVolumeSource(in *core.ScaleIOVolumeSource, out *corev1.ScaleIOVolumeSource, s conversion.Scope) error {
	return autoConvert_core_ScaleIOVolumeSource_To_v1_ScaleIOVolumeSource(in, out, s)
}

func autoConvert_v1_ScopeSelector_To_core_ScopeSelector(in *corev1.ScopeSelector, out *core.ScopeSelector, s conversion.Scope) error {
	out.MatchExpressions = *(*[]core.ScopedResourceSelectorRequirement)(unsafe.Pointer(&in.MatchExpressions))
	return nil
}

// Convert_v1_ScopeSelector_To_core_ScopeSelector is an autogenerated conversion function.
func Convert_v1_ScopeSelector_To_core_ScopeSelector(in *corev1.ScopeSelector, out *core.ScopeSelector, s conversion.Scope) error {
	return autoConvert_v1_ScopeSelector_To_core_ScopeSelector(in, out, s)
}

func autoConvert_core_ScopeSelector_To_v1_ScopeSelector(in *core.ScopeSelector, out *corev1.ScopeSelector, s conversion.Scope) error {
	out.MatchExpressions = *(*[]corev1.ScopedResourceSelectorRequirement)(unsafe.Pointer(&in.MatchExpressions))
	return nil
}

// Convert_core_ScopeSelector_To_v1_ScopeSelector is an autogenerated conversion function.
func Convert_core_ScopeSelector_To_v1_ScopeSelector(in *core.ScopeSelector, out *corev1.ScopeSelector, s conversion.Scope) error {
	return autoConvert_core_ScopeSelector_To_v1_ScopeSelector(in, out, s)
}

func autoConvert_v1_ScopedResourceSelectorRequirement_To_core_ScopedResourceSelectorRequirement(in *corev1.ScopedResourceSelectorRequirement, out *core.ScopedResourceSelectorRequirement, s conversion.Scope) error {
	out.ScopeName = core.ResourceQuotaScope(in.ScopeName)
	out.Operator = core.ScopeSelectorOperator(in.Operator)
	out.Values = *(*[]string)(unsafe.Pointer(&in.Values))
	return nil
}

// Convert_v1_ScopedResourceSelectorRequirement_To_core_ScopedResourceSelectorRequirement is an autogenerated conversion function.
func Convert_v1_ScopedResourceSelectorRequirement_To_core_ScopedResourceSelectorRequirement(in *corev1.ScopedResourceSelectorRequirement, out *core.ScopedResourceSelectorRequirement, s conversion.Scope) error {
	return autoConvert_v1_ScopedResourceSelectorRequirement_To_core_ScopedResourceSelectorRequirement(in, out, s)
}

func autoConvert_core_ScopedResourceSelectorRequirement_To_v1_ScopedResourceSelectorRequirement(in *core.ScopedResourceSelectorRequirement, out *corev1.ScopedResourceSelectorRequirement, s conversion.Scope) error {
	out.ScopeName = corev1.ResourceQuotaScope(in.ScopeName)
	out.Operator = corev1.ScopeSelectorOperator(in.Operator)
	out.Values = *(*[]string)(unsafe.Pointer(&in.Values))
	return nil
}

// Convert_core_ScopedResourceSelectorRequirement_To_v1_ScopedResourceSelectorRequirement is an autogenerated conversion function.
func Convert_core_ScopedResourceSelectorRequirement_To_v1_ScopedResourceSelectorRequirement(in *core.ScopedResourceSelectorRequirement, out *corev1.ScopedResourceSelectorRequirement, s conversion.Scope) error {
	return autoConvert_core_ScopedResourceSelectorRequirement_To_v1_ScopedResourceSelectorRequirement(in, out, s)
}

func autoConvert_v1_SeccompProfile_To_core_SeccompProfile(in *corev1.SeccompProfile, out *core.SeccompProfile, s conversion.Scope) error {
	out.Type = core.SeccompProfileType(in.Type)
	out.LocalhostProfile = (*string)(unsafe.Pointer(in.LocalhostProfile))
	return nil
}

// Convert_v1_SeccompProfile_To_core_SeccompProfile is an autogenerated conversion function.
func Convert_v1_SeccompProfile_To_core_SeccompProfile(in *corev1.SeccompProfile, out *core.SeccompProfile, s conversion.Scope) error {
	return autoConvert_v1_SeccompProfile_To_core_SeccompProfile(in, out, s)
}

func autoConvert_core_SeccompProfile_To_v1_SeccompProfile(in *core.SeccompProfile, out *corev1.SeccompProfile, s conversion.Scope) error {
	out.Type = corev1.SeccompProfileType(in.Type)
	out.LocalhostProfile = (*string)(unsafe.Pointer(in.LocalhostProfile))
	return nil
}

// Convert_core_SeccompProfile_To_v1_SeccompProfile is an autogenerated conversion function.
func Convert_core_SeccompProfile_To_v1_SeccompProfile(in *core.SeccompProfile, out *corev1.SeccompProfile, s conversion.Scope) error {
	return autoConvert_core_SeccompProfile_To_v1_SeccompProfile(in, out, s)
}

func autoConvert_v1_Secret_To_core_Secret(in *corev1.Secret, out *core.Secret, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Immutable = (*bool)(unsafe.Pointer(in.Immutable))
	out.Data = *(*map[string][]byte)(unsafe.Pointer(&in.Data))
	// INFO: in.StringData opted out of conversion generation
	out.Type = core.SecretType(in.Type)
	return nil
}

func autoConvert_core_Secret_To_v1_Secret(in *core.Secret, out *corev1.Secret, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Immutable = (*bool)(unsafe.Pointer(in.Immutable))
	out.Data = *(*map[string][]byte)(unsafe.Pointer(&in.Data))
	out.Type = corev1.SecretType(in.Type)
	return nil
}

// Convert_core_Secret_To_v1_Secret is an autogenerated conversion function.
func Convert_core_Secret_To_v1_Secret(in *core.Secret, out *corev1.Secret, s conversion.Scope) error {
	return autoConvert_core_Secret_To_v1_Secret(in, out, s)
}

func autoConvert_v1_SecretEnvSource_To_core_SecretEnvSource(in *corev1.SecretEnvSource, out *core.SecretEnvSource, s conversion.Scope) error {
	if err := Convert_v1_LocalObjectReference_To_core_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_SecretEnvSource_To_core_SecretEnvSource is an autogenerated conversion function.
func Convert_v1_SecretEnvSource_To_core_SecretEnvSource(in *corev1.SecretEnvSource, out *core.SecretEnvSource, s conversion.Scope) error {
	return autoConvert_v1_SecretEnvSource_To_core_SecretEnvSource(in, out, s)
}

func autoConvert_core_SecretEnvSource_To_v1_SecretEnvSource(in *core.SecretEnvSource, out *corev1.SecretEnvSource, s conversion.Scope) error {
	if err := Convert_core_LocalObjectReference_To_v1_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_SecretEnvSource_To_v1_SecretEnvSource is an autogenerated conversion function.
func Convert_core_SecretEnvSource_To_v1_SecretEnvSource(in *core.SecretEnvSource, out *corev1.SecretEnvSource, s conversion.Scope) error {
	return autoConvert_core_SecretEnvSource_To_v1_SecretEnvSource(in, out, s)
}

func autoConvert_v1_SecretKeySelector_To_core_SecretKeySelector(in *corev1.SecretKeySelector, out *core.SecretKeySelector, s conversion.Scope) error {
	if err := Convert_v1_LocalObjectReference_To_core_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Key = in.Key
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_SecretKeySelector_To_core_SecretKeySelector is an autogenerated conversion function.
func Convert_v1_SecretKeySelector_To_core_SecretKeySelector(in *corev1.SecretKeySelector, out *core.SecretKeySelector, s conversion.Scope) error {
	return autoConvert_v1_SecretKeySelector_To_core_SecretKeySelector(in, out, s)
}

func autoConvert_core_SecretKeySelector_To_v1_SecretKeySelector(in *core.SecretKeySelector, out *corev1.SecretKeySelector, s conversion.Scope) error {
	if err := Convert_core_LocalObjectReference_To_v1_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Key = in.Key
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_SecretKeySelector_To_v1_SecretKeySelector is an autogenerated conversion function.
func Convert_core_SecretKeySelector_To_v1_SecretKeySelector(in *core.SecretKeySelector, out *corev1.SecretKeySelector, s conversion.Scope) error {
	return autoConvert_core_SecretKeySelector_To_v1_SecretKeySelector(in, out, s)
}

func autoConvert_v1_SecretList_To_core_SecretList(in *corev1.SecretList, out *core.SecretList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]core.Secret, len(*in))
		for i := range *in {
			if err := Convert_v1_Secret_To_core_Secret(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_SecretList_To_core_SecretList is an autogenerated conversion function.
func Convert_v1_SecretList_To_core_SecretList(in *corev1.SecretList, out *core.SecretList, s conversion.Scope) error {
	return autoConvert_v1_SecretList_To_core_SecretList(in, out, s)
}

func autoConvert_core_SecretList_To_v1_SecretList(in *core.SecretList, out *corev1.SecretList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]corev1.Secret, len(*in))
		for i := range *in {
			if err := Convert_core_Secret_To_v1_Secret(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_SecretList_To_v1_SecretList is an autogenerated conversion function.
func Convert_core_SecretList_To_v1_SecretList(in *core.SecretList, out *corev1.SecretList, s conversion.Scope) error {
	return autoConvert_core_SecretList_To_v1_SecretList(in, out, s)
}

func autoConvert_v1_SecretProjection_To_core_SecretProjection(in *corev1.SecretProjection, out *core.SecretProjection, s conversion.Scope) error {
	if err := Convert_v1_LocalObjectReference_To_core_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Items = *(*[]core.KeyToPath)(unsafe.Pointer(&in.Items))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_SecretProjection_To_core_SecretProjection is an autogenerated conversion function.
func Convert_v1_SecretProjection_To_core_SecretProjection(in *corev1.SecretProjection, out *core.SecretProjection, s conversion.Scope) error {
	return autoConvert_v1_SecretProjection_To_core_SecretProjection(in, out, s)
}

func autoConvert_core_SecretProjection_To_v1_SecretProjection(in *core.SecretProjection, out *corev1.SecretProjection, s conversion.Scope) error {
	if err := Convert_core_LocalObjectReference_To_v1_LocalObjectReference(&in.LocalObjectReference, &out.LocalObjectReference, s); err != nil {
		return err
	}
	out.Items = *(*[]corev1.KeyToPath)(unsafe.Pointer(&in.Items))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_SecretProjection_To_v1_SecretProjection is an autogenerated conversion function.
func Convert_core_SecretProjection_To_v1_SecretProjection(in *core.SecretProjection, out *corev1.SecretProjection, s conversion.Scope) error {
	return autoConvert_core_SecretProjection_To_v1_SecretProjection(in, out, s)
}

func autoConvert_v1_SecretReference_To_core_SecretReference(in *corev1.SecretReference, out *core.SecretReference, s conversion.Scope) error {
	out.Name = in.Name
	out.Namespace = in.Namespace
	return nil
}

// Convert_v1_SecretReference_To_core_SecretReference is an autogenerated conversion function.
func Convert_v1_SecretReference_To_core_SecretReference(in *corev1.SecretReference, out *core.SecretReference, s conversion.Scope) error {
	return autoConvert_v1_SecretReference_To_core_SecretReference(in, out, s)
}

func autoConvert_core_SecretReference_To_v1_SecretReference(in *core.SecretReference, out *corev1.SecretReference, s conversion.Scope) error {
	out.Name = in.Name
	out.Namespace = in.Namespace
	return nil
}

// Convert_core_SecretReference_To_v1_SecretReference is an autogenerated conversion function.
func Convert_core_SecretReference_To_v1_SecretReference(in *core.SecretReference, out *corev1.SecretReference, s conversion.Scope) error {
	return autoConvert_core_SecretReference_To_v1_SecretReference(in, out, s)
}

func autoConvert_v1_SecretVolumeSource_To_core_SecretVolumeSource(in *corev1.SecretVolumeSource, out *core.SecretVolumeSource, s conversion.Scope) error {
	out.SecretName = in.SecretName
	out.Items = *(*[]core.KeyToPath)(unsafe.Pointer(&in.Items))
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_v1_SecretVolumeSource_To_core_SecretVolumeSource is an autogenerated conversion function.
func Convert_v1_SecretVolumeSource_To_core_SecretVolumeSource(in *corev1.SecretVolumeSource, out *core.SecretVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_SecretVolumeSource_To_core_SecretVolumeSource(in, out, s)
}

func autoConvert_core_SecretVolumeSource_To_v1_SecretVolumeSource(in *core.SecretVolumeSource, out *corev1.SecretVolumeSource, s conversion.Scope) error {
	out.SecretName = in.SecretName
	out.Items = *(*[]corev1.KeyToPath)(unsafe.Pointer(&in.Items))
	out.DefaultMode = (*int32)(unsafe.Pointer(in.DefaultMode))
	out.Optional = (*bool)(unsafe.Pointer(in.Optional))
	return nil
}

// Convert_core_SecretVolumeSource_To_v1_SecretVolumeSource is an autogenerated conversion function.
func Convert_core_SecretVolumeSource_To_v1_SecretVolumeSource(in *core.SecretVolumeSource, out *corev1.SecretVolumeSource, s conversion.Scope) error {
	return autoConvert_core_SecretVolumeSource_To_v1_SecretVolumeSource(in, out, s)
}

func autoConvert_v1_SecurityContext_To_core_SecurityContext(in *corev1.SecurityContext, out *core.SecurityContext, s conversion.Scope) error {
	out.Capabilities = (*core.Capabilities)(unsafe.Pointer(in.Capabilities))
	out.Privileged = (*bool)(unsafe.Pointer(in.Privileged))
	out.SELinuxOptions = (*core.SELinuxOptions)(unsafe.Pointer(in.SELinuxOptions))
	out.WindowsOptions = (*core.WindowsSecurityContextOptions)(unsafe.Pointer(in.WindowsOptions))
	out.RunAsUser = (*int64)(unsafe.Pointer(in.RunAsUser))
	out.RunAsGroup = (*int64)(unsafe.Pointer(in.RunAsGroup))
	out.RunAsNonRoot = (*bool)(unsafe.Pointer(in.RunAsNonRoot))
	out.ReadOnlyRootFilesystem = (*bool)(unsafe.Pointer(in.ReadOnlyRootFilesystem))
	out.AllowPrivilegeEscalation = (*bool)(unsafe.Pointer(in.AllowPrivilegeEscalation))
	out.ProcMount = (*core.ProcMountType)(unsafe.Pointer(in.ProcMount))
	out.SeccompProfile = (*core.SeccompProfile)(unsafe.Pointer(in.SeccompProfile))
	out.AppArmorProfile = (*core.AppArmorProfile)(unsafe.Pointer(in.AppArmorProfile))
	return nil
}

// Convert_v1_SecurityContext_To_core_SecurityContext is an autogenerated conversion function.
func Convert_v1_SecurityContext_To_core_SecurityContext(in *corev1.SecurityContext, out *core.SecurityContext, s conversion.Scope) error {
	return autoConvert_v1_SecurityContext_To_core_SecurityContext(in, out, s)
}

func autoConvert_core_SecurityContext_To_v1_SecurityContext(in *core.SecurityContext, out *corev1.SecurityContext, s conversion.Scope) error {
	out.Capabilities = (*corev1.Capabilities)(unsafe.Pointer(in.Capabilities))
	out.Privileged = (*bool)(unsafe.Pointer(in.Privileged))
	out.SELinuxOptions = (*corev1.SELinuxOptions)(unsafe.Pointer(in.SELinuxOptions))
	out.WindowsOptions = (*corev1.WindowsSecurityContextOptions)(unsafe.Pointer(in.WindowsOptions))
	out.RunAsUser = (*int64)(unsafe.Pointer(in.RunAsUser))
	out.RunAsGroup = (*int64)(unsafe.Pointer(in.RunAsGroup))
	out.RunAsNonRoot = (*bool)(unsafe.Pointer(in.RunAsNonRoot))
	out.ReadOnlyRootFilesystem = (*bool)(unsafe.Pointer(in.ReadOnlyRootFilesystem))
	out.AllowPrivilegeEscalation = (*bool)(unsafe.Pointer(in.AllowPrivilegeEscalation))
	out.ProcMount = (*corev1.ProcMountType)(unsafe.Pointer(in.ProcMount))
	out.SeccompProfile = (*corev1.SeccompProfile)(unsafe.Pointer(in.SeccompProfile))
	out.AppArmorProfile = (*corev1.AppArmorProfile)(unsafe.Pointer(in.AppArmorProfile))
	return nil
}

// Convert_core_SecurityContext_To_v1_SecurityContext is an autogenerated conversion function.
func Convert_core_SecurityContext_To_v1_SecurityContext(in *core.SecurityContext, out *corev1.SecurityContext, s conversion.Scope) error {
	return autoConvert_core_SecurityContext_To_v1_SecurityContext(in, out, s)
}

func autoConvert_v1_SerializedReference_To_core_SerializedReference(in *corev1.SerializedReference, out *core.SerializedReference, s conversion.Scope) error {
	if err := Convert_v1_ObjectReference_To_core_ObjectReference(&in.Reference, &out.Reference, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_SerializedReference_To_core_SerializedReference is an autogenerated conversion function.
func Convert_v1_SerializedReference_To_core_SerializedReference(in *corev1.SerializedReference, out *core.SerializedReference, s conversion.Scope) error {
	return autoConvert_v1_SerializedReference_To_core_SerializedReference(in, out, s)
}

func autoConvert_core_SerializedReference_To_v1_SerializedReference(in *core.SerializedReference, out *corev1.SerializedReference, s conversion.Scope) error {
	if err := Convert_core_ObjectReference_To_v1_ObjectReference(&in.Reference, &out.Reference, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_SerializedReference_To_v1_SerializedReference is an autogenerated conversion function.
func Convert_core_SerializedReference_To_v1_SerializedReference(in *core.SerializedReference, out *corev1.SerializedReference, s conversion.Scope) error {
	return autoConvert_core_SerializedReference_To_v1_SerializedReference(in, out, s)
}

func autoConvert_v1_Service_To_core_Service(in *corev1.Service, out *core.Service, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ServiceSpec_To_core_ServiceSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ServiceStatus_To_core_ServiceStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Service_To_core_Service is an autogenerated conversion function.
func Convert_v1_Service_To_core_Service(in *corev1.Service, out *core.Service, s conversion.Scope) error {
	return autoConvert_v1_Service_To_core_Service(in, out, s)
}

func autoConvert_core_Service_To_v1_Service(in *core.Service, out *corev1.Service, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_core_ServiceSpec_To_v1_ServiceSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_core_ServiceStatus_To_v1_ServiceStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_Service_To_v1_Service is an autogenerated conversion function.
func Convert_core_Service_To_v1_Service(in *core.Service, out *corev1.Service, s conversion.Scope) error {
	return autoConvert_core_Service_To_v1_Service(in, out, s)
}

func autoConvert_v1_ServiceAccount_To_core_ServiceAccount(in *corev1.ServiceAccount, out *core.ServiceAccount, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Secrets = *(*[]core.ObjectReference)(unsafe.Pointer(&in.Secrets))
	out.ImagePullSecrets = *(*[]core.LocalObjectReference)(unsafe.Pointer(&in.ImagePullSecrets))
	out.AutomountServiceAccountToken = (*bool)(unsafe.Pointer(in.AutomountServiceAccountToken))
	return nil
}

// Convert_v1_ServiceAccount_To_core_ServiceAccount is an autogenerated conversion function.
func Convert_v1_ServiceAccount_To_core_ServiceAccount(in *corev1.ServiceAccount, out *core.ServiceAccount, s conversion.Scope) error {
	return autoConvert_v1_ServiceAccount_To_core_ServiceAccount(in, out, s)
}

func autoConvert_core_ServiceAccount_To_v1_ServiceAccount(in *core.ServiceAccount, out *corev1.ServiceAccount, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Secrets = *(*[]corev1.ObjectReference)(unsafe.Pointer(&in.Secrets))
	out.ImagePullSecrets = *(*[]corev1.LocalObjectReference)(unsafe.Pointer(&in.ImagePullSecrets))
	out.AutomountServiceAccountToken = (*bool)(unsafe.Pointer(in.AutomountServiceAccountToken))
	return nil
}

// Convert_core_ServiceAccount_To_v1_ServiceAccount is an autogenerated conversion function.
func Convert_core_ServiceAccount_To_v1_ServiceAccount(in *core.ServiceAccount, out *corev1.ServiceAccount, s conversion.Scope) error {
	return autoConvert_core_ServiceAccount_To_v1_ServiceAccount(in, out, s)
}

func autoConvert_v1_ServiceAccountList_To_core_ServiceAccountList(in *corev1.ServiceAccountList, out *core.ServiceAccountList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]core.ServiceAccount)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ServiceAccountList_To_core_ServiceAccountList is an autogenerated conversion function.
func Convert_v1_ServiceAccountList_To_core_ServiceAccountList(in *corev1.ServiceAccountList, out *core.ServiceAccountList, s conversion.Scope) error {
	return autoConvert_v1_ServiceAccountList_To_core_ServiceAccountList(in, out, s)
}

func autoConvert_core_ServiceAccountList_To_v1_ServiceAccountList(in *core.ServiceAccountList, out *corev1.ServiceAccountList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]corev1.ServiceAccount)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_core_ServiceAccountList_To_v1_ServiceAccountList is an autogenerated conversion function.
func Convert_core_ServiceAccountList_To_v1_ServiceAccountList(in *core.ServiceAccountList, out *corev1.ServiceAccountList, s conversion.Scope) error {
	return autoConvert_core_ServiceAccountList_To_v1_ServiceAccountList(in, out, s)
}

func autoConvert_v1_ServiceAccountTokenProjection_To_core_ServiceAccountTokenProjection(in *corev1.ServiceAccountTokenProjection, out *core.ServiceAccountTokenProjection, s conversion.Scope) error {
	out.Audience = in.Audience
	if err := metav1.Convert_Pointer_int64_To_int64(&in.ExpirationSeconds, &out.ExpirationSeconds, s); err != nil {
		return err
	}
	out.Path = in.Path
	return nil
}

// Convert_v1_ServiceAccountTokenProjection_To_core_ServiceAccountTokenProjection is an autogenerated conversion function.
func Convert_v1_ServiceAccountTokenProjection_To_core_ServiceAccountTokenProjection(in *corev1.ServiceAccountTokenProjection, out *core.ServiceAccountTokenProjection, s conversion.Scope) error {
	return autoConvert_v1_ServiceAccountTokenProjection_To_core_ServiceAccountTokenProjection(in, out, s)
}

func autoConvert_core_ServiceAccountTokenProjection_To_v1_ServiceAccountTokenProjection(in *core.ServiceAccountTokenProjection, out *corev1.ServiceAccountTokenProjection, s conversion.Scope) error {
	out.Audience = in.Audience
	if err := metav1.Convert_int64_To_Pointer_int64(&in.ExpirationSeconds, &out.ExpirationSeconds, s); err != nil {
		return err
	}
	out.Path = in.Path
	return nil
}

// Convert_core_ServiceAccountTokenProjection_To_v1_ServiceAccountTokenProjection is an autogenerated conversion function.
func Convert_core_ServiceAccountTokenProjection_To_v1_ServiceAccountTokenProjection(in *core.ServiceAccountTokenProjection, out *corev1.ServiceAccountTokenProjection, s conversion.Scope) error {
	return autoConvert_core_ServiceAccountTokenProjection_To_v1_ServiceAccountTokenProjection(in, out, s)
}

func autoConvert_v1_ServiceList_To_core_ServiceList(in *corev1.ServiceList, out *core.ServiceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]core.Service, len(*in))
		for i := range *in {
			if err := Convert_v1_Service_To_core_Service(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_ServiceList_To_core_ServiceList is an autogenerated conversion function.
func Convert_v1_ServiceList_To_core_ServiceList(in *corev1.ServiceList, out *core.ServiceList, s conversion.Scope) error {
	return autoConvert_v1_ServiceList_To_core_ServiceList(in, out, s)
}

func autoConvert_core_ServiceList_To_v1_ServiceList(in *core.ServiceList, out *corev1.ServiceList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]corev1.Service, len(*in))
		for i := range *in {
			if err := Convert_core_Service_To_v1_Service(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_core_ServiceList_To_v1_ServiceList is an autogenerated conversion function.
func Convert_core_ServiceList_To_v1_ServiceList(in *core.ServiceList, out *corev1.ServiceList, s conversion.Scope) error {
	return autoConvert_core_ServiceList_To_v1_ServiceList(in, out, s)
}

func autoConvert_v1_ServicePort_To_core_ServicePort(in *corev1.ServicePort, out *core.ServicePort, s conversion.Scope) error {
	out.Name = in.Name
	out.Protocol = core.Protocol(in.Protocol)
	out.AppProtocol = (*string)(unsafe.Pointer(in.AppProtocol))
	out.Port = in.Port
	out.TargetPort = in.TargetPort
	out.NodePort = in.NodePort
	return nil
}

// Convert_v1_ServicePort_To_core_ServicePort is an autogenerated conversion function.
func Convert_v1_ServicePort_To_core_ServicePort(in *corev1.ServicePort, out *core.ServicePort, s conversion.Scope) error {
	return autoConvert_v1_ServicePort_To_core_ServicePort(in, out, s)
}

func autoConvert_core_ServicePort_To_v1_ServicePort(in *core.ServicePort, out *corev1.ServicePort, s conversion.Scope) error {
	out.Name = in.Name
	out.Protocol = corev1.Protocol(in.Protocol)
	out.AppProtocol = (*string)(unsafe.Pointer(in.AppProtocol))
	out.Port = in.Port
	out.TargetPort = in.TargetPort
	out.NodePort = in.NodePort
	return nil
}

// Convert_core_ServicePort_To_v1_ServicePort is an autogenerated conversion function.
func Convert_core_ServicePort_To_v1_ServicePort(in *core.ServicePort, out *corev1.ServicePort, s conversion.Scope) error {
	return autoConvert_core_ServicePort_To_v1_ServicePort(in, out, s)
}

func autoConvert_v1_ServiceProxyOptions_To_core_ServiceProxyOptions(in *corev1.ServiceProxyOptions, out *core.ServiceProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_v1_ServiceProxyOptions_To_core_ServiceProxyOptions is an autogenerated conversion function.
func Convert_v1_ServiceProxyOptions_To_core_ServiceProxyOptions(in *corev1.ServiceProxyOptions, out *core.ServiceProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_ServiceProxyOptions_To_core_ServiceProxyOptions(in, out, s)
}

func autoConvert_core_ServiceProxyOptions_To_v1_ServiceProxyOptions(in *core.ServiceProxyOptions, out *corev1.ServiceProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_core_ServiceProxyOptions_To_v1_ServiceProxyOptions is an autogenerated conversion function.
func Convert_core_ServiceProxyOptions_To_v1_ServiceProxyOptions(in *core.ServiceProxyOptions, out *corev1.ServiceProxyOptions, s conversion.Scope) error {
	return autoConvert_core_ServiceProxyOptions_To_v1_ServiceProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_ServiceProxyOptions(in *url.Values, out *corev1.ServiceProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["path"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Path, s); err != nil {
			return err
		}
	} else {
		out.Path = ""
	}
	return nil
}

// Convert_url_Values_To_v1_ServiceProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_ServiceProxyOptions(in *url.Values, out *corev1.ServiceProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_ServiceProxyOptions(in, out, s)
}

func autoConvert_v1_ServiceSpec_To_core_ServiceSpec(in *corev1.ServiceSpec, out *core.ServiceSpec, s conversion.Scope) error {
	out.Ports = *(*[]core.ServicePort)(unsafe.Pointer(&in.Ports))
	out.Selector = *(*map[string]string)(unsafe.Pointer(&in.Selector))
	out.ClusterIP = in.ClusterIP
	out.ClusterIPs = *(*[]string)(unsafe.Pointer(&in.ClusterIPs))
	out.Type = core.ServiceType(in.Type)
	out.ExternalIPs = *(*[]string)(unsafe.Pointer(&in.ExternalIPs))
	out.SessionAffinity = core.ServiceAffinity(in.SessionAffinity)
	out.LoadBalancerIP = in.LoadBalancerIP
	out.LoadBalancerSourceRanges = *(*[]string)(unsafe.Pointer(&in.LoadBalancerSourceRanges))
	out.ExternalName = in.ExternalName
	out.ExternalTrafficPolicy = core.ServiceExternalTrafficPolicy(in.ExternalTrafficPolicy)
	out.HealthCheckNodePort = in.HealthCheckNodePort
	out.PublishNotReadyAddresses = in.PublishNotReadyAddresses
	out.SessionAffinityConfig = (*core.SessionAffinityConfig)(unsafe.Pointer(in.SessionAffinityConfig))
	out.IPFamilies = *(*[]core.IPFamily)(unsafe.Pointer(&in.IPFamilies))
	out.IPFamilyPolicy = (*core.IPFamilyPolicy)(unsafe.Pointer(in.IPFamilyPolicy))
	out.AllocateLoadBalancerNodePorts = (*bool)(unsafe.Pointer(in.AllocateLoadBalancerNodePorts))
	out.LoadBalancerClass = (*string)(unsafe.Pointer(in.LoadBalancerClass))
	out.InternalTrafficPolicy = (*core.ServiceInternalTrafficPolicy)(unsafe.Pointer(in.InternalTrafficPolicy))
	out.TrafficDistribution = (*string)(unsafe.Pointer(in.TrafficDistribution))
	return nil
}

// Convert_v1_ServiceSpec_To_core_ServiceSpec is an autogenerated conversion function.
func Convert_v1_ServiceSpec_To_core_ServiceSpec(in *corev1.ServiceSpec, out *core.ServiceSpec, s conversion.Scope) error {
	return autoConvert_v1_ServiceSpec_To_core_ServiceSpec(in, out, s)
}

func autoConvert_core_ServiceSpec_To_v1_ServiceSpec(in *core.ServiceSpec, out *corev1.ServiceSpec, s conversion.Scope) error {
	out.Type = corev1.ServiceType(in.Type)
	out.Ports = *(*[]corev1.ServicePort)(unsafe.Pointer(&in.Ports))
	out.Selector = *(*map[string]string)(unsafe.Pointer(&in.Selector))
	out.ClusterIP = in.ClusterIP
	out.ClusterIPs = *(*[]string)(unsafe.Pointer(&in.ClusterIPs))
	out.IPFamilies = *(*[]corev1.IPFamily)(unsafe.Pointer(&in.IPFamilies))
	out.IPFamilyPolicy = (*corev1.IPFamilyPolicy)(unsafe.Pointer(in.IPFamilyPolicy))
	out.ExternalName = in.ExternalName
	out.ExternalIPs = *(*[]string)(unsafe.Pointer(&in.ExternalIPs))
	out.LoadBalancerIP = in.LoadBalancerIP
	out.SessionAffinity = corev1.ServiceAffinity(in.SessionAffinity)
	out.SessionAffinityConfig = (*corev1.SessionAffinityConfig)(unsafe.Pointer(in.SessionAffinityConfig))
	out.LoadBalancerSourceRanges = *(*[]string)(unsafe.Pointer(&in.LoadBalancerSourceRanges))
	out.ExternalTrafficPolicy = corev1.ServiceExternalTrafficPolicy(in.ExternalTrafficPolicy)
	out.HealthCheckNodePort = in.HealthCheckNodePort
	out.PublishNotReadyAddresses = in.PublishNotReadyAddresses
	out.AllocateLoadBalancerNodePorts = (*bool)(unsafe.Pointer(in.AllocateLoadBalancerNodePorts))
	out.LoadBalancerClass = (*string)(unsafe.Pointer(in.LoadBalancerClass))
	out.InternalTrafficPolicy = (*corev1.ServiceInternalTrafficPolicy)(unsafe.Pointer(in.InternalTrafficPolicy))
	out.TrafficDistribution = (*string)(unsafe.Pointer(in.TrafficDistribution))
	return nil
}

// Convert_core_ServiceSpec_To_v1_ServiceSpec is an autogenerated conversion function.
func Convert_core_ServiceSpec_To_v1_ServiceSpec(in *core.ServiceSpec, out *corev1.ServiceSpec, s conversion.Scope) error {
	return autoConvert_core_ServiceSpec_To_v1_ServiceSpec(in, out, s)
}

func autoConvert_v1_ServiceStatus_To_core_ServiceStatus(in *corev1.ServiceStatus, out *core.ServiceStatus, s conversion.Scope) error {
	if err := Convert_v1_LoadBalancerStatus_To_core_LoadBalancerStatus(&in.LoadBalancer, &out.LoadBalancer, s); err != nil {
		return err
	}
	out.Conditions = *(*[]metav1.Condition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_v1_ServiceStatus_To_core_ServiceStatus is an autogenerated conversion function.
func Convert_v1_ServiceStatus_To_core_ServiceStatus(in *corev1.ServiceStatus, out *core.ServiceStatus, s conversion.Scope) error {
	return autoConvert_v1_ServiceStatus_To_core_ServiceStatus(in, out, s)
}

func autoConvert_core_ServiceStatus_To_v1_ServiceStatus(in *core.ServiceStatus, out *corev1.ServiceStatus, s conversion.Scope) error {
	if err := Convert_core_LoadBalancerStatus_To_v1_LoadBalancerStatus(&in.LoadBalancer, &out.LoadBalancer, s); err != nil {
		return err
	}
	out.Conditions = *(*[]metav1.Condition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_core_ServiceStatus_To_v1_ServiceStatus is an autogenerated conversion function.
func Convert_core_ServiceStatus_To_v1_ServiceStatus(in *core.ServiceStatus, out *corev1.ServiceStatus, s conversion.Scope) error {
	return autoConvert_core_ServiceStatus_To_v1_ServiceStatus(in, out, s)
}

func autoConvert_v1_SessionAffinityConfig_To_core_SessionAffinityConfig(in *corev1.SessionAffinityConfig, out *core.SessionAffinityConfig, s conversion.Scope) error {
	out.ClientIP = (*core.ClientIPConfig)(unsafe.Pointer(in.ClientIP))
	return nil
}

// Convert_v1_SessionAffinityConfig_To_core_SessionAffinityConfig is an autogenerated conversion function.
func Convert_v1_SessionAffinityConfig_To_core_SessionAffinityConfig(in *corev1.SessionAffinityConfig, out *core.SessionAffinityConfig, s conversion.Scope) error {
	return autoConvert_v1_SessionAffinityConfig_To_core_SessionAffinityConfig(in, out, s)
}

func autoConvert_core_SessionAffinityConfig_To_v1_SessionAffinityConfig(in *core.SessionAffinityConfig, out *corev1.SessionAffinityConfig, s conversion.Scope) error {
	out.ClientIP = (*corev1.ClientIPConfig)(unsafe.Pointer(in.ClientIP))
	return nil
}

// Convert_core_SessionAffinityConfig_To_v1_SessionAffinityConfig is an autogenerated conversion function.
func Convert_core_SessionAffinityConfig_To_v1_SessionAffinityConfig(in *core.SessionAffinityConfig, out *corev1.SessionAffinityConfig, s conversion.Scope) error {
	return autoConvert_core_SessionAffinityConfig_To_v1_SessionAffinityConfig(in, out, s)
}

func autoConvert_v1_SleepAction_To_core_SleepAction(in *corev1.SleepAction, out *core.SleepAction, s conversion.Scope) error {
	out.Seconds = in.Seconds
	return nil
}

// Convert_v1_SleepAction_To_core_SleepAction is an autogenerated conversion function.
func Convert_v1_SleepAction_To_core_SleepAction(in *corev1.SleepAction, out *core.SleepAction, s conversion.Scope) error {
	return autoConvert_v1_SleepAction_To_core_SleepAction(in, out, s)
}

func autoConvert_core_SleepAction_To_v1_SleepAction(in *core.SleepAction, out *corev1.SleepAction, s conversion.Scope) error {
	out.Seconds = in.Seconds
	return nil
}

// Convert_core_SleepAction_To_v1_SleepAction is an autogenerated conversion function.
func Convert_core_SleepAction_To_v1_SleepAction(in *core.SleepAction, out *corev1.SleepAction, s conversion.Scope) error {
	return autoConvert_core_SleepAction_To_v1_SleepAction(in, out, s)
}

func autoConvert_v1_StorageOSPersistentVolumeSource_To_core_StorageOSPersistentVolumeSource(in *corev1.StorageOSPersistentVolumeSource, out *core.StorageOSPersistentVolumeSource, s conversion.Scope) error {
	out.VolumeName = in.VolumeName
	out.VolumeNamespace = in.VolumeNamespace
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*core.ObjectReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_v1_StorageOSPersistentVolumeSource_To_core_StorageOSPersistentVolumeSource is an autogenerated conversion function.
func Convert_v1_StorageOSPersistentVolumeSource_To_core_StorageOSPersistentVolumeSource(in *corev1.StorageOSPersistentVolumeSource, out *core.StorageOSPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_StorageOSPersistentVolumeSource_To_core_StorageOSPersistentVolumeSource(in, out, s)
}

func autoConvert_core_StorageOSPersistentVolumeSource_To_v1_StorageOSPersistentVolumeSource(in *core.StorageOSPersistentVolumeSource, out *corev1.StorageOSPersistentVolumeSource, s conversion.Scope) error {
	out.VolumeName = in.VolumeName
	out.VolumeNamespace = in.VolumeNamespace
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*corev1.ObjectReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_core_StorageOSPersistentVolumeSource_To_v1_StorageOSPersistentVolumeSource is an autogenerated conversion function.
func Convert_core_StorageOSPersistentVolumeSource_To_v1_StorageOSPersistentVolumeSource(in *core.StorageOSPersistentVolumeSource, out *corev1.StorageOSPersistentVolumeSource, s conversion.Scope) error {
	return autoConvert_core_StorageOSPersistentVolumeSource_To_v1_StorageOSPersistentVolumeSource(in, out, s)
}

func autoConvert_v1_StorageOSVolumeSource_To_core_StorageOSVolumeSource(in *corev1.StorageOSVolumeSource, out *core.StorageOSVolumeSource, s conversion.Scope) error {
	out.VolumeName = in.VolumeName
	out.VolumeNamespace = in.VolumeNamespace
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*core.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_v1_StorageOSVolumeSource_To_core_StorageOSVolumeSource is an autogenerated conversion function.
func Convert_v1_StorageOSVolumeSource_To_core_StorageOSVolumeSource(in *corev1.StorageOSVolumeSource, out *core.StorageOSVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_StorageOSVolumeSource_To_core_StorageOSVolumeSource(in, out, s)
}

func autoConvert_core_StorageOSVolumeSource_To_v1_StorageOSVolumeSource(in *core.StorageOSVolumeSource, out *corev1.StorageOSVolumeSource, s conversion.Scope) error {
	out.VolumeName = in.VolumeName
	out.VolumeNamespace = in.VolumeNamespace
	out.FSType = in.FSType
	out.ReadOnly = in.ReadOnly
	out.SecretRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.SecretRef))
	return nil
}

// Convert_core_StorageOSVolumeSource_To_v1_StorageOSVolumeSource is an autogenerated conversion function.
func Convert_core_StorageOSVolumeSource_To_v1_StorageOSVolumeSource(in *core.StorageOSVolumeSource, out *corev1.StorageOSVolumeSource, s conversion.Scope) error {
	return autoConvert_core_StorageOSVolumeSource_To_v1_StorageOSVolumeSource(in, out, s)
}

func autoConvert_v1_Sysctl_To_core_Sysctl(in *corev1.Sysctl, out *core.Sysctl, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = in.Value
	return nil
}

// Convert_v1_Sysctl_To_core_Sysctl is an autogenerated conversion function.
func Convert_v1_Sysctl_To_core_Sysctl(in *corev1.Sysctl, out *core.Sysctl, s conversion.Scope) error {
	return autoConvert_v1_Sysctl_To_core_Sysctl(in, out, s)
}

func autoConvert_core_Sysctl_To_v1_Sysctl(in *core.Sysctl, out *corev1.Sysctl, s conversion.Scope) error {
	out.Name = in.Name
	out.Value = in.Value
	return nil
}

// Convert_core_Sysctl_To_v1_Sysctl is an autogenerated conversion function.
func Convert_core_Sysctl_To_v1_Sysctl(in *core.Sysctl, out *corev1.Sysctl, s conversion.Scope) error {
	return autoConvert_core_Sysctl_To_v1_Sysctl(in, out, s)
}

func autoConvert_v1_TCPSocketAction_To_core_TCPSocketAction(in *corev1.TCPSocketAction, out *core.TCPSocketAction, s conversion.Scope) error {
	out.Port = in.Port
	out.Host = in.Host
	return nil
}

// Convert_v1_TCPSocketAction_To_core_TCPSocketAction is an autogenerated conversion function.
func Convert_v1_TCPSocketAction_To_core_TCPSocketAction(in *corev1.TCPSocketAction, out *core.TCPSocketAction, s conversion.Scope) error {
	return autoConvert_v1_TCPSocketAction_To_core_TCPSocketAction(in, out, s)
}

func autoConvert_core_TCPSocketAction_To_v1_TCPSocketAction(in *core.TCPSocketAction, out *corev1.TCPSocketAction, s conversion.Scope) error {
	out.Port = in.Port
	out.Host = in.Host
	return nil
}

// Convert_core_TCPSocketAction_To_v1_TCPSocketAction is an autogenerated conversion function.
func Convert_core_TCPSocketAction_To_v1_TCPSocketAction(in *core.TCPSocketAction, out *corev1.TCPSocketAction, s conversion.Scope) error {
	return autoConvert_core_TCPSocketAction_To_v1_TCPSocketAction(in, out, s)
}

func autoConvert_v1_Taint_To_core_Taint(in *corev1.Taint, out *core.Taint, s conversion.Scope) error {
	out.Key = in.Key
	out.Value = in.Value
	out.Effect = core.TaintEffect(in.Effect)
	out.TimeAdded = (*metav1.Time)(unsafe.Pointer(in.TimeAdded))
	return nil
}

// Convert_v1_Taint_To_core_Taint is an autogenerated conversion function.
func Convert_v1_Taint_To_core_Taint(in *corev1.Taint, out *core.Taint, s conversion.Scope) error {
	return autoConvert_v1_Taint_To_core_Taint(in, out, s)
}

func autoConvert_core_Taint_To_v1_Taint(in *core.Taint, out *corev1.Taint, s conversion.Scope) error {
	out.Key = in.Key
	out.Value = in.Value
	out.Effect = corev1.TaintEffect(in.Effect)
	out.TimeAdded = (*metav1.Time)(unsafe.Pointer(in.TimeAdded))
	return nil
}

// Convert_core_Taint_To_v1_Taint is an autogenerated conversion function.
func Convert_core_Taint_To_v1_Taint(in *core.Taint, out *corev1.Taint, s conversion.Scope) error {
	return autoConvert_core_Taint_To_v1_Taint(in, out, s)
}

func autoConvert_v1_Toleration_To_core_Toleration(in *corev1.Toleration, out *core.Toleration, s conversion.Scope) error {
	out.Key = in.Key
	out.Operator = core.TolerationOperator(in.Operator)
	out.Value = in.Value
	out.Effect = core.TaintEffect(in.Effect)
	out.TolerationSeconds = (*int64)(unsafe.Pointer(in.TolerationSeconds))
	return nil
}

// Convert_v1_Toleration_To_core_Toleration is an autogenerated conversion function.
func Convert_v1_Toleration_To_core_Toleration(in *corev1.Toleration, out *core.Toleration, s conversion.Scope) error {
	return autoConvert_v1_Toleration_To_core_Toleration(in, out, s)
}

func autoConvert_core_Toleration_To_v1_Toleration(in *core.Toleration, out *corev1.Toleration, s conversion.Scope) error {
	out.Key = in.Key
	out.Operator = corev1.TolerationOperator(in.Operator)
	out.Value = in.Value
	out.Effect = corev1.TaintEffect(in.Effect)
	out.TolerationSeconds = (*int64)(unsafe.Pointer(in.TolerationSeconds))
	return nil
}

// Convert_core_Toleration_To_v1_Toleration is an autogenerated conversion function.
func Convert_core_Toleration_To_v1_Toleration(in *core.Toleration, out *corev1.Toleration, s conversion.Scope) error {
	return autoConvert_core_Toleration_To_v1_Toleration(in, out, s)
}

func autoConvert_v1_TopologySelectorLabelRequirement_To_core_TopologySelectorLabelRequirement(in *corev1.TopologySelectorLabelRequirement, out *core.TopologySelectorLabelRequirement, s conversion.Scope) error {
	out.Key = in.Key
	out.Values = *(*[]string)(unsafe.Pointer(&in.Values))
	return nil
}

// Convert_v1_TopologySelectorLabelRequirement_To_core_TopologySelectorLabelRequirement is an autogenerated conversion function.
func Convert_v1_TopologySelectorLabelRequirement_To_core_TopologySelectorLabelRequirement(in *corev1.TopologySelectorLabelRequirement, out *core.TopologySelectorLabelRequirement, s conversion.Scope) error {
	return autoConvert_v1_TopologySelectorLabelRequirement_To_core_TopologySelectorLabelRequirement(in, out, s)
}

func autoConvert_core_TopologySelectorLabelRequirement_To_v1_TopologySelectorLabelRequirement(in *core.TopologySelectorLabelRequirement, out *corev1.TopologySelectorLabelRequirement, s conversion.Scope) error {
	out.Key = in.Key
	out.Values = *(*[]string)(unsafe.Pointer(&in.Values))
	return nil
}

// Convert_core_TopologySelectorLabelRequirement_To_v1_TopologySelectorLabelRequirement is an autogenerated conversion function.
func Convert_core_TopologySelectorLabelRequirement_To_v1_TopologySelectorLabelRequirement(in *core.TopologySelectorLabelRequirement, out *corev1.TopologySelectorLabelRequirement, s conversion.Scope) error {
	return autoConvert_core_TopologySelectorLabelRequirement_To_v1_TopologySelectorLabelRequirement(in, out, s)
}

func autoConvert_v1_TopologySelectorTerm_To_core_TopologySelectorTerm(in *corev1.TopologySelectorTerm, out *core.TopologySelectorTerm, s conversion.Scope) error {
	out.MatchLabelExpressions = *(*[]core.TopologySelectorLabelRequirement)(unsafe.Pointer(&in.MatchLabelExpressions))
	return nil
}

// Convert_v1_TopologySelectorTerm_To_core_TopologySelectorTerm is an autogenerated conversion function.
func Convert_v1_TopologySelectorTerm_To_core_TopologySelectorTerm(in *corev1.TopologySelectorTerm, out *core.TopologySelectorTerm, s conversion.Scope) error {
	return autoConvert_v1_TopologySelectorTerm_To_core_TopologySelectorTerm(in, out, s)
}

func autoConvert_core_TopologySelectorTerm_To_v1_TopologySelectorTerm(in *core.TopologySelectorTerm, out *corev1.TopologySelectorTerm, s conversion.Scope) error {
	out.MatchLabelExpressions = *(*[]corev1.TopologySelectorLabelRequirement)(unsafe.Pointer(&in.MatchLabelExpressions))
	return nil
}

// Convert_core_TopologySelectorTerm_To_v1_TopologySelectorTerm is an autogenerated conversion function.
func Convert_core_TopologySelectorTerm_To_v1_TopologySelectorTerm(in *core.TopologySelectorTerm, out *corev1.TopologySelectorTerm, s conversion.Scope) error {
	return autoConvert_core_TopologySelectorTerm_To_v1_TopologySelectorTerm(in, out, s)
}

func autoConvert_v1_TopologySpreadConstraint_To_core_TopologySpreadConstraint(in *corev1.TopologySpreadConstraint, out *core.TopologySpreadConstraint, s conversion.Scope) error {
	out.MaxSkew = in.MaxSkew
	out.TopologyKey = in.TopologyKey
	out.WhenUnsatisfiable = core.UnsatisfiableConstraintAction(in.WhenUnsatisfiable)
	out.LabelSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.LabelSelector))
	out.MinDomains = (*int32)(unsafe.Pointer(in.MinDomains))
	out.NodeAffinityPolicy = (*core.NodeInclusionPolicy)(unsafe.Pointer(in.NodeAffinityPolicy))
	out.NodeTaintsPolicy = (*core.NodeInclusionPolicy)(unsafe.Pointer(in.NodeTaintsPolicy))
	out.MatchLabelKeys = *(*[]string)(unsafe.Pointer(&in.MatchLabelKeys))
	return nil
}

// Convert_v1_TopologySpreadConstraint_To_core_TopologySpreadConstraint is an autogenerated conversion function.
func Convert_v1_TopologySpreadConstraint_To_core_TopologySpreadConstraint(in *corev1.TopologySpreadConstraint, out *core.TopologySpreadConstraint, s conversion.Scope) error {
	return autoConvert_v1_TopologySpreadConstraint_To_core_TopologySpreadConstraint(in, out, s)
}

func autoConvert_core_TopologySpreadConstraint_To_v1_TopologySpreadConstraint(in *core.TopologySpreadConstraint, out *corev1.TopologySpreadConstraint, s conversion.Scope) error {
	out.MaxSkew = in.MaxSkew
	out.TopologyKey = in.TopologyKey
	out.WhenUnsatisfiable = corev1.UnsatisfiableConstraintAction(in.WhenUnsatisfiable)
	out.LabelSelector = (*metav1.LabelSelector)(unsafe.Pointer(in.LabelSelector))
	out.MinDomains = (*int32)(unsafe.Pointer(in.MinDomains))
	out.NodeAffinityPolicy = (*corev1.NodeInclusionPolicy)(unsafe.Pointer(in.NodeAffinityPolicy))
	out.NodeTaintsPolicy = (*corev1.NodeInclusionPolicy)(unsafe.Pointer(in.NodeTaintsPolicy))
	out.MatchLabelKeys = *(*[]string)(unsafe.Pointer(&in.MatchLabelKeys))
	return nil
}

// Convert_core_TopologySpreadConstraint_To_v1_TopologySpreadConstraint is an autogenerated conversion function.
func Convert_core_TopologySpreadConstraint_To_v1_TopologySpreadConstraint(in *core.TopologySpreadConstraint, out *corev1.TopologySpreadConstraint, s conversion.Scope) error {
	return autoConvert_core_TopologySpreadConstraint_To_v1_TopologySpreadConstraint(in, out, s)
}

func autoConvert_v1_TypedLocalObjectReference_To_core_TypedLocalObjectReference(in *corev1.TypedLocalObjectReference, out *core.TypedLocalObjectReference, s conversion.Scope) error {
	out.APIGroup = (*string)(unsafe.Pointer(in.APIGroup))
	out.Kind = in.Kind
	out.Name = in.Name
	return nil
}

// Convert_v1_TypedLocalObjectReference_To_core_TypedLocalObjectReference is an autogenerated conversion function.
func Convert_v1_TypedLocalObjectReference_To_core_TypedLocalObjectReference(in *corev1.TypedLocalObjectReference, out *core.TypedLocalObjectReference, s conversion.Scope) error {
	return autoConvert_v1_TypedLocalObjectReference_To_core_TypedLocalObjectReference(in, out, s)
}

func autoConvert_core_TypedLocalObjectReference_To_v1_TypedLocalObjectReference(in *core.TypedLocalObjectReference, out *corev1.TypedLocalObjectReference, s conversion.Scope) error {
	out.APIGroup = (*string)(unsafe.Pointer(in.APIGroup))
	out.Kind = in.Kind
	out.Name = in.Name
	return nil
}

// Convert_core_TypedLocalObjectReference_To_v1_TypedLocalObjectReference is an autogenerated conversion function.
func Convert_core_TypedLocalObjectReference_To_v1_TypedLocalObjectReference(in *core.TypedLocalObjectReference, out *corev1.TypedLocalObjectReference, s conversion.Scope) error {
	return autoConvert_core_TypedLocalObjectReference_To_v1_TypedLocalObjectReference(in, out, s)
}

func autoConvert_v1_TypedObjectReference_To_core_TypedObjectReference(in *corev1.TypedObjectReference, out *core.TypedObjectReference, s conversion.Scope) error {
	out.APIGroup = (*string)(unsafe.Pointer(in.APIGroup))
	out.Kind = in.Kind
	out.Name = in.Name
	out.Namespace = (*string)(unsafe.Pointer(in.Namespace))
	return nil
}

// Convert_v1_TypedObjectReference_To_core_TypedObjectReference is an autogenerated conversion function.
func Convert_v1_TypedObjectReference_To_core_TypedObjectReference(in *corev1.TypedObjectReference, out *core.TypedObjectReference, s conversion.Scope) error {
	return autoConvert_v1_TypedObjectReference_To_core_TypedObjectReference(in, out, s)
}

func autoConvert_core_TypedObjectReference_To_v1_TypedObjectReference(in *core.TypedObjectReference, out *corev1.TypedObjectReference, s conversion.Scope) error {
	out.APIGroup = (*string)(unsafe.Pointer(in.APIGroup))
	out.Kind = in.Kind
	out.Name = in.Name
	out.Namespace = (*string)(unsafe.Pointer(in.Namespace))
	return nil
}

// Convert_core_TypedObjectReference_To_v1_TypedObjectReference is an autogenerated conversion function.
func Convert_core_TypedObjectReference_To_v1_TypedObjectReference(in *core.TypedObjectReference, out *corev1.TypedObjectReference, s conversion.Scope) error {
	return autoConvert_core_TypedObjectReference_To_v1_TypedObjectReference(in, out, s)
}

func autoConvert_v1_Volume_To_core_Volume(in *corev1.Volume, out *core.Volume, s conversion.Scope) error {
	out.Name = in.Name
	if err := Convert_v1_VolumeSource_To_core_VolumeSource(&in.VolumeSource, &out.VolumeSource, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Volume_To_core_Volume is an autogenerated conversion function.
func Convert_v1_Volume_To_core_Volume(in *corev1.Volume, out *core.Volume, s conversion.Scope) error {
	return autoConvert_v1_Volume_To_core_Volume(in, out, s)
}

func autoConvert_core_Volume_To_v1_Volume(in *core.Volume, out *corev1.Volume, s conversion.Scope) error {
	out.Name = in.Name
	if err := Convert_core_VolumeSource_To_v1_VolumeSource(&in.VolumeSource, &out.VolumeSource, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_Volume_To_v1_Volume is an autogenerated conversion function.
func Convert_core_Volume_To_v1_Volume(in *core.Volume, out *corev1.Volume, s conversion.Scope) error {
	return autoConvert_core_Volume_To_v1_Volume(in, out, s)
}

func autoConvert_v1_VolumeDevice_To_core_VolumeDevice(in *corev1.VolumeDevice, out *core.VolumeDevice, s conversion.Scope) error {
	out.Name = in.Name
	out.DevicePath = in.DevicePath
	return nil
}

// Convert_v1_VolumeDevice_To_core_VolumeDevice is an autogenerated conversion function.
func Convert_v1_VolumeDevice_To_core_VolumeDevice(in *corev1.VolumeDevice, out *core.VolumeDevice, s conversion.Scope) error {
	return autoConvert_v1_VolumeDevice_To_core_VolumeDevice(in, out, s)
}

func autoConvert_core_VolumeDevice_To_v1_VolumeDevice(in *core.VolumeDevice, out *corev1.VolumeDevice, s conversion.Scope) error {
	out.Name = in.Name
	out.DevicePath = in.DevicePath
	return nil
}

// Convert_core_VolumeDevice_To_v1_VolumeDevice is an autogenerated conversion function.
func Convert_core_VolumeDevice_To_v1_VolumeDevice(in *core.VolumeDevice, out *corev1.VolumeDevice, s conversion.Scope) error {
	return autoConvert_core_VolumeDevice_To_v1_VolumeDevice(in, out, s)
}

func autoConvert_v1_VolumeMount_To_core_VolumeMount(in *corev1.VolumeMount, out *core.VolumeMount, s conversion.Scope) error {
	out.Name = in.Name
	out.ReadOnly = in.ReadOnly
	out.RecursiveReadOnly = (*core.RecursiveReadOnlyMode)(unsafe.Pointer(in.RecursiveReadOnly))
	out.MountPath = in.MountPath
	out.SubPath = in.SubPath
	out.MountPropagation = (*core.MountPropagationMode)(unsafe.Pointer(in.MountPropagation))
	out.SubPathExpr = in.SubPathExpr
	return nil
}

// Convert_v1_VolumeMount_To_core_VolumeMount is an autogenerated conversion function.
func Convert_v1_VolumeMount_To_core_VolumeMount(in *corev1.VolumeMount, out *core.VolumeMount, s conversion.Scope) error {
	return autoConvert_v1_VolumeMount_To_core_VolumeMount(in, out, s)
}

func autoConvert_core_VolumeMount_To_v1_VolumeMount(in *core.VolumeMount, out *corev1.VolumeMount, s conversion.Scope) error {
	out.Name = in.Name
	out.ReadOnly = in.ReadOnly
	out.RecursiveReadOnly = (*corev1.RecursiveReadOnlyMode)(unsafe.Pointer(in.RecursiveReadOnly))
	out.MountPath = in.MountPath
	out.SubPath = in.SubPath
	out.MountPropagation = (*corev1.MountPropagationMode)(unsafe.Pointer(in.MountPropagation))
	out.SubPathExpr = in.SubPathExpr
	return nil
}

// Convert_core_VolumeMount_To_v1_VolumeMount is an autogenerated conversion function.
func Convert_core_VolumeMount_To_v1_VolumeMount(in *core.VolumeMount, out *corev1.VolumeMount, s conversion.Scope) error {
	return autoConvert_core_VolumeMount_To_v1_VolumeMount(in, out, s)
}

func autoConvert_v1_VolumeMountStatus_To_core_VolumeMountStatus(in *corev1.VolumeMountStatus, out *core.VolumeMountStatus, s conversion.Scope) error {
	out.Name = in.Name
	out.MountPath = in.MountPath
	out.ReadOnly = in.ReadOnly
	out.RecursiveReadOnly = (*core.RecursiveReadOnlyMode)(unsafe.Pointer(in.RecursiveReadOnly))
	return nil
}

// Convert_v1_VolumeMountStatus_To_core_VolumeMountStatus is an autogenerated conversion function.
func Convert_v1_VolumeMountStatus_To_core_VolumeMountStatus(in *corev1.VolumeMountStatus, out *core.VolumeMountStatus, s conversion.Scope) error {
	return autoConvert_v1_VolumeMountStatus_To_core_VolumeMountStatus(in, out, s)
}

func autoConvert_core_VolumeMountStatus_To_v1_VolumeMountStatus(in *core.VolumeMountStatus, out *corev1.VolumeMountStatus, s conversion.Scope) error {
	out.Name = in.Name
	out.MountPath = in.MountPath
	out.ReadOnly = in.ReadOnly
	out.RecursiveReadOnly = (*corev1.RecursiveReadOnlyMode)(unsafe.Pointer(in.RecursiveReadOnly))
	return nil
}

// Convert_core_VolumeMountStatus_To_v1_VolumeMountStatus is an autogenerated conversion function.
func Convert_core_VolumeMountStatus_To_v1_VolumeMountStatus(in *core.VolumeMountStatus, out *corev1.VolumeMountStatus, s conversion.Scope) error {
	return autoConvert_core_VolumeMountStatus_To_v1_VolumeMountStatus(in, out, s)
}

func autoConvert_v1_VolumeNodeAffinity_To_core_VolumeNodeAffinity(in *corev1.VolumeNodeAffinity, out *core.VolumeNodeAffinity, s conversion.Scope) error {
	out.Required = (*core.NodeSelector)(unsafe.Pointer(in.Required))
	return nil
}

// Convert_v1_VolumeNodeAffinity_To_core_VolumeNodeAffinity is an autogenerated conversion function.
func Convert_v1_VolumeNodeAffinity_To_core_VolumeNodeAffinity(in *corev1.VolumeNodeAffinity, out *core.VolumeNodeAffinity, s conversion.Scope) error {
	return autoConvert_v1_VolumeNodeAffinity_To_core_VolumeNodeAffinity(in, out, s)
}

func autoConvert_core_VolumeNodeAffinity_To_v1_VolumeNodeAffinity(in *core.VolumeNodeAffinity, out *corev1.VolumeNodeAffinity, s conversion.Scope) error {
	out.Required = (*corev1.NodeSelector)(unsafe.Pointer(in.Required))
	return nil
}

// Convert_core_VolumeNodeAffinity_To_v1_VolumeNodeAffinity is an autogenerated conversion function.
func Convert_core_VolumeNodeAffinity_To_v1_VolumeNodeAffinity(in *core.VolumeNodeAffinity, out *corev1.VolumeNodeAffinity, s conversion.Scope) error {
	return autoConvert_core_VolumeNodeAffinity_To_v1_VolumeNodeAffinity(in, out, s)
}

func autoConvert_v1_VolumeProjection_To_core_VolumeProjection(in *corev1.VolumeProjection, out *core.VolumeProjection, s conversion.Scope) error {
	out.Secret = (*core.SecretProjection)(unsafe.Pointer(in.Secret))
	out.DownwardAPI = (*core.DownwardAPIProjection)(unsafe.Pointer(in.DownwardAPI))
	out.ConfigMap = (*core.ConfigMapProjection)(unsafe.Pointer(in.ConfigMap))
	if in.ServiceAccountToken != nil {
		in, out := &in.ServiceAccountToken, &out.ServiceAccountToken
		*out = new(core.ServiceAccountTokenProjection)
		if err := Convert_v1_ServiceAccountTokenProjection_To_core_ServiceAccountTokenProjection(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.ServiceAccountToken = nil
	}
	out.ClusterTrustBundle = (*core.ClusterTrustBundleProjection)(unsafe.Pointer(in.ClusterTrustBundle))
	out.PodCertificate = (*core.PodCertificateProjection)(unsafe.Pointer(in.PodCertificate))
	return nil
}

// Convert_v1_VolumeProjection_To_core_VolumeProjection is an autogenerated conversion function.
func Convert_v1_VolumeProjection_To_core_VolumeProjection(in *corev1.VolumeProjection, out *core.VolumeProjection, s conversion.Scope) error {
	return autoConvert_v1_VolumeProjection_To_core_VolumeProjection(in, out, s)
}

func autoConvert_core_VolumeProjection_To_v1_VolumeProjection(in *core.VolumeProjection, out *corev1.VolumeProjection, s conversion.Scope) error {
	out.Secret = (*corev1.SecretProjection)(unsafe.Pointer(in.Secret))
	out.DownwardAPI = (*corev1.DownwardAPIProjection)(unsafe.Pointer(in.DownwardAPI))
	out.ConfigMap = (*corev1.ConfigMapProjection)(unsafe.Pointer(in.ConfigMap))
	if in.ServiceAccountToken != nil {
		in, out := &in.ServiceAccountToken, &out.ServiceAccountToken
		*out = new(corev1.ServiceAccountTokenProjection)
		if err := Convert_core_ServiceAccountTokenProjection_To_v1_ServiceAccountTokenProjection(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.ServiceAccountToken = nil
	}
	out.ClusterTrustBundle = (*corev1.ClusterTrustBundleProjection)(unsafe.Pointer(in.ClusterTrustBundle))
	out.PodCertificate = (*corev1.PodCertificateProjection)(unsafe.Pointer(in.PodCertificate))
	return nil
}

// Convert_core_VolumeProjection_To_v1_VolumeProjection is an autogenerated conversion function.
func Convert_core_VolumeProjection_To_v1_VolumeProjection(in *core.VolumeProjection, out *corev1.VolumeProjection, s conversion.Scope) error {
	return autoConvert_core_VolumeProjection_To_v1_VolumeProjection(in, out, s)
}

func autoConvert_v1_VolumeResourceRequirements_To_core_VolumeResourceRequirements(in *corev1.VolumeResourceRequirements, out *core.VolumeResourceRequirements, s conversion.Scope) error {
	out.Limits = *(*core.ResourceList)(unsafe.Pointer(&in.Limits))
	out.Requests = *(*core.ResourceList)(unsafe.Pointer(&in.Requests))
	return nil
}

// Convert_v1_VolumeResourceRequirements_To_core_VolumeResourceRequirements is an autogenerated conversion function.
func Convert_v1_VolumeResourceRequirements_To_core_VolumeResourceRequirements(in *corev1.VolumeResourceRequirements, out *core.VolumeResourceRequirements, s conversion.Scope) error {
	return autoConvert_v1_VolumeResourceRequirements_To_core_VolumeResourceRequirements(in, out, s)
}

func autoConvert_core_VolumeResourceRequirements_To_v1_VolumeResourceRequirements(in *core.VolumeResourceRequirements, out *corev1.VolumeResourceRequirements, s conversion.Scope) error {
	out.Limits = *(*corev1.ResourceList)(unsafe.Pointer(&in.Limits))
	out.Requests = *(*corev1.ResourceList)(unsafe.Pointer(&in.Requests))
	return nil
}

// Convert_core_VolumeResourceRequirements_To_v1_VolumeResourceRequirements is an autogenerated conversion function.
func Convert_core_VolumeResourceRequirements_To_v1_VolumeResourceRequirements(in *core.VolumeResourceRequirements, out *corev1.VolumeResourceRequirements, s conversion.Scope) error {
	return autoConvert_core_VolumeResourceRequirements_To_v1_VolumeResourceRequirements(in, out, s)
}

func autoConvert_v1_VolumeSource_To_core_VolumeSource(in *corev1.VolumeSource, out *core.VolumeSource, s conversion.Scope) error {
	out.HostPath = (*core.HostPathVolumeSource)(unsafe.Pointer(in.HostPath))
	out.EmptyDir = (*core.EmptyDirVolumeSource)(unsafe.Pointer(in.EmptyDir))
	out.GCEPersistentDisk = (*core.GCEPersistentDiskVolumeSource)(unsafe.Pointer(in.GCEPersistentDisk))
	out.AWSElasticBlockStore = (*core.AWSElasticBlockStoreVolumeSource)(unsafe.Pointer(in.AWSElasticBlockStore))
	out.GitRepo = (*core.GitRepoVolumeSource)(unsafe.Pointer(in.GitRepo))
	out.Secret = (*core.SecretVolumeSource)(unsafe.Pointer(in.Secret))
	out.NFS = (*core.NFSVolumeSource)(unsafe.Pointer(in.NFS))
	out.ISCSI = (*core.ISCSIVolumeSource)(unsafe.Pointer(in.ISCSI))
	out.Glusterfs = (*core.GlusterfsVolumeSource)(unsafe.Pointer(in.Glusterfs))
	out.PersistentVolumeClaim = (*core.PersistentVolumeClaimVolumeSource)(unsafe.Pointer(in.PersistentVolumeClaim))
	out.RBD = (*core.RBDVolumeSource)(unsafe.Pointer(in.RBD))
	out.FlexVolume = (*core.FlexVolumeSource)(unsafe.Pointer(in.FlexVolume))
	out.Cinder = (*core.CinderVolumeSource)(unsafe.Pointer(in.Cinder))
	out.CephFS = (*core.CephFSVolumeSource)(unsafe.Pointer(in.CephFS))
	out.Flocker = (*core.FlockerVolumeSource)(unsafe.Pointer(in.Flocker))
	out.DownwardAPI = (*core.DownwardAPIVolumeSource)(unsafe.Pointer(in.DownwardAPI))
	out.FC = (*core.FCVolumeSource)(unsafe.Pointer(in.FC))
	out.AzureFile = (*core.AzureFileVolumeSource)(unsafe.Pointer(in.AzureFile))
	out.ConfigMap = (*core.ConfigMapVolumeSource)(unsafe.Pointer(in.ConfigMap))
	out.VsphereVolume = (*core.VsphereVirtualDiskVolumeSource)(unsafe.Pointer(in.VsphereVolume))
	out.Quobyte = (*core.QuobyteVolumeSource)(unsafe.Pointer(in.Quobyte))
	out.AzureDisk = (*core.AzureDiskVolumeSource)(unsafe.Pointer(in.AzureDisk))
	out.PhotonPersistentDisk = (*core.PhotonPersistentDiskVolumeSource)(unsafe.Pointer(in.PhotonPersistentDisk))
	if in.Projected != nil {
		in, out := &in.Projected, &out.Projected
		*out = new(core.ProjectedVolumeSource)
		if err := Convert_v1_ProjectedVolumeSource_To_core_ProjectedVolumeSource(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.Projected = nil
	}
	out.PortworxVolume = (*core.PortworxVolumeSource)(unsafe.Pointer(in.PortworxVolume))
	out.ScaleIO = (*core.ScaleIOVolumeSource)(unsafe.Pointer(in.ScaleIO))
	out.StorageOS = (*core.StorageOSVolumeSource)(unsafe.Pointer(in.StorageOS))
	out.CSI = (*core.CSIVolumeSource)(unsafe.Pointer(in.CSI))
	out.Ephemeral = (*core.EphemeralVolumeSource)(unsafe.Pointer(in.Ephemeral))
	out.Image = (*core.ImageVolumeSource)(unsafe.Pointer(in.Image))
	return nil
}

// Convert_v1_VolumeSource_To_core_VolumeSource is an autogenerated conversion function.
func Convert_v1_VolumeSource_To_core_VolumeSource(in *corev1.VolumeSource, out *core.VolumeSource, s conversion.Scope) error {
	return autoConvert_v1_VolumeSource_To_core_VolumeSource(in, out, s)
}

func autoConvert_core_VolumeSource_To_v1_VolumeSource(in *core.VolumeSource, out *corev1.VolumeSource, s conversion.Scope) error {
	out.HostPath = (*corev1.HostPathVolumeSource)(unsafe.Pointer(in.HostPath))
	out.EmptyDir = (*corev1.EmptyDirVolumeSource)(unsafe.Pointer(in.EmptyDir))
	out.GCEPersistentDisk = (*corev1.GCEPersistentDiskVolumeSource)(unsafe.Pointer(in.GCEPersistentDisk))
	out.AWSElasticBlockStore = (*corev1.AWSElasticBlockStoreVolumeSource)(unsafe.Pointer(in.AWSElasticBlockStore))
	out.GitRepo = (*corev1.GitRepoVolumeSource)(unsafe.Pointer(in.GitRepo))
	out.Secret = (*corev1.SecretVolumeSource)(unsafe.Pointer(in.Secret))
	out.NFS = (*corev1.NFSVolumeSource)(unsafe.Pointer(in.NFS))
	out.ISCSI = (*corev1.ISCSIVolumeSource)(unsafe.Pointer(in.ISCSI))
	out.Glusterfs = (*corev1.GlusterfsVolumeSource)(unsafe.Pointer(in.Glusterfs))
	out.PersistentVolumeClaim = (*corev1.PersistentVolumeClaimVolumeSource)(unsafe.Pointer(in.PersistentVolumeClaim))
	out.RBD = (*corev1.RBDVolumeSource)(unsafe.Pointer(in.RBD))
	out.Quobyte = (*corev1.QuobyteVolumeSource)(unsafe.Pointer(in.Quobyte))
	out.FlexVolume = (*corev1.FlexVolumeSource)(unsafe.Pointer(in.FlexVolume))
	out.Cinder = (*corev1.CinderVolumeSource)(unsafe.Pointer(in.Cinder))
	out.CephFS = (*corev1.CephFSVolumeSource)(unsafe.Pointer(in.CephFS))
	out.Flocker = (*corev1.FlockerVolumeSource)(unsafe.Pointer(in.Flocker))
	out.DownwardAPI = (*corev1.DownwardAPIVolumeSource)(unsafe.Pointer(in.DownwardAPI))
	out.FC = (*corev1.FCVolumeSource)(unsafe.Pointer(in.FC))
	out.AzureFile = (*corev1.AzureFileVolumeSource)(unsafe.Pointer(in.AzureFile))
	out.ConfigMap = (*corev1.ConfigMapVolumeSource)(unsafe.Pointer(in.ConfigMap))
	out.VsphereVolume = (*corev1.VsphereVirtualDiskVolumeSource)(unsafe.Pointer(in.VsphereVolume))
	out.AzureDisk = (*corev1.AzureDiskVolumeSource)(unsafe.Pointer(in.AzureDisk))
	out.PhotonPersistentDisk = (*corev1.PhotonPersistentDiskVolumeSource)(unsafe.Pointer(in.PhotonPersistentDisk))
	if in.Projected != nil {
		in, out := &in.Projected, &out.Projected
		*out = new(corev1.ProjectedVolumeSource)
		if err := Convert_core_ProjectedVolumeSource_To_v1_ProjectedVolumeSource(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.Projected = nil
	}
	out.PortworxVolume = (*corev1.PortworxVolumeSource)(unsafe.Pointer(in.PortworxVolume))
	out.ScaleIO = (*corev1.ScaleIOVolumeSource)(unsafe.Pointer(in.ScaleIO))
	out.StorageOS = (*corev1.StorageOSVolumeSource)(unsafe.Pointer(in.StorageOS))
	out.CSI = (*corev1.CSIVolumeSource)(unsafe.Pointer(in.CSI))
	out.Ephemeral = (*corev1.EphemeralVolumeSource)(unsafe.Pointer(in.Ephemeral))
	out.Image = (*corev1.ImageVolumeSource)(unsafe.Pointer(in.Image))
	return nil
}

// Convert_core_VolumeSource_To_v1_VolumeSource is an autogenerated conversion function.
func Convert_core_VolumeSource_To_v1_VolumeSource(in *core.VolumeSource, out *corev1.VolumeSource, s conversion.Scope) error {
	return autoConvert_core_VolumeSource_To_v1_VolumeSource(in, out, s)
}

func autoConvert_v1_VsphereVirtualDiskVolumeSource_To_core_VsphereVirtualDiskVolumeSource(in *corev1.VsphereVirtualDiskVolumeSource, out *core.VsphereVirtualDiskVolumeSource, s conversion.Scope) error {
	out.VolumePath = in.VolumePath
	out.FSType = in.FSType
	out.StoragePolicyName = in.StoragePolicyName
	out.StoragePolicyID = in.StoragePolicyID
	return nil
}

// Convert_v1_VsphereVirtualDiskVolumeSource_To_core_VsphereVirtualDiskVolumeSource is an autogenerated conversion function.
func Convert_v1_VsphereVirtualDiskVolumeSource_To_core_VsphereVirtualDiskVolumeSource(in *corev1.VsphereVirtualDiskVolumeSource, out *core.VsphereVirtualDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_v1_VsphereVirtualDiskVolumeSource_To_core_VsphereVirtualDiskVolumeSource(in, out, s)
}

func autoConvert_core_VsphereVirtualDiskVolumeSource_To_v1_VsphereVirtualDiskVolumeSource(in *core.VsphereVirtualDiskVolumeSource, out *corev1.VsphereVirtualDiskVolumeSource, s conversion.Scope) error {
	out.VolumePath = in.VolumePath
	out.FSType = in.FSType
	out.StoragePolicyName = in.StoragePolicyName
	out.StoragePolicyID = in.StoragePolicyID
	return nil
}

// Convert_core_VsphereVirtualDiskVolumeSource_To_v1_VsphereVirtualDiskVolumeSource is an autogenerated conversion function.
func Convert_core_VsphereVirtualDiskVolumeSource_To_v1_VsphereVirtualDiskVolumeSource(in *core.VsphereVirtualDiskVolumeSource, out *corev1.VsphereVirtualDiskVolumeSource, s conversion.Scope) error {
	return autoConvert_core_VsphereVirtualDiskVolumeSource_To_v1_VsphereVirtualDiskVolumeSource(in, out, s)
}

func autoConvert_v1_WeightedPodAffinityTerm_To_core_WeightedPodAffinityTerm(in *corev1.WeightedPodAffinityTerm, out *core.WeightedPodAffinityTerm, s conversion.Scope) error {
	out.Weight = in.Weight
	if err := Convert_v1_PodAffinityTerm_To_core_PodAffinityTerm(&in.PodAffinityTerm, &out.PodAffinityTerm, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_WeightedPodAffinityTerm_To_core_WeightedPodAffinityTerm is an autogenerated conversion function.
func Convert_v1_WeightedPodAffinityTerm_To_core_WeightedPodAffinityTerm(in *corev1.WeightedPodAffinityTerm, out *core.WeightedPodAffinityTerm, s conversion.Scope) error {
	return autoConvert_v1_WeightedPodAffinityTerm_To_core_WeightedPodAffinityTerm(in, out, s)
}

func autoConvert_core_WeightedPodAffinityTerm_To_v1_WeightedPodAffinityTerm(in *core.WeightedPodAffinityTerm, out *corev1.WeightedPodAffinityTerm, s conversion.Scope) error {
	out.Weight = in.Weight
	if err := Convert_core_PodAffinityTerm_To_v1_PodAffinityTerm(&in.PodAffinityTerm, &out.PodAffinityTerm, s); err != nil {
		return err
	}
	return nil
}

// Convert_core_WeightedPodAffinityTerm_To_v1_WeightedPodAffinityTerm is an autogenerated conversion function.
func Convert_core_WeightedPodAffinityTerm_To_v1_WeightedPodAffinityTerm(in *core.WeightedPodAffinityTerm, out *corev1.WeightedPodAffinityTerm, s conversion.Scope) error {
	return autoConvert_core_WeightedPodAffinityTerm_To_v1_WeightedPodAffinityTerm(in, out, s)
}

func autoConvert_v1_WindowsSecurityContextOptions_To_core_WindowsSecurityContextOptions(in *corev1.WindowsSecurityContextOptions, out *core.WindowsSecurityContextOptions, s conversion.Scope) error {
	out.GMSACredentialSpecName = (*string)(unsafe.Pointer(in.GMSACredentialSpecName))
	out.GMSACredentialSpec = (*string)(unsafe.Pointer(in.GMSACredentialSpec))
	out.RunAsUserName = (*string)(unsafe.Pointer(in.RunAsUserName))
	out.HostProcess = (*bool)(unsafe.Pointer(in.HostProcess))
	return nil
}

// Convert_v1_WindowsSecurityContextOptions_To_core_WindowsSecurityContextOptions is an autogenerated conversion function.
func Convert_v1_WindowsSecurityContextOptions_To_core_WindowsSecurityContextOptions(in *corev1.WindowsSecurityContextOptions, out *core.WindowsSecurityContextOptions, s conversion.Scope) error {
	return autoConvert_v1_WindowsSecurityContextOptions_To_core_WindowsSecurityContextOptions(in, out, s)
}

func autoConvert_core_WindowsSecurityContextOptions_To_v1_WindowsSecurityContextOptions(in *core.WindowsSecurityContextOptions, out *corev1.WindowsSecurityContextOptions, s conversion.Scope) error {
	out.GMSACredentialSpecName = (*string)(unsafe.Pointer(in.GMSACredentialSpecName))
	out.GMSACredentialSpec = (*string)(unsafe.Pointer(in.GMSACredentialSpec))
	out.RunAsUserName = (*string)(unsafe.Pointer(in.RunAsUserName))
	out.HostProcess = (*bool)(unsafe.Pointer(in.HostProcess))
	return nil
}

// Convert_core_WindowsSecurityContextOptions_To_v1_WindowsSecurityContextOptions is an autogenerated conversion function.
func Convert_core_WindowsSecurityContextOptions_To_v1_WindowsSecurityContextOptions(in *core.WindowsSecurityContextOptions, out *corev1.WindowsSecurityContextOptions, s conversion.Scope) error {
	return autoConvert_core_WindowsSecurityContextOptions_To_v1_WindowsSecurityContextOptions(in, out, s)
}
