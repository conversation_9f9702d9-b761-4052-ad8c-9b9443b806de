# This file is generated by compatibility_lifecycle tool.
# Do not edit manually. Run hack/update-featuregates.sh to regenerate.

- name: AggregatedDiscoveryRemoveBetaType
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: true
    lockToDefault: false
    preRelease: Deprecated
    version: "1.33"
- name: AllowDNSOnlyNodeCSR
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.31"
- name: AllowInsecureKubeletCertificateSigningRequests
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.31"
- name: AllowOverwriteTerminationGracePeriodSeconds
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.32"
- name: AllowParsingUserUIDFromCertAuth
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: AllowServiceLBStatusOnNonLB
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.29"
  - default: false
    lockToDefault: true
    preRelease: Deprecated
    version: "1.32"
- name: AllowUnsafeMalformedObjectDeletion
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: AnonymousAuthConfigurableEndpoints
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: AnyVolumeDataSource
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.18"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.24"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: APIResponseCompression
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.8"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.16"
- name: APIServerIdentity
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.20"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.26"
- name: APIServerTracing
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.22"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: APIServingWithRoutine
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
- name: AuthorizeNodeWithSelectors
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: AuthorizeWithSelectors
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: BtreeWatchCache
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: CBORServingAndStorage
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: CloudControllerManagerWebhook
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
- name: ClusterTrustBundle
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: ClusterTrustBundleProjection
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: ComponentFlagz
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: ComponentSLIs
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: ComponentStatusz
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: ConcurrentWatchObjectDecode
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
- name: ConsistentListFromCache
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: ContainerCheckpoint
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.25"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
- name: ContainerStopSignals
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: ContextualLogging
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.24"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
- name: CoordinatedLeaderElection
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: CPUCFSQuotaPeriod
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.12"
- name: CPUManagerPolicyAlphaOptions
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
- name: CPUManagerPolicyBetaOptions
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.23"
- name: CPUManagerPolicyOptions
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.22"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.23"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: CRDValidationRatcheting
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: CronJobsScheduledAnnotation
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: CrossNamespaceVolumeDataSource
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
- name: CSIMigrationPortworx
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.25"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: CSIVolumeHealth
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.21"
- name: CustomResourceFieldSelectors
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: DeclarativeValidation
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: DeclarativeValidationTakeover
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: DeploymentReplicaSetTerminatingReplicas
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: DetectCacheInconsistency
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: DisableAllocatorDualWrite
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.34"
- name: DisableCPUQuotaWithExclusiveCPUs
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: DisableNodeKubeProxyVersion
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.31"
  - default: true
    lockToDefault: false
    preRelease: Deprecated
    version: "1.33"
- name: DRAAdminAccess
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: DRADeviceTaints
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: DRAPartitionableDevices
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: DRAPrioritizedList
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: DRAResourceClaimDeviceStatus
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: DRASchedulerFilterTimeout
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: DynamicResourceAllocation
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.34"
- name: EnvFiles
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.34"
- name: EventedPLEG
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
- name: ExecProbeTimeout
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.20"
- name: ExternalServiceAccountTokenSigner
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: GitRepoVolumeDriver
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.33"
- name: GracefulNodeShutdown
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.20"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.21"
- name: GracefulNodeShutdownBasedOnPodPriority
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.24"
- name: HonorPVReclaimPolicy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: HostnameOverride
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.34"
- name: HPAConfigurableTolerance
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: HPAScaleToZero
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.16"
- name: ImageMaximumGCAge
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
- name: ImageVolume
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: InPlacePodVerticalScaling
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: InPlacePodVerticalScalingAllocatedStatus
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.33"
- name: InPlacePodVerticalScalingExclusiveCPUs
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: InPlacePodVerticalScalingExclusiveMemory
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.34"
- name: InTreePluginPortworxUnregister
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
- name: JobBackoffLimitPerIndex
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.29"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: JobManagedBy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
- name: JobPodReplacementPolicy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.29"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: JobSuccessPolicy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: KMSv1
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: true
    lockToDefault: false
    preRelease: Deprecated
    version: "1.28"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.29"
- name: KubeletCgroupDriverFromCRI
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: KubeletCrashLoopBackOffMax
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: KubeletEnsureSecretPulledImages
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: KubeletFineGrainedAuthz
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: KubeletInUserNamespace
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.22"
- name: KubeletPodResourcesDynamicResources
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
- name: KubeletPodResourcesGet
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
- name: KubeletPodResourcesListUseActivePods
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: true
    lockToDefault: false
    preRelease: Deprecated
    version: "1.34"
- name: KubeletPSI
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: KubeletRegistrationGetOnExistsOnly
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.0"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.32"
- name: KubeletSeparateDiskGC
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
- name: KubeletServiceAccountTokenForCredentialProviders
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: KubeletTracing
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.25"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: ListFromCacheSnapshot
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: LoadBalancerIPMode
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: LocalStorageCapacityIsolationFSQuotaMonitoring
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.15"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
- name: LogarithmicScaleDown
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.21"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.22"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.31"
- name: LoggingAlphaOptions
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.24"
- name: LoggingBetaOptions
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.24"
- name: MatchLabelKeysInPodAffinity
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: MatchLabelKeysInPodTopologySpread
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.25"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
- name: MatchLabelKeysInPodTopologySpreadSelectorMerge
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: MaxUnavailableStatefulSet
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.24"
- name: MemoryManager
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.21"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.22"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: MemoryQoS
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.22"
- name: MultiCIDRServiceAllocator
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.33"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: MutableCSINodeAllocatableCount
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: MutatingAdmissionPolicy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: NFTablesProxyMode
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: NodeInclusionPolicyInPodTopologySpread
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.25"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.26"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: NodeLogQuery
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
- name: NodeSwap
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.22"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: NominatedNodeNameForExpectation
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: OpenAPIEnums
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.24"
- name: OrderedNamespaceDeletion
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: PodAndContainerStatsFromCRI
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
- name: PodCertificateRequest
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.34"
- name: PodDeletionCost
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.21"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.22"
- name: PodIndexLabel
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: PodLevelResources
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: PodLifecycleSleepAction
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: PodLifecycleSleepActionAllowZero
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: PodLogsQuerySplitStreams
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: PodObservedGenerationTracking
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: PodReadyToStartContainersCondition
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.29"
- name: PodSchedulingReadiness
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.30"
- name: PodTopologyLabelsAdmission
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: PortForwardWebsockets
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
- name: PreferSameTrafficDistribution
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: PreventStaticPodAPIReferences
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: ProcMountType
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.12"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: QOSReserved
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.11"
- name: RecoverVolumeExpansionFailure
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: RecursiveReadOnlyMounts
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: ReduceDefaultCrashLoopBackOffDecay
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: RelaxedDNSSearchValidation
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: RelaxedEnvironmentVariableValidation
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: RelaxedServiceNameValidation
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.34"
- name: ReloadKubeletServerCertificateFile
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
- name: RemoteRequestHeaderUID
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: ResilientWatchCacheInitialization
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: ResourceHealthStatus
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
- name: RetryGenerateName
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: RotateKubeletServerCertificate
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.7"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.12"
- name: RuntimeClassInImageCriAPI
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
- name: SchedulerAsyncAPICalls
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: SchedulerAsyncPreemption
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: SchedulerPopFromBackoffQ
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: SchedulerQueueingHints
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: SELinuxChangePolicy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: SELinuxMount
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: SELinuxMountReadWriteOncePod
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.25"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
- name: SeparateCacheWatchRPC
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.33"
- name: SeparateTaintEvictionController
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.29"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: ServiceAccountNodeAudienceRestriction
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: ServiceAccountTokenJTI
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: ServiceAccountTokenNodeBinding
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: ServiceAccountTokenNodeBindingValidation
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: ServiceAccountTokenPodNodeInfo
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: ServiceTrafficDistribution
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: SidecarContainers
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.29"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: SizeBasedListCostEstimate
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: SizeMemoryBackedVolumes
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.20"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.22"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: StatefulSetAutoDeletePVC
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.23"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: StorageCapacityScoring
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: StorageNamespaceIndex
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Deprecated
    version: "1.33"
- name: StorageVersionAPI
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.20"
- name: StorageVersionHash
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.14"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.15"
- name: StorageVersionMigrator
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.30"
- name: StreamingCollectionEncodingToJSON
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: StreamingCollectionEncodingToProtobuf
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: StrictCostEnforcementForVAP
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: StrictCostEnforcementForWebhooks
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: StrictIPCIDRValidation
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.33"
- name: StructuredAuthenticationConfiguration
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: StructuredAuthenticationConfigurationEgressSelector
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: StructuredAuthorizationConfiguration
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.32"
- name: SupplementalGroupsPolicy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.31"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: SystemdWatchdog
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
- name: TokenRequestServiceAccountUIDValidation
  versionedSpecs:
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: TopologyAwareHints
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.21"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.23"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.24"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.33"
- name: TopologyManagerPolicyAlphaOptions
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
- name: TopologyManagerPolicyBetaOptions
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.26"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
- name: TopologyManagerPolicyOptions
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.28"
  - default: true
    lockToDefault: false
    preRelease: GA
    version: "1.32"
- name: TranslateStreamCloseWebsocketRequests
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
- name: UnauthenticatedHTTP2DOSMitigation
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.25"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.29"
- name: UnknownVersionInteroperabilityProxy
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.28"
- name: UserNamespacesPodSecurityStandards
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
- name: UserNamespacesSupport
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.25"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.30"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
- name: VolumeAttributesClass
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.29"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
- name: WatchCacheInitializationPostStartHook
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.31"
- name: WatchFromStorageWithoutResourceVersion
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.27"
  - default: false
    lockToDefault: true
    preRelease: Deprecated
    version: "1.33"
- name: WatchList
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.27"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.32"
  - default: false
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.34"
- name: WindowsCPUAndMemoryAffinity
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: WindowsGracefulNodeShutdown
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.32"
- name: WindowsHostNetwork
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.26"
  - default: false
    lockToDefault: false
    preRelease: Deprecated
    version: "1.33"
- name: WinDSR
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.14"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.33"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
- name: WinOverlay
  versionedSpecs:
  - default: false
    lockToDefault: false
    preRelease: Alpha
    version: "1.14"
  - default: true
    lockToDefault: false
    preRelease: Beta
    version: "1.20"
  - default: true
    lockToDefault: true
    preRelease: GA
    version: "1.34"
